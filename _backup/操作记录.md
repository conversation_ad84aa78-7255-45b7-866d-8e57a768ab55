/_dev/
/_dev/项目结构梳理.md
/_dev/架构特性.md
核心架构特性
微内核架构：核心包<15KB，插件化扩展
6种沙箱策略：Proxy、DefineProperty、iframe、WebComponent、Namespace、Federation
一行代码接入：await init({ autoStart: true })零配置启动
完全兼容：qiankun、wujie、micro-app API完全兼容
高性能：应用加载<500ms，内存占用<50MB
技术栈规范
构建：Vite 7.0.6 + TypeScript 5.7.x
测试：Vitest 3.2.4
文档：VitePress 2.0.0-alpha.8
包管理：pnpm + monorepo
版本：统一0.1.0，@micro-core组织
🚀 详尽开发任务规划
第一阶段：项目基础设施（1-2周）
任务1.1：Monorepo架构初始化
优先级：🔴 最高
实现步骤：

创建根目录结构和pnpm工作空间
配置TypeScript 5.7.x基础配置
设置Vite 7.0.6构建模板
配置Vitest 3.2.4测试环境
设置代码规范（ESLint + Prettier）
技术细节：

# pnpm-workspace.yaml
packages:
- 'packages/*'
- 'examples/*'
- 'docs'
任务1.2：构建工具链配置
实现步骤：

配置Vite基础构建配置
设置库构建和应用构建模式
配置Tree-shaking优化
设置开发服务器和热更新
第二阶段：核心运行时开发（3-4周）
任务2.1：微内核核心包（@micro-core/core）
优先级：🔴 最高
体积要求：严格控制在15KB以内

核心模块：

生命周期调度器：
export class LifecycleScheduler {
async bootstrap(app: MicroApp): Promise<void>
async mount(app: MicroApp): Promise<void>
async unmount(app: MicroApp): Promise<void>
async unload(app: MicroApp): Promise<void>
}
插件管理器：
export class PluginManager {
register(plugin: Plugin): void
async load(pluginName: string): Promise<Plugin>
getPlugin(name: string): Plugin | undefined
}
应用注册表：
export class ApplicationRegistry {
register(config: AppConfig): void
getApp(name: string): MicroApp | undefined
getAllApps(): MicroApp[]
}
任务2.2：共享基础设施包（@micro-core/shared）
9个核心工具模块：

DOM操作工具（dom.ts）
事件处理工具（event.ts）
URL处理工具（url.ts）
存储工具（storage.ts）
异步处理工具（async.ts）
验证工具（validation.ts）
性能监控工具（performance.ts）
安全工具（security.ts）
日志工具（logger.ts）
错误处理机制：

export class MicroCoreError extends Error {
code: string
context?: Record<string, any>
constructor(message: string, code: string, context?: Record<string, any>)
}
第三阶段：沙箱系统开发（5-6周）
任务3.1：6种沙箱策略实现（@micro-core/sandbox）
优先级：🟡 高

沙箱策略：

Proxy沙箱：性能最优
export class ProxySandbox implements SandboxStrategy {
private proxyWindow: WindowProxy
create(): SandboxContext
destroy(): void
isolate(code: string): any
}
DefineProperty沙箱：兼容性好
iframe沙箱：隔离性最强
WebComponent沙箱：现代化方案
Namespace沙箱：轻量级方案
Federation沙箱：模块联邦方案
任务3.2：沙箱管理器
沙箱工厂：

export class SandboxFactory {
createSandbox(strategy: SandboxType): SandboxStrategy
selectOptimalStrategy(env: Environment): SandboxType
}
多层隔离：

JavaScript隔离
CSS隔离
全局变量隔离
第四阶段：插件系统开发（7-8周）
任务4.1：插件基础设施（@micro-core/plugins）
插件基类：

export abstract class PluginBase {
abstract name: string
abstract version: string
abstract install(core: MicroCore): void
abstract uninstall(): void
}
任务4.2：核心插件开发
Router插件：路由管理
Communication插件：应用间通信
Auth插件：权限管理
DevTools插件：开发工具
Performance插件：性能监控
ErrorHandler插件：错误处理
第五阶段：应用间通信系统（9-10周）
任务5.1：事件总线系统（@micro-core/communication）
事件总线：

export class EventBus {
subscribe(event: string, handler: Function): void
unsubscribe(event: string, handler: Function): void
publish(event: string, data: any): void
}
消息通道：

SharedWorker通信
BroadcastChannel通信
PostMessage通信
第六阶段：框架适配器系统（11-12周）
任务6.1：基础适配器抽象（@micro-core/adapters）
适配器基类：

export abstract class AdapterBase {
abstract bootstrap(props: any): Promise<void>
abstract mount(container: Element): Promise<void>
abstract unmount(): Promise<void>
}
任务6.2：9种框架适配器
React适配器
Vue2适配器
Vue3适配器
Angular适配器
Svelte适配器
Solid适配器
Lit适配器
HTML适配器
原生JS适配器
第七阶段：构建工具适配（13-14周）
任务7.1：7种构建工具适配（@micro-core/builders）
Webpack适配器
Rollup适配器
Parcel适配器
Vite适配器
esbuild适配器
Rspack适配器
Turbopack适配器
第八阶段：Sidecar模式实现（15-16周）
任务8.1：Sidecar核心（@micro-core/sidecar）
一行代码接入：

export async function init(options?: SidecarOptions): Promise<void> {
const sidecar = new Sidecar(options)
await sidecar.autoStart()
}
核心功能：

零配置启动
自动发现机制
渐进式迁移工具
第九阶段：兼容模式开发（17-18周）
任务9.1：兼容适配器（@micro-core/compatibility）
qiankun兼容：

export class QiankunAdapter {
registerMicroApps: typeof qiankun.registerMicroApps
start: typeof qiankun.start
loadMicroApp: typeof qiankun.loadMicroApp
}
兼容框架：

qiankun完全兼容
wujie完全兼容
micro-app完全兼容
第十阶段：性能优化系统（19-20周）
任务10.1：性能监控（@micro-core/performance）
核心功能：

性能监控器
内存管理和泄漏检测
智能预加载策略
缓存优化
第十一阶段：权限管理系统（21周）
任务11.1：权限核心（@micro-core/auth）
分层权限校验：

权限管理器
路由守卫
资源守卫
JWT/OAuth提供者
第十二阶段：开发工具（22周）
任务12.1：调试工具（@micro-core/dev-tools）
开发工具：

微前端调试器
开发服务器
调试面板
应用树视图
第十三阶段：资源管理系统（23周）
任务13.1：资源管理器（@micro-core/resource）
资源管理：

动态导入
ES模块/UMD加载器
版本控制
缓存策略
第十四阶段：CLI命令行工具（24周）
任务14.1：CLI工具（@micro-core/cli）
命令支持：

micro-core init # 初始化微前端
micro-core add-main # 配置基座应用
micro-core add-sub # 配置子应用
micro-core dev # 开发命令
micro-core build # 构建命令
智能功能：

项目检测
代码生成
自动转换
第十五阶段：示例项目开发（25周）
任务15.1：Vue3基座 + 4种子应用
示例结构：

Vue3基座应用（统一容器）
React子应用
Vue2子应用
Vue3子应用
HTML子应用
第十六阶段：文档系统（26周）
任务16.1：VitePress文档
文档特性：

中英文双语
深浅主题切换
交互式演示
API自动生成
第十七阶段：测试体系（贯穿全程）
任务17.1：测试覆盖
测试要求：

单元测试：90%以上覆盖率
集成测试：跨包功能测试
E2E测试：端到端场景
性能测试：指标验证
第十八阶段：CI/CD和发布（最后1周）
任务18.1：自动化流程
发布流程：

GitHub Actions配置
自动化测试构建
版本管理
文档部署
📊 开发时间规划
| 阶段 | 任务 | 周期 | 优先级 |
|------|------|------|--------|
| 1-2 | 基础设施 + 核心运行时 | 4周 | 🔴 最高 |
| 3-5 | 沙箱 + 插件 + 通信 | 6周 | 🟡 高 |
| 6-8 | 适配器 + 构建工具 + Sidecar | 6周 | 🟡 高 |
| 9-13 | 兼容 + 性能 + 权限 + 开发工具 + 资源 | 5周 | 🟢 中 |
| 14-16 | CLI + 示例 + 文档 | 3周 | 🟡 高 |
| 17-18 | 测试 + CI/CD | 2周 | 🟡 高 |

总开发周期：26周

🎯 关键技术指标
性能指标
核心包体积：<15KB
应用加载时间：<500ms
内存占用：<50MB
首屏渲染：<1s
质量指标
测试覆盖率：>90%
TypeScript覆盖：100%
文档完整性：100%
API兼容性：100%
🔧 最佳实践建议
开发规范
严格遵循架构特性：确保每个特性都按文档要求实现
渐进式开发：先实现核心功能，再扩展高级特性
测试驱动：每个模块都要有完整的测试覆盖
文档同步：代码和文档同步更新
性能监控：持续监控性能指标
风险控制
核心包体积控制：严格监控core包大小
兼容性测试：确保多框架兼容性
性能基准：建立性能基准测试
安全审计：定期进行安全审计

@/_dev/ @/_dev/架构特性.md @/_dev/项目结构梳理.md 可以参考备份目录下的项目代码，严格遵循“项目结构梳理.md”目录结构，深度、全面执行上面要求的开发任务。严禁幻觉，依次认真执行，严禁中断













@/Users/<USER>/Desktop/micro-core/架构特性.md 请深度、全面分析、理解文档内容，设计架构一份完整、全面的微前端目录结构，只要输出整体目录结构，要求如下：
- 确保“架构特征.md”文档所有功能都齐全
- 采用微内核+可插拔的自由插件（内置插件+自定义插件）的 monorepo 多包架构方式，用户可以按需加载所需插件
- 核心包只保留最小运行时的核心功能和代码
- 将所有的公共工具、公共函数、公共代码、公共类型、公共枚举等所有公共部分都抽离到公共的工具子包里面
- 确保多层沙箱机制齐全，并且可以自由组合，从 JavaScript、CSS、HTML 粒度，也可以从基座、各个子包维度进行自由组合，默认全局采用 Proxy 代理方式
- 确保所有核心内核、沙箱系统、插件系统、应用间通信、多框架适配、构建工具适配、性能优化、Sidecar模式、多工程复用、权限系统、错误处理与监控等功能都要齐全、完整
- 构建工具、测试框架、版本管理、发布配置、项目信息等都要统一，包命名、文件命名、函数命名、变量命名、目录结构、注释说明等都要标准、统一
- 所有子包、文件都要保证职责单一原则，合理规范与拆分，保证既不能过于内聚、也不要过于细分。尽量保证所有文件函数不超过 500 行
- 要求输出整体 packages 多包架构目录的所有文件以及文件功能说明，要求全面无遗漏；也要包括统一的__tests__目录下所有测试文件，覆盖率要达到 90% 以上
- 也要将打包工具、演示项目、vitepress文档系统的所有文件都梳理清楚，包括文件内容说明，360 度无死角梳理清楚
- 所有梳理都要精确到文件粒度，每一个子包、每一个模块、每一个功能、每一个文件不要有任何遗漏和缺失，保证360度无死角，要多次核对
- 要严格遵循Tree-shaking 原则，所有功能都要按需加载，不能出现冗余代码
在根目录输出一份完整的 tree 架构目录说明文档“项目结构梳理.md”，严禁幻觉，认真执行
rewrite prompt 用中文，要深入结合“架构特性.md”文档优化提示词







请基于 `/Users/<USER>/Desktop/micro-core/架构特性.md` 文档内容，设计并输出一个完整的微前端 monorepo 项目架构。具体要求如下：

## 核心架构要求
1. **微内核架构**：采用微内核 + 可插拔插件的设计模式
   - 核心包（packages/core）仅保留最小运行时功能
   - 支持内置插件和自定义插件的动态加载
   - 实现按需加载机制，严格遵循 Tree-shaking 原则

2. **包结构组织**：
   - 公共代码统一抽离到 packages/shared 包
   - 包括：工具函数、类型定义、枚举、常量、公共组件等
   - 每个包职责单一，避免过度内聚或过度细分
   - 单个文件代码量控制在 500 行以内

3. **沙箱系统**：
   - 实现多层级沙箱机制（JavaScript/CSS/HTML 三个维度）
   - 支持基座应用和子应用的沙箱隔离
   - 默认采用 Proxy 代理方式实现
   - 支持沙箱策略的自由组合配置

4. **工程化配置**：
   - 统一的构建工具配置
   - 完整的测试框架设置
   - 版本管理和发布配置
   - 标准化的项目信息配置

## 功能完整性要求
必须包含以下所有功能模块：
- 核心运行时内核
- 多层沙箱系统
- 插件系统（加载、卸载、生命周期管理）
- 应用间通信机制
- 多框架适配器（React/Vue/Angular 等）
- 构建工具适配
- 性能优化模块
- Sidecar 模式支持
- 多工程代码复用
- 权限管理系统
- 错误处理与监控系统
- 支持 qiankun、wujie、microApp 兼容模式

## 输出要求
1. **目录结构**：输出完整的 packages 多包架构目录树
2. **文件清单**：精确到每个文件，包含功能说明
3. **测试覆盖**：包含完整的 __tests__ 目录结构，确保 90% 以上测试覆盖率
4. **工具链配置**：包含构建工具、演示项目、VitePress 文档系统的完整文件结构
5. **命名规范**：统一的包名、文件名、函数名、变量名命名标准

## 质量标准
- 严格遵循单一职责原则
- 统一的代码风格和注释规范
- 完整的类型定义和文档说明
- 严格基于架构特性.md 文档内容，不允许遗漏任何功能特性
- 确保架构设计的完整性和一致性
- 所有输出内容必须准确无误，严禁产生幻觉内容
- 目录结构要体现清晰的层次关系和职责分离
- 支持渐进式升级和向后兼容

## 最终交付物
在项目根目录创建 `项目结构梳理.md` 文件，包含：
- 完整的目录树结构
- 每个文件的功能说明
- 包依赖关系图（ASCII art 表示图）
- 架构设计说明

请确保输出内容与 `架构特性.md` 文档要求完全对应，不遗漏任何功能点。











请基于 `/Users/<USER>/Desktop/micro-core/架构特性.md` 文档内容，深度分析并理解其中的所有架构特性和功能需求，然后设计一个完整、全面的微前端 monorepo 多包架构目录结构。

**核心架构要求：**
1. **微内核架构**：采用微内核 + 可插拔插件系统的设计模式
   - 核心包（packages/core/）仅保留最小运行时功能
   - 支持内置插件和自定义插件的按需加载机制
   - 插件系统要支持热插拔和动态加载

2. **代码组织原则**：
   - 所有公共工具、函数、类型定义、枚举等抽离到 packages/shared/ 包
   - 每个子包职责单一，避免过度内聚或过度细分
   - 单个文件代码行数尽量控制在 500 行以内
   - 遵循统一的命名规范（包名、文件名、函数名、变量名）

3. **沙箱系统**：
   - 实现多层沙箱机制，支持自由组合
   - 支持 JavaScript、CSS、HTML 三个维度的沙箱隔离
   - 支持基座应用和子应用维度的沙箱隔离
   - 默认采用 Proxy 代理方式实现全局沙箱

4. **必备功能模块**（确保架构特性.md 中的所有功能完整实现）：
   - 核心内核系统
   - 多层沙箱系统
   - 插件系统架构
   - 应用间通信机制
   - 多框架适配层
   - 构建工具适配
   - 性能优化模块
   - Sidecar 模式支持
   - 多工程代码复用
   - 权限管理系统
   - 错误处理与监控

5. **工程化配置**：
   - 统一的构建工具配置
   - 完整的测试框架设置
   - 版本管理和发布配置
   - 标准化的项目信息配置

**输出要求：**
1. **完整目录结构**：输出 packages/ 下所有子包的完整目录结构
2. **文件功能说明**：每个文件都要包含详细的功能说明
3. **测试覆盖**：包含 __tests__ 目录下的所有测试文件，确保测试覆盖率达到 90% 以上
4. **配套工具**：包含打包工具、演示项目、VitePress 文档系统的完整文件结构和内容说明
5. **文档输出**：在根目录生成 `项目结构梳理.md` 文档，以 tree 格式展示完整架构

**执行标准：**
- 严格基于架构特性.md 文档内容，不允许遗漏任何功能特性
- 所有梳理都要精确到文件粒度，每一个子包、每一个模块、每一个功能、每一个文件不要有任何遗漏和缺失，保证360度无死角，要多次核对
- 确保架构设计的完整性和一致性
- 所有输出内容必须准确无误，严禁产生幻觉内容
- 目录结构要体现清晰的层次关系和职责分离







整理所有项目结构和文件，将所有非项目必要的文件都按原目录路径移动到 @/_backup 目录下，如果存在结构不合理的，请按标准调整，使其更加规范、清晰。严禁幻觉，认真执行



@/_backup/ @/packages/shared "按照以下规范整理项目结构和文件：1) 将所有非项目必要的文件（如临时文件、废弃资源等）按原始目录结构完整迁移至@/_backup目录；2) 对存在问题的目录结构进行标准化调整，确保符合行业通用规范（如src存放源码、assets存放静态资源等）；3) 保持核心项目文件的完整性和可运行性；4) 对需要调整的目录需满足：a) 层级不超过3级 b) 同类型文件集中存放 c) 命名全小写用连字符连接；5) 将所有公共工具、公共函数、公共类型、公共枚举等所有公共部分都抽离到 @/packages/shared 子包里面。严禁幻觉，认真依次执行，完成后提供新的目录树结构说明。"




请深度、全面、严格遵循 `架构特性.md` 文档的详细内容，重新、全面、完整的优化当前微前端架构项目，从而达到一个完整生产标准的微前端架构系统。项目必须严格遵循文档中定义的所有技术规范和架构要求




第一阶段：核心基础设施完善（优先级：🔥 极高）
1.1 创建缺失的核心包结构
创建 packages/communication 通信系统包
创建 packages/sandbox 沙箱系统包
建立完整的包依赖关系和构建配置
1.2 完善 shared 包的基础设施
实现 9 个核心工具模块
建立完整的 TypeScript 类型定义系统
实现统一的错误处理机制
建立常量管理系统
1.3 核心运行时系统实现
完善 core 包的微内核架构（<15KB 目标）
实现应用生命周期管理器
实现插件管理器和注册机制
建立应用加载和状态管理机制
交付物：

完整的包结构和依赖关系
核心运行时系统（<15KB）
基础工具函数库和类型系统
单元测试覆盖率 >90%
第二阶段：沙箱隔离系统实现（优先级：🔥 极高）
2.1 沙箱系统架构设计
实现 6 种沙箱策略：Proxy、DefineProperty、iframe、WebComponent、Namespace、Federation
建立沙箱工厂和管理器
实现智能沙箱选择机制
2.2 多层隔离能力实现
JavaScript 执行环境隔离
CSS 样式隔离机制
全局变量隔离策略
性能优化和资源复用
交付物：

完整的沙箱系统包
6 种沙箱策略全部实现
沙箱性能测试和基准
隔离效果验证测试
第三阶段：应用间通信系统（优先级：🔥 高）
3.1 通信基础设施
实现高性能事件总线系统
建立消息通道和路由机制
实现通信管理器和状态同步
3.2 通信协议和安全
定义标准化通信协议
实现消息序列化和反序列化
建立通信权限控制机制
交付物：

完整的通信系统包
事件总线和消息通道
通信协议文档
性能和安全测试
第四阶段：插件系统架构完善（优先级：🔥 高）
4.1 插件基础设施优化
完善插件注册和生命周期管理
实现插件懒加载和动态卸载
建立插件依赖管理机制
4.2 核心插件实现
Router 路由插件
Communication 通信插件
Auth 权限插件
DevTools 开发工具插件
各种沙箱插件的完善
交付物：

完善的插件系统架构
核心插件完整实现
插件开发文档和示例
插件市场基础设施
第五阶段：框架适配器系统优化（优先级：🔥 中高）
5.1 适配器架构优化
完善基础适配器抽象
实现适配器工厂和自动检测
建立兼容性检查机制
5.2 多框架支持完善
React、Vue2、Vue3 适配器优化
Angular、Svelte、Solid 适配器完善
Lit、HTML、原生 JS 适配器实现
交付物：

完整的适配器系统
9+ 框架适配器全部实现
框架兼容性测试套件
适配器开发指南
第六阶段：Sidecar 模式实现（优先级：🔥 中高）
6.1 一行代码接入实现
实现零配置启动机制
建立自动发现和注册系统
实现渐进式迁移支持
6.2 智能化功能
自动检测现有应用架构
智能配置生成
无缝集成现有项目
交付物：

完整的 Sidecar 模式实现
一行代码接入功能
自动发现和配置系统
迁移工具和文档
第七阶段：构建工具适配和性能优化（优先级：🔥 中）
7.1 构建工具适配完善
完善 7 种构建工具支持
实现构建配置复用
建立构建性能优化
7.2 性能优化实现
智能预加载和懒加载
缓存策略优化
内存管理和监控
Tree-Shaking 优化
交付物：

完整的构建工具适配
性能优化机制
性能监控和分析工具
性能基准测试
第八阶段：权限控制和错误处理（优先级：🔥 中）
8.1 权限控制系统
实现分层权限校验
建立角色管理机制
实现访问控制策略
8.2 错误处理与监控
统一错误处理机制
错误恢复策略
监控和上报系统
交付物：

完整的权限控制系统
错误处理和监控机制
安全测试和验证
监控面板和工具
第九阶段：开发工具和文档（优先级：🔥 低）
9.1 开发工具实现
调试面板和工具
性能分析工具
日志系统
9.2 文档和示例
完整的 API 文档
使用指南和最佳实践
示例项目和教程
交付物：

开发调试工具
完整的文档系统
示例项目和教程
开发者指南
🎯 每个阶段的验收标准
技术指标
包体积：core 包 <15KB
性能：应用加载 <500ms，内存占用 <50MB，首屏渲染 <1s
测试覆盖率：单元测试 >90%，集成测试 >80%
类型安全：100% TypeScript 覆盖，无 any 类型
功能完整性
所有架构特性文档要求的功能全部实现
6 种沙箱策略全部可用
9+ 框架适配器全部支持
7 种构建工具全部适配
代码质量
严格遵循职责边界，不得跨包包含不当内容
目录层级不超过 3 层
单文件代码行数 200-800 行
完整的 TypeScript 类型定义




第一阶段：核心运行时系统优化（优先级：🔥🔥🔥）
目标：确保 core 包符合微内核架构要求（<15KB）

任务清单：

重构 core 包结构

移除所有工具函数到 shared 包
保留最小运行时功能
实现应用生命周期管理器
实现插件管理器
优化 shared 包

整合所有公共工具函数
完善类型定义系统
实现错误处理机制
验收标准：

core 包构建后 < 15KB
所有类型定义完整
单元测试覆盖率 > 90%
第二阶段：沙箱隔离系统实现（优先级：🔥🔥🔥）
目标：实现 6 种沙箱策略的完整功能

任务清单：

沙箱基础架构

实现沙箱工厂模式
实现沙箱管理器
实现沙箱选择策略
6种沙箱策略实现

Proxy 沙箱（高性能场景）
DefineProperty 沙箱（兼容性场景）
iframe 沙箱（强隔离场景）
WebComponent 沙箱（组件化场景）
Namespace 沙箱（命名空间隔离）
Federation 沙箱（模块联邦场景）
验收标准：

6种沙箱策略全部可用
JavaScript、CSS、全局变量完全隔离
性能测试通过（应用加载 < 500ms）
第三阶段：插件系统架构完善（优先级：🔥🔥）
目标：建立完整的插件生态系统

任务清单：

插件基础设施

完善插件注册机制
实现插件生命周期管理
实现扩展点机制
核心插件实现

Router 插件（路由管理）
Communication 插件（通信管理）
Auth 插件（权限控制）
DevTools 插件（开发工具）
Logger 插件（日志系统）
Metrics 插件（性能监控）
验收标准：

插件按需加载功能正常
插件生命周期管理完整
第三方插件开发文档完善
第四阶段：应用间通信系统（优先级：🔥🔥）
目标：实现高性能的应用间通信机制

任务清单：

事件总线系统

实现发布订阅模式
实现事件命名空间
实现事件优先级
消息通道系统

实现跨应用消息传递
实现消息序列化/反序列化
实现消息持久化
验收标准：

通信延迟 < 10ms
支持复杂数据类型传递
消息可靠性 99.9%
第五阶段：资源适配器系统（优先级：🔥）
目标：实现智能的资源加载和管理

任务清单：

资源管理器

实现动态模块导入
实现资源缓存策略
实现版本冲突解决
预加载策略

实现智能预加载
实现懒加载机制
实现资源优先级管理
第六阶段：框架适配器完善（优先级：🔥）
目标：支持所有主流前端框架

任务清单：

适配器工厂

实现自动框架检测
实现适配器创建
实现兼容性检查
框架适配器

React 适配器优化
Vue2/Vue3 适配器优化
Angular 适配器完善
其他框架适配器实现
第七阶段：Sidecar模式实现（优先级：中）
目标：实现一行代码接入能力

任务清单：

自动发现机制

实现应用自动发现
实现配置自动生成
实现依赖自动注入
零配置启动

实现默认配置策略
实现环境自动检测
实现错误自动恢复




请深度、全面、严格遵循 `架构特性.md` 文档的详细内容，设计并实现一个完整的微前端架构系统。项目必须严格遵循文档中定义的所有技术规范和架构要求。严禁幻觉。

## 技术栈与架构模式
- **核心技术栈**: TypeScript + Vite
- **项目架构**: monorepo 多包架构，纯前端实现
- **架构模式**: 微内核 + 插件化的微前端架构

## 包结构与职责划分（严格遵循）
1. **packages/core/** - 微前端微内核包
   - 职责：仅包含微前端运行时的最小必要功能
   - 限制：不得包含任何工具函数、类型定义或枚举
   
2. **packages/shared/** - 共享资源包
   - 职责：所有公共工具函数、类型定义、枚举、可复用业务逻辑
   
3. **packages/plugins/** - 插件系统包
   - 职责：所有插件的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举
   
4. **packages/adapters/** - 适配器系统包
   - 职责：所有框架适配器的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举
   
5. **packages/builders/** - 构建工具适配包
   - 职责：所有构建工具适配器的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举
   
6. **packages/sidecar/** - 边车模式包
   - 职责：所有边车模式的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举
   
7. **packages/communication/** - 通信系统包
   - 职责：所有通信相关的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举
   
8. **packages/sandbox/** - 沙箱系统包
   - 职责：所有沙箱相关的完整功能和逻辑实现
   - 限制：不得包含任何工具函数、类型定义或枚举

## 代码组织规范
- **目录层级**: 最大深度不超过 3 层
- **文件大小**: 单个文件代码行数控制在 200-800 行
- **模块设计**: 功能模块既不过度内聚也不过度分散
- **职责边界**: 每个模块职责单一且边界清晰

## 核心功能模块实现优先级
按以下顺序逐步实现：
1. **核心运行时系统** - 应用生命周期管理、状态管理、应用加载机制
2. **沙箱隔离系统** - JavaScript 执行沙箱、CSS 样式隔离、全局变量隔离
3. **插件系统架构** - 插件注册机制、插件生命周期、扩展点机制
4. **应用间通信系统** - 事件总线、状态共享、消息传递机制
5. **资源适配器系统** - 静态资源加载、动态模块导入、缓存管理
6. **共享资源管理** - 依赖共享、版本冲突解决、资源去重
7. **构建工具适配** - 支持 Webpack、Vite、Rollup 等主流构建工具
8. **本地开发支持** - 开发服务器、热更新、代理配置
9. **性能优化机制** - 懒加载、预加载、缓存策略
10. **权限控制系统** - 访问控制、角色管理、权限验证
11. **错误处理与监控** - 异常捕获、错误上报、性能监控
12. **开发调试工具** - 调试面板、日志系统、性能分析
13. **Sidecar模式支持** - 独立进程通信、服务代理（可选实现）
14. **多工程复用设计** - 模块复用、配置复用、构建复用（可选实现）

## 代码质量标准
- **类型安全**: 所有代码必须包含完整的 TypeScript 类型定义
- **文档完整**: 每个模块都要有清晰的注释说明其用途和使用方法
- **示例齐全**: 提供完整的使用示例和 API 文档
- **风格统一**: 确保代码风格统一，遵循最佳实践

## 执行计划要求
1. **任务分解**: 将整体任务分解为多个可管理的子任务
2. **测试驱动**: 每个子任务完成后必须提供测试用例验证功能
3. **严格遵循**: 严格按照"架构特性"文档执行，不允许随意修改架构设计
4. **问题处理**: 如遇到技术难点，先分析问题再提出解决方案
5. **特殊操作**: 删除文件前请按原目录路径备份到@/_backup目录

## 开始步骤
请首先：
1. 分析现有项目结构和"架构特性"文档的具体要求
2. 制定详细的分阶段实施计划
3. 确定每个阶段的具体交付物和验收标准






用 typescript+vite 最新版本写一个纯前端的微内核的微前端+自由插件功能增强的架构，采用 monorepo 的架构，core 只保留微前端微内核可运行的最小运行时，将所有公共工具、公共函数、公共类型、公共枚举等所有公共部分都抽离到 shared 子包里面。通信、沙箱、插件、适配器、性能等采用子包承载，严禁幻觉、认真执行



深度、全面、严格遵循“架构特性”文档完整内容，使用 typescript+vite技术栈和 monorepo 多包纯前端的架构设计，完成微前端的微内核+自由插件的架构。core 只保留微前端微内核可运行的最小运行时，将所有公共工具、公共函数、公共类型、公共枚举等所有公共部分都抽离到 shared 子包里面。采用合理、标准的目录架构模式，确保目录深度合理，文件粒度合理，功能不要过于内聚，也不要过于细分。注释内容和说明内容、examples 示例要统一、清晰、全面。以下核心功能完整：
- **核心运行时系统**：应用加载、生命周期管理、状态管理
- **沙箱隔离系统**：JavaScript沙箱、CSS隔离、全局变量隔离
- **插件系统架构**：插件注册、生命周期、扩展机制
- **应用间通信系统**：事件总线、状态共享、消息传递
- **资源适配器系统**：静态资源加载、动态导入、缓存管理
- **Sidecar模式支持**：独立进程通信、服务代理
- **共享资源管理**：依赖共享、版本管理、冲突解决
- **多工程复用设计**：模块复用、配置复用、构建复用
- **构建工具适配**：Webpack、Vite、Rollup等构建工具支持
- **本地联调支持**：开发服务器、热更新、代理配置
- **性能优化实现**：懒加载、预加载、缓存策略
- **权限系统实现**：访问控制、角色管理、权限验证
- **错误处理与监控**：异常捕获、错误上报、性能监控
- **开发工具与调试**：调试面板、日志系统、性能分析
可以拆分多个子任务、严禁幻觉、认真执行




深度、全面、严格遵循 `架构特性.md` 文档的详细内容，设计并实现一个完整的微前端架构系统。请严格遵循文档中定义的架构特性和技术规范。

## 项目分析与规划要求
1. **首先分析现有项目结构**：
   - 检查当前 `/micro-core` 目录下的文件结构
   - 分析现有代码的架构模式和技术栈
   - 识别需要重构或新建的模块

2. **深度解读架构特性文档**：
   - 详细分析文档中定义的微前端架构特性
   - 理解微内核 + 插件化架构模式的核心原理
   - 明确各个系统模块的职责边界和交互关系

## 技术栈与架构规范
- **核心技术栈**：TypeScript + Vite
- **项目架构**：monorepo 多包架构，纯前端实现
- **架构模式**：微内核 + 插件化微前端架构

## 包结构设计（严格遵循分层原则）
```
packages/
├── core/           # 微前端微内核包（最小化核心运行时）
├── shared/         # 共享资源包（工具函数、类型定义、枚举）
├── plugins/        # 插件系统包（完整插件功能实现）
├── adapters/       # 适配器系统包（框架适配器实现）
├── builders/       # 构建工具适配包（构建工具适配器）
├── sidecar/        # 边车模式包（边车模式功能实现）
├── communication/  # 通信系统包（应用间通信功能）
└── sandbox/        # 沙箱系统包（沙箱隔离功能）
```

## 代码组织规范
- **目录层级**：最多 3 层深度
- **文件大小**：单文件 200-800 行代码
- **模块设计**：职责单一，边界清晰，避免过度内聚或分散

## 核心功能模块实现优先级
1. **核心运行时系统**（packages/core/）
   - 应用生命周期管理
   - 状态管理机制
   - 应用加载与卸载机制

2. **沙箱隔离系统**（packages/sandbox/）
   - JavaScript 执行环境隔离
   - CSS 样式隔离机制
   - 全局变量隔离保护
   - 可自由组合沙箱

3. **插件系统架构**（packages/plugins/）
   - 插件注册与发现机制
   - 插件生命周期管理
   - 扩展点与钩子机制

4. **应用间通信系统**（packages/communication/）
   - 事件总线实现
   - 状态共享机制
   - 消息传递与路由

5. **资源适配器系统**（packages/adapters/）
   - 静态资源动态加载
   - 模块导入与解析
   - 资源缓存管理

6. **共享资源管理**（packages/shared/）
   - 依赖共享策略
   - 版本冲突解决
   - 资源去重优化

7. **构建工具适配**（packages/builders/）
   - Webpack 适配器
   - Vite 适配器
   - Rollup 适配器

8. **开发环境支持**
   - 本地开发服务器
   - 热更新机制
   - 代理配置管理

9. **性能优化机制**
   - 应用懒加载
   - 资源预加载
   - 智能缓存策略

10. **权限控制系统**
    - 访问权限控制
    - 角色权限管理
    - 权限验证机制

11. **错误处理与监控**
    - 异常捕获与处理
    - 错误上报机制
    - 性能监控分析

12. **开发调试工具**
    - 调试面板界面
    - 日志系统实现
    - 性能分析工具

13. **Sidecar模式支持**（packages/sidecar/，可选）
    - 独立进程通信
    - 服务代理机制

14. **多工程复用设计**（可选）
    - 模块复用策略
    - 配置复用机制
    - 构建复用优化

## 代码质量标准
- **类型安全**：完整的 TypeScript 类型定义，无 any 类型
- **文档规范**：每个模块包含详细的 JSDoc 注释
- **API 文档**：提供完整的使用示例和 API 说明
- **代码风格**：统一的代码格式和命名规范
- **测试覆盖**：每个功能模块都要有对应的测试用例

## 执行计划要求
1. **任务分解**：将整体架构实现分解为可管理的子任务
2. **渐进式开发**：按优先级逐步实现各个功能模块
3. **测试驱动**：每个子任务完成后必须提供测试用例验证
4. **架构一致性**：严格遵循架构特性文档，不允许随意偏离设计
5. **问题解决**：遇到技术难点时，先进行深入分析再提出解决方案

## 开始执行
请从以下步骤开始：
1. 分析当前项目结构和现有代码
2. 深度解读架构特性文档内容
3. 制定详细的分阶段实施计划
4. 开始实现第一个核心模块（核心运行时系统）







深度、全面、严格遵循“架构特性”文档详细内容，设计并实现一个完整的微前端架构系统。项目需要严格遵循以下技术规范和架构要求：

## 技术栈要求
- 使用 TypeScript + Vite 作为主要技术栈
- 采用 monorepo 多包架构，纯前端实现
- 实现微内核 + 插件化的微前端架构模式

## 架构分层要求
1. **packages/core/** - 微前端微内核包
   - 仅保留微前端运行时的最小必要功能
   - 不包含任何工具函数、类型定义或枚举
   
2. **packages/shared/** - 共享资源包
   - 包含所有公共工具函数
   - 包含所有公共类型定义和枚举
   - 包含所有可复用的业务逻辑
3. **packages/plugins/** - 插件系统包
   - 包含所有插件完整功能和逻辑实现

4. **packages/adapters/** - 适配器系统包
   - 包含所有框架适配器完整功能和逻辑实现

5. **packages/builders/** - 构建工具适配包
   - 包含所有构建工具适配器完整功能和逻辑实现

6. **packages/sidecar/** - 边车模式包
   - 包含所有边车模式完整功能和逻辑实现

7. **packages/communication/** - 通信系统包
   - 包含所有通信相关完整功能和逻辑实现

8. **packages/sandbox/** - 沙箱系统包
   - 包含所有沙箱相关完整功能和逻辑实现


## 目录结构要求
- 目录层级深度控制在 3 层以内
- 单个文件代码行数控制在 200-800 行
- 功能模块既不过度内聚也不过度分散
- 每个模块职责单一且边界清晰

## 核心功能模块实现（按优先级排序）
1. **核心运行时系统** - 应用生命周期管理、状态管理、应用加载机制
2. **沙箱隔离系统** - JavaScript 执行沙箱、CSS 样式隔离、全局变量隔离
3. **插件系统架构** - 插件注册机制、插件生命周期、扩展点机制
4. **应用间通信系统** - 事件总线、状态共享、消息传递机制
5. **资源适配器系统** - 静态资源加载、动态模块导入、缓存管理
6. **共享资源管理** - 依赖共享、版本冲突解决、资源去重
7. **构建工具适配** - 支持 Webpack、Vite、Rollup 等主流构建工具
8. **本地开发支持** - 开发服务器、热更新、代理配置
9. **性能优化机制** - 懒加载、预加载、缓存策略
10. **权限控制系统** - 访问控制、角色管理、权限验证
11. **错误处理与监控** - 异常捕获、错误上报、性能监控
12. **开发调试工具** - 调试面板、日志系统、性能分析
13. **Sidecar模式支持** - 独立进程通信、服务代理（可选实现）
14. **多工程复用设计** - 模块复用、配置复用、构建复用（可选实现）

## 代码质量要求
- 所有代码必须包含完整的 TypeScript 类型定义
- 每个模块都要有清晰的注释说明其用途和使用方法
- 提供完整的使用示例和 API 文档
- 确保代码风格统一，遵循最佳实践

## 执行要求
- 请将此任务分解为多个子任务，逐步实现
- 每个子任务完成后需要提供测试用例验证功能
- 严格按照架构设计文档执行，不允许随意修改架构
- 如遇到技术难点，请先分析问题再提出解决方案

请从分析现有项目结构开始，然后制定详细的实施计划。





请严格遵循"项目优化建议.md"文档中的要求和规范，对当前微前端项目进行全面深度重构优化。

## 核心重构目标

### 1. 重新设计shared目录架构
- **迁移范围**：将所有子包中的公共工具函数、类型定义、常量配置迁移至packages/shared/
- **整合要求**：
  - 创建统一的工具函数库（utils/）
  - 建立通用类型定义系统（types/）
  - 整合公共常量和配置（constants/、config/）
- **架构目标**：确保shared目录成为项目的公共基础设施层，为所有子包提供统一的基础能力

### 2. 保证微前端核心功能完整性
必须包含以下完整功能模块（不可删除或简化）：
- **核心运行时系统**：应用加载、生命周期管理、状态管理
- **沙箱隔离系统**：JavaScript沙箱、CSS隔离、全局变量隔离
- **插件系统架构**：插件注册、生命周期、扩展机制
- **应用间通信系统**：事件总线、状态共享、消息传递
- **资源适配器系统**：静态资源加载、动态导入、缓存管理
- **Sidecar模式支持**：独立进程通信、服务代理
- **共享资源管理**：依赖共享、版本管理、冲突解决
- **多工程复用设计**：模块复用、配置复用、构建复用
- **构建工具适配**：Webpack、Vite、Rollup等构建工具支持
- **本地联调支持**：开发服务器、热更新、代理配置
- **性能优化实现**：懒加载、预加载、缓存策略
- **权限系统实现**：访问控制、角色管理、权限验证
- **错误处理与监控**：异常捕获、错误上报、性能监控
- **开发工具与调试**：调试面板、日志系统、性能分析

### 3. 构建工具统一化
- **清理目标**：移除所有tsup相关配置文件和依赖
- **统一标准**：所有子包统一使用Vite 7.0.6作为构建工具
- **配置要求**：创建统一的Vite配置模板，确保构建行为一致

### 4. 子包职责重新划分（核心重点）
- **packages/core/**：
  - 职责：微前端核心运行时引擎
  - 包含：应用加载器、生命周期管理、基础沙箱、核心API
  - 原则：保持最小化，只包含必需的核心功能
  
- **packages/shared/**：
  - 职责：公共基础设施和工具库
  - 包含：工具函数、类型定义、常量、通用组件
  - 原则：为所有其他包提供基础能力支持

- **其他子包**：
  - 职责：功能增强插件（如沙箱增强、通信增强、监控插件等）
  - 原则：基于core的插件化扩展，职责单一、功能纯净
  - 架构：遵循微内核+插件的设计模式

## 架构设计原则
- **目录架构（重点）**：确保目录架构标准、清晰、规范、扁平化（要求可以重新梳理和优化）
- **目录层级控制（重点）**：严格控制目录深度不超过3层，避免交叉引用、重复引用
- **单一职责原则**：每个文件、模块、包都有明确单一的职责
- **命名规范统一**：
  - 目录名：kebab-case（如micro-core、shared-utils）
  - 文件名：camelCase或kebab-case保持一致
  - 函数名：camelCase
  - 类型名：PascalCase
- **依赖关系清晰**：避免循环依赖，建立清晰的依赖层次

## 代码清理要求
- **冗余文件识别**：
  - 重复的工具函数和类型定义
  - 废弃的模块和组件
  - 临时测试文件和调试代码
  - 未使用的资源文件
- **清理执行**：
  - 将清理的文件按原始目录结构迁移至`_backup/`目录
  - 记录清理日志，说明每个文件的清理原因
  - 确保清理后不影响核心功能

要求执行任务：根据上述要求，先深度、全面检查 core 和 shared 两个子包所有文件，完成 core 核心包的优化工作，确保最小运行时的要求，公共工具、函数、常量等全部移动到 shared 包里面。
严禁幻觉，不要中断，一次性完成任务





请严格遵循"项目优化建议.md"文档中的要求和规范，对当前微前端项目进行全面深度重构优化。

## 前置准备工作
1. **深入分析现有代码结构**：
   - 使用codebase-retrieval工具全面了解当前项目架构
   - 识别所有子包的功能职责和依赖关系
   - 分析现有shared目录的使用情况和不足之处

2. **制定详细重构计划**：
   - 基于分析结果制定分阶段重构计划
   - 使用任务管理工具创建具体的执行步骤
   - 确保每个步骤都有明确的验收标准

## 核心重构目标

### 1. 重新设计shared目录架构
- **迁移范围**：将所有子包中的公共工具函数、类型定义、常量配置迁移至packages/shared/
- **整合要求**：
  - 创建统一的工具函数库（utils/）
  - 建立通用类型定义系统（types/）
  - 整合公共常量和配置（constants/、config/）
- **架构目标**：确保shared目录成为项目的公共基础设施层，为所有子包提供统一的基础能力

### 2. 保证微前端核心功能完整性
必须包含以下完整功能模块（不可删除或简化）：
- **核心运行时系统**：应用加载、生命周期管理、状态管理
- **沙箱隔离系统**：JavaScript沙箱、CSS隔离、全局变量隔离
- **插件系统架构**：插件注册、生命周期、扩展机制
- **应用间通信系统**：事件总线、状态共享、消息传递
- **资源适配器系统**：静态资源加载、动态导入、缓存管理
- **Sidecar模式支持**：独立进程通信、服务代理
- **共享资源管理**：依赖共享、版本管理、冲突解决
- **多工程复用设计**：模块复用、配置复用、构建复用
- **构建工具适配**：Webpack、Vite、Rollup等构建工具支持
- **本地联调支持**：开发服务器、热更新、代理配置
- **性能优化实现**：懒加载、预加载、缓存策略
- **权限系统实现**：访问控制、角色管理、权限验证
- **错误处理与监控**：异常捕获、错误上报、性能监控
- **开发工具与调试**：调试面板、日志系统、性能分析

### 3. 构建工具统一化
- **清理目标**：移除所有tsup相关配置文件和依赖
- **统一标准**：所有子包统一使用Vite 7.0.6作为构建工具
- **配置要求**：创建统一的Vite配置模板，确保构建行为一致

### 4. 子包职责重新划分（核心重点）
- **packages/core/**：
  - 职责：微前端核心运行时引擎
  - 包含：应用加载器、生命周期管理、基础沙箱、核心API
  - 原则：保持最小化，只包含必需的核心功能
  
- **packages/shared/**：
  - 职责：公共基础设施和工具库
  - 包含：工具函数、类型定义、常量、通用组件
  - 原则：为所有其他包提供基础能力支持

- **其他子包**：
  - 职责：功能增强插件（如沙箱增强、通信增强、监控插件等）
  - 原则：基于core的插件化扩展，职责单一、功能纯净
  - 架构：遵循微内核+插件的设计模式

## 架构设计原则
- **目录层级控制**：严格控制目录深度不超过4层
- **单一职责原则**：每个文件、模块、包都有明确单一的职责
- **命名规范统一**：
  - 目录名：kebab-case（如micro-core、shared-utils）
  - 文件名：camelCase或kebab-case保持一致
  - 函数名：camelCase
  - 类型名：PascalCase
- **依赖关系清晰**：避免循环依赖，建立清晰的依赖层次

## 代码清理要求
- **冗余文件识别**：
  - 重复的工具函数和类型定义
  - 废弃的模块和组件
  - 临时测试文件和调试代码
  - 未使用的资源文件
- **清理执行**：
  - 将清理的文件按原始目录结构迁移至`_backup/`目录
  - 记录清理日志，说明每个文件的清理原因
  - 确保清理后不影响核心功能

## 执行范围和限制
- **当前阶段专注**：微前端核心功能架构重构和代码组织优化
- **暂不处理**：
  - 单元测试和集成测试
  - 文档编写和维护
  - 示例应用和演示代码
  - 性能基准测试

## 执行要求和质量标准
- **基于现实**：严禁产生幻觉功能，必须基于现有代码进行重构
- **逐步验证**：每个重构步骤完成后进行功能验证
- **代码质量**：确保重构后代码结构清晰、逻辑完整、可维护性强
- **文档记录**：每个重要变更都要有明确的理由和技术依据

## 执行流程
1. **信息收集阶段**：深入分析现有代码结构和功能分布
2. **计划制定阶段**：基于分析结果制定详细的分阶段重构计划
3. **逐步执行阶段**：按计划逐步执行重构，每步都进行验证
4. **质量检查阶段**：确保所有核心功能完整且架构清晰

请首先使用相关工具深入分析当前项目状况，然后制定详细的重构执行计划。
严禁幻觉，不要中断，一次性完成任务








@/Users/<USER>/Desktop/micro-core/项目优化建议.md 严格遵循“项目优化建议.md”文档中的要求和规范，对当前项目进行全面深度优化：
- 重新设计 shared 目录所有的子包规划，要求里面包括所有子包、模块公共的工具逻辑和代码，公共的功能和逻辑，公共的类型命名之类的
- 项目中一定要包含所有功能特性的功能和逻辑，保证所有核心功能完整，可以重新设计子包设计和架构，确保每个子包纯净、整洁、清晰，逻辑和功能单一。所有功能和工具代码可以抽象到 shared 目录下对应的子包里面
- 清理所有tsup构建工具，统一使用vite 7.0.6
- 避免目录深度过深，保证单一职责原则
- 这是全新的项目，删除所有向后兼容逻辑和代码
- 每个文件要职责单一，避免高度内聚，也要避免过于细分
- 所有的目录、文件、函数等命名都要统一和规范
- 识别并移除所有冗余文件（包括重复资源、废弃模块和临时文件），将其按原始目录结构归档至@/_backup目录
- 要避免目录深度过深。避免文件过大，也要避免粒度太细，同时保证单一职责原则
基于以上要求，严禁幻觉，认真执行
- 先不要考虑 测试文件 和 文档建设、示例应用，只考虑微前端的功能完整、清晰、全面、齐全
rewrite prompt 用中文



@/项目优化建议.md 
请严格遵循"项目优化建议.md"文档中的要求和规范，对当前微前端项目进行全面深度重构优化。具体要求如下：

## 核心重构目标
1. **重新设计shared目录架构**：
- 将所有子包、模块的公共工具逻辑和代码迁移至shared目录
- 整合公共功能、逻辑和类型定义
- 确保shared目录成为项目的公共基础设施层

2. **保证功能完整性**：
- 项目必须包含所有微前端核心功能特性
- 可以重新设计子包架构，但不能删除任何核心功能
- 确保每个子包职责单一、逻辑清晰、功能纯净

3. **构建工具统一**：
- 清理所有tsup构建配置
- 统一使用vite 7.0.6作为构建工具

4. **所有子包职责划分**：
- 明确所有子包的职责划分，确保功能完整、逻辑清晰、职责单一
- 所有的公共工具、函数都移动到shared子包里面
- 确保整个架构是微内核+自由插件设计，保证core核心的完整功能，所有的插件都只是功能增强
- 确保核心运行时、沙箱系统、插件系统、通信系统、适配器系统、Sidecar模式、共享资源管理、多工程复用设计、技术栈与工具链、构建工具适配实现、本地联调支持、性能优化实现、权限系统实现、错误处理与监控、开发工具与调试等功能齐全，可落地、可执行、可生产的标准

## 架构设计原则
- 避免目录层级过深（建议不超过3-4层）
- 严格遵循单一职责原则
- 删除所有向后兼容的遗留代码（这是全新项目）
- 每个文件职责单一，避免过度内聚和过度细分
- 统一所有目录、文件、函数的命名规范

## 代码清理要求
- 识别并移除所有冗余文件：
- 重复的资源文件
- 废弃的模块代码
- 临时测试文件
- 将清理的文件按原始目录结构归档至`_backup`目录

## 执行范围限制
- **当前阶段仅关注**：微前端核心功能的完整性、清晰性和全面性
- **暂不处理**：测试文件、文档建设、示例应用

## 执行要求
- 严禁产生幻觉或臆测功能
- 必须基于现有代码进行重构
- 确保重构后的代码结构清晰、逻辑完整
- 每个步骤都要有明确的理由和依据

请按照以上要求，制定详细的重构计划并逐步执行。





请严格遵循"项目优化建议.md"文档中的要求和规范，对当前微前端项目进行全面深度重构优化。具体要求如下：

## 核心重构目标
1. **重新设计shared目录架构**：
   - 将所有子包、模块的公共工具逻辑和代码迁移至shared目录
   - 整合公共功能、逻辑和类型定义
   - 确保shared目录成为项目的公共基础设施层

2. **保证功能完整性**：
   - 项目必须包含所有微前端核心功能特性
   - 可以重新设计子包架构，但不能删除任何核心功能
   - 确保每个子包职责单一、逻辑清晰、功能纯净

3. **构建工具统一**：
   - 清理所有tsup构建配置
   - 统一使用vite 7.0.6作为构建工具

## 架构设计原则
- 避免目录层级过深（建议不超过3-4层）
- 严格遵循单一职责原则
- 删除所有向后兼容的遗留代码（这是全新项目）
- 每个文件职责单一，避免过度内聚和过度细分
- 统一所有目录、文件、函数的命名规范

## 代码清理要求
- 识别并移除所有冗余文件：
  - 重复的资源文件
  - 废弃的模块代码
  - 临时测试文件
- 将清理的文件按原始目录结构归档至`_backup`目录

## 执行范围限制
- **当前阶段仅关注**：微前端核心功能的完整性、清晰性和全面性
- **暂不处理**：测试文件、文档建设、示例应用

## 执行要求
- 严禁产生幻觉或臆测功能
- 必须基于现有代码进行重构
- 确保重构后的代码结构清晰、逻辑完整
- 每个步骤都要有明确的理由和依据

请按照以上要求，制定详细的重构计划并逐步执行。







严格遵循“开发功能特性.md”里面所有核心特性，结合“核心功能列表.md”、“架构特性.md”和全面、深度检查项目文件，在根目录梳理一份完整的“项目优化建议.md”文档，要求如下：
- 项目所有的功能特性是否齐全？
- 项目的子包、模块、文件是否保持单一职责原理？
- 项目的子包拆分是否合理？目录结构和文件划分是否清晰？文件内容是否高度内聚？文件粒度是否过于细分？
- 项目的技术栈是否符合要求？
    - **核心框架**：Vite 7.0.6 + TypeScript
    - **文档系统**：VitePress 2.0.0-alpha.8
    - **测试框架**：Vitest 3.2.4
    - **包管理**：pnpm + monorepo
    - **版本管理**：0.1.0
    - **发布渠道**：npm @micro-core 组织
    "author": "Echo <<EMAIL>>",
    "url": "https://github.com/echo008/micro-core.git"
- 项目文件、子包、函数等命名、子包划分、目录结构是否统一、规范？注释内容、函数命名是否符合要求？
- 项目的性能是否符合要求？测试覆盖率是否达标？技术栈、打包工具等是否完全统一？
基于以上要求，认真梳理，要求梳理出原内容+优化建议内容，严禁幻觉
rewrite prompt 用中文




/项目优化建议.md
/开发项目优化建议.md
严格遵循项目优化建议文档中的规范，对当前项目进行全面深度优化。识别并移除所有冗余文件（包括重复资源、废弃模块和临时文件），将其按原始目录结构归档至@/_backup目录。严格执行代码重构，确保符合开发项目优化建议中的性能指标和架构标准。优化过程需保持核心功能完整。清理所有 tsup构建工具，统一使用 vite 7.0.6 统一构建。要避免目录深度过深。文件过大，保证单一职责原则。这是新项目，不用兼容向后考虑（删除所有相关逻辑和代码、注释等），严禁幻觉，认真执行





请基于已创建的"开发功能特性.md"文档，结合项目中的"核心功能列表.md"、"架构特性.md"等相关文档，对整个 micro-core 项目进行全面深度分析，并在根目录创建一份详细的"项目优化建议.md"文档。

## 分析维度要求

### 1. 功能完整性分析
- 对照"开发功能特性.md"中列出的所有核心特性，逐一检查项目当前实现状态
- 识别已实现、部分实现、未实现的功能特性
- 分析功能缺失对整体架构的影响

### 2. 架构设计合理性分析
- **单一职责原则**：检查每个子包、模块、文件是否职责单一，避免功能耦合
- **包结构合理性**：评估 packages/ 目录下的子包划分是否符合微前端架构需求
- **目录结构清晰度**：检查文件组织是否便于理解和维护
- **内聚性评估**：分析相关功能是否合理聚合，避免过度分散
- **粒度适中性**：评估文件拆分粒度是否过细或过粗

### 3. 技术栈规范性检查
严格对照以下技术栈要求进行检查：
- **构建工具**：Vite 7.0.6 + TypeScript（检查版本一致性）
- **文档系统**：VitePress 2.0.0-alpha.8
- **测试框架**：Vitest 3.2.4
- **包管理器**：pnpm + monorepo 架构
- **版本管理**：统一版本号 0.1.0
- **发布配置**：npm @micro-core 组织
- **项目信息**：
  - author: "Echo <<EMAIL>>"
  - repository: "https://github.com/echo008/micro-core.git"

### 4. 代码规范性审查
- **命名规范**：检查文件名、目录名、函数名、变量名是否遵循统一的命名约定
- **代码注释**：评估注释的完整性、准确性和规范性
- **代码结构**：检查代码组织是否清晰、易读
- **类型定义**：检查 TypeScript 类型定义的完整性和准确性

### 5. 性能与质量评估
- **性能指标**：检查是否达到"开发功能特性.md"中提到的性能要求
- **测试覆盖率**：评估单元测试、集成测试、E2E测试的覆盖情况
- **构建配置**：检查各子包的构建配置是否统一和优化
- **依赖管理**：分析依赖版本是否统一，是否存在冗余依赖

## 输出要求

### 文档结构
1. **项目现状分析**：详细描述当前项目状态
2. **问题识别**：列出发现的所有问题，按优先级分类
3. **优化建议**：针对每个问题提供具体的解决方案
4. **实施计划**：提供分阶段的优化实施建议

### 分析方法
- 使用 codebase-retrieval 工具深入分析项目文件结构
- 使用 view 工具检查具体文件内容
- 使用 diagnostics 工具检查代码质量问题
- 对比"开发功能特性.md"中的要求与实际实现

### 质量要求
- **基于事实**：所有分析和建议必须基于实际的项目文件内容，严禁虚构
- **具体可行**：每个优化建议都要提供具体的实施步骤
- **优先级明确**：按照影响程度和实施难度对建议进行优先级排序
- **全面覆盖**：确保涵盖上述所有分析维度

请开始进行全面的项目分析，并创建详细的优化建议文档。






/项目优化建议.md
/packages/sidecar/
/packages/shared
请严格遵循"项目优化建议.md"文档中的要求和规范，对当前微前端项目进行全面深度重构优化。

## 核心重构目标

### 1. 重新设计shared目录架构
- **迁移范围**：将所有子包中的公共工具函数、类型定义、常量配置迁移至packages/shared/
- **整合要求**：
- 创建统一的工具函数库（utils/）
- 建立通用类型定义系统（types/）
- 整合公共常量和配置（constants/、config/）
- **架构目标**：确保shared目录成为项目的公共基础设施层，为所有子包提供统一的基础能力

### 2. 保证微前端核心功能完整性
必须包含以下完整功能模块（不可删除或简化）：
- **核心运行时系统**：应用加载、生命周期管理、状态管理
- **沙箱隔离系统**：JavaScript沙箱、CSS隔离、全局变量隔离
- **插件系统架构**：插件注册、生命周期、扩展机制
- **应用间通信系统**：事件总线、状态共享、消息传递
- **资源适配器系统**：静态资源加载、动态导入、缓存管理
- **Sidecar模式支持**：独立进程通信、服务代理
- **共享资源管理**：依赖共享、版本管理、冲突解决
- **多工程复用设计**：模块复用、配置复用、构建复用
- **构建工具适配**：Webpack、Vite、Rollup等构建工具支持
- **本地联调支持**：开发服务器、热更新、代理配置
- **性能优化实现**：懒加载、预加载、缓存策略
- **权限系统实现**：访问控制、角色管理、权限验证
- **错误处理与监控**：异常捕获、错误上报、性能监控
- **开发工具与调试**：调试面板、日志系统、性能分析

### 3. 构建工具统一化
- **清理目标**：移除所有tsup相关配置文件和依赖
- **统一标准**：所有子包统一使用Vite 7.0.6作为构建工具
- **配置要求**：创建统一的Vite配置模板，确保构建行为一致

### 4. 子包职责重新划分（核心重点）
- **packages/sidecar/**：
- 职责：一行代码接入

- **packages/shared/**：
- 职责：公共基础设施和工具库
- 包含：工具函数、类型定义、常量、通用组件
- 原则：为所有其他包提供基础能力支持

- **其他子包**：
- 职责：功能增强插件（如沙箱增强、通信增强、监控插件等）
- 原则：基于core的插件化扩展，职责单一、功能纯净
- 架构：遵循微内核+插件的设计模式

## 架构设计原则
- **目录架构（重点）**：确保目录架构标准、清晰、规范、扁平化（要求可以重新梳理和优化）
- **目录层级控制（重点）**：严格控制目录深度不超过3层，避免交叉引用、重复引用
- **单一职责原则**：每个文件、模块、包都有明确单一的职责
- **命名规范统一**：
- 目录名：kebab-case（如micro-core、shared-utils）
- 文件名：camelCase或kebab-case保持一致
- 函数名：camelCase
- 类型名：PascalCase
- **依赖关系清晰**：避免循环依赖，建立清晰的依赖层次

## 代码清理要求
- **冗余文件识别**：
- 重复的工具函数和类型定义
- 废弃的模块和组件
- 临时测试文件和调试代码
- 未使用的资源文件
- **清理执行**：
- 将清理的文件按原始目录结构迁移至`_backup/`目录
- 记录清理日志，说明每个文件的清理原因
- 确保清理后不影响核心功能

要求执行任务：根据上述要求，先深度、全面检查 sidecar 和 shared 两个子包所有文件，完成 sidecar 边车模式包的优化工作，公共工具、函数、常量等全部移动到 shared 包里面。
严禁幻觉，不要中断，一次性完成任务