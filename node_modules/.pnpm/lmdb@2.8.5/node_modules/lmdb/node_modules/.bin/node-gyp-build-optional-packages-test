#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node-gyp-build-optional-packages@5.1.1/node_modules/node-gyp-build-optional-packages/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node-gyp-build-optional-packages@5.1.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node-gyp-build-optional-packages@5.1.1/node_modules/node-gyp-build-optional-packages/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node-gyp-build-optional-packages@5.1.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node-gyp-build-optional-packages/build-test.js" "$@"
else
  exec node  "$basedir/../../../node-gyp-build-optional-packages/build-test.js" "$@"
fi
