#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules/npm/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules/npm/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules/npm/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules/npm/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/npm@10.9.3/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../npm/bin/npm-cli.js" "$@"
else
  exec node  "$basedir/../npm/bin/npm-cli.js" "$@"
fi
