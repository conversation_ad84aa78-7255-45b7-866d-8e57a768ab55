#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules/ua-parser-js/script/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules/ua-parser-js/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules/ua-parser-js/script/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules/ua-parser-js/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/ua-parser-js@0.7.40/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ua-parser-js/script/cli.js" "$@"
else
  exec node  "$basedir/../ua-parser-js/script/cli.js" "$@"
fi
