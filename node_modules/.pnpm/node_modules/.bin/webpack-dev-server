#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules/webpack-dev-server/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules/webpack-dev-server/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules/webpack-dev-server/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules/webpack-dev-server/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/webpack-dev-server@4.15.2_webpack@5.101.0/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" "$@"
else
  exec node  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" "$@"
fi
