#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/git-semver-tags@7.0.1/node_modules/git-semver-tags/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/git-semver-tags@7.0.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/git-semver-tags@7.0.1/node_modules/git-semver-tags/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/git-semver-tags@7.0.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../git-semver-tags/cli.mjs" "$@"
else
  exec node  "$basedir/../git-semver-tags/cli.mjs" "$@"
fi
