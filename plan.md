# micro-core 微前端框架开发计划

## Notes
- 遵循“项目结构梳理.md”目录结构，严格执行阶段性开发任务。
- 微内核架构，核心包体积 <15KB，插件化扩展。
- 6种沙箱策略，API 兼容 qiankun、wujie、micro-app。
- 技术栈：Vite 7.x, TypeScript 5.7.x, Vitest 3.x, pnpm + monorepo。
- 测试覆盖率 >90%，TypeScript 覆盖 100%。
- 逐步推进各阶段任务，严禁跳步或遗漏。
- 已完成 Monorepo 架构初始化与基础设施搭建，项目结构与工具链齐全。

## Task List
- [/] 第一阶段：项目基础设施（1-2周）
  - [x] Monorepo 架构初始化（根目录、pnpm 工作空间、TS 配置、Vite、Vitest、ESLint/Prettier）
  - [/] 构建工具链配置（Vite 构建、Tree-shaking、开发服务器）
- [ ] 第二阶段：核心运行时开发（3-4周）
  - [ ] 微内核核心包（@micro-core/core）：生命周期调度器、插件管理器、应用注册表
  - [ ] 共享基础设施包（@micro-core/shared）：9个工具模块与错误处理机制
- [ ] 第三阶段：沙箱系统开发（5-6周）
  - [ ] 6种沙箱策略（Proxy、DefineProperty、iframe、WebComponent、Namespace、Federation）
  - [ ] 沙箱管理器与多层隔离
- [ ] 第四阶段：插件系统开发（7-8周）
  - [ ] 插件基础设施与核心插件（Router、Communication、Auth、DevTools、Performance、ErrorHandler）
- [ ] 第五阶段：应用间通信系统（9-10周）
  - [ ] 事件总线与消息通道
- [ ] 第六阶段：框架适配器系统（11-12周）
  - [ ] 抽象适配器与9种框架适配器
- [ ] 第七阶段：构建工具适配（13-14周）
  - [ ] 7种构建工具适配器
- [ ] 第八阶段：Sidecar模式实现（15-16周）
  - [ ] Sidecar 核心与自动发现/零配置启动
- [ ] 第九阶段：兼容模式开发（17-18周）
  - [ ] 兼容适配器（qiankun、wujie、micro-app）
- [ ] 第十阶段：性能优化系统（19-20周）
  - [ ] 性能监控、内存管理、预加载、缓存优化
- [ ] 第十一阶段：权限管理系统（21周）
  - [ ] 权限管理器、路由/资源守卫、JWT/OAuth
- [ ] 第十二阶段：开发工具（22周）
  - [ ] 微前端调试器、开发服务器、调试面板、应用树视图
- [ ] 第十三阶段：资源管理系统（23周）
  - [ ] 动态导入、模块加载器、版本控制、缓存策略
- [ ] 第十四阶段：CLI命令行工具（24周）
  - [ ] CLI 工具及智能功能
- [ ] 第十五阶段：示例项目开发（25周）
  - [ ] Vue3 基座 + 4 种子应用
- [ ] 第十六阶段：文档系统（26周）
  - [ ] VitePress 文档（中英文、主题切换、API 自动生成）
- [ ] 第十七阶段：测试体系（贯穿全程）
  - [ ] 单元、集成、E2E、性能测试
- [ ] 第十八阶段：CI/CD和发布（最后1周）
  - [ ] 自动化流程、GitHub Actions、文档部署
