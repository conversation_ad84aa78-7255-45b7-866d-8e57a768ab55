/**
 * 消息系统类型定义
 */

// 消息类型枚举
export enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  NOTIFICATION = 'notification',
  BROADCAST = 'broadcast',
  ERROR = 'error'
}

// 消息优先级
export enum MessagePriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  URGENT = 3
}

// 消息状态
export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  ACKNOWLEDGED = 'acknowledged',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

// 消息头部信息
export interface MessageHeader {
  id: string
  type: MessageType
  priority: MessagePriority
  timestamp: number
  source: string
  target?: string
  correlationId?: string
  replyTo?: string
  ttl?: number
  retryCount?: number
  metadata?: Record<string, any>
}

// 消息体
export interface MessagePayload<T = any> {
  data: T
  schema?: string
  encoding?: string
  compression?: string
}

// 完整消息结构
export interface Message<T = any> {
  header: MessageHeader
  payload: MessagePayload<T>
  status?: MessageStatus
  error?: Error
}

// 消息过滤器
export type MessageFilter = (message: Message) => boolean

// 消息处理器
export type MessageHandler<T = any> = (message: Message<T>) => void | Promise<void>

// 消息中间件
export type MessageMiddleware = (message: Message, next: () => void) => void | Promise<void>

// 消息配置
export interface MessageConfig {
  maxRetries?: number
  timeout?: number
  enablePersistence?: boolean
  enableCompression?: boolean
  enableEncryption?: boolean
  batchSize?: number
  flushInterval?: number
}