/**
 * 通信通道类型定义
 */

import type { Message, MessageHandler, MessageFilter } from './message'

// 通道类型枚举
export enum ChannelType {
  MEMORY = 'memory',
  BROADCAST = 'broadcast',
  SHARED_WORKER = 'shared_worker',
  POST_MESSAGE = 'post_message',
  WEBSOCKET = 'websocket',
  WEBRTC = 'webrtc'
}

// 通道状态
export enum ChannelStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
  CLOSED = 'closed'
}

// 通道配置
export interface ChannelConfig {
  type: ChannelType
  name: string
  autoReconnect?: boolean
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  enableCompression?: boolean
  enableEncryption?: boolean
  bufferSize?: number
  metadata?: Record<string, any>
}

// 通道统计信息
export interface ChannelStats {
  status: ChannelStatus
  connectedAt?: number
  lastMessageAt?: number
  messagesSent: number
  messagesReceived: number
  bytesTransferred: number
  errorCount: number
  reconnectCount: number
}

// 通道事件
export interface ChannelEvents {
  connect: () => void
  disconnect: (reason?: string) => void
  error: (error: Error) => void
  message: (message: Message) => void
  reconnect: (attempt: number) => void
}

// 通道接口
export interface Channel {
  readonly id: string
  readonly config: ChannelConfig
  readonly status: ChannelStatus
  readonly stats: ChannelStats

  // 生命周期方法
  connect(): Promise<void>
  disconnect(): Promise<void>
  reconnect(): Promise<void>

  // 消息方法
  send(message: Message): Promise<void>
  subscribe(handler: MessageHandler): string
  unsubscribe(subscriptionId: string): void
  addFilter(filter: MessageFilter): string
  removeFilter(filterId: string): void

  // 事件方法
  on<K extends keyof ChannelEvents>(event: K, handler: ChannelEvents[K]): void
  off<K extends keyof ChannelEvents>(event: K, handler: ChannelEvents[K]): void
  emit<K extends keyof ChannelEvents>(event: K, ...args: Parameters<ChannelEvents[K]>): void

  // 工具方法
  isConnected(): boolean
  getStats(): ChannelStats
  destroy(): Promise<void>
}

// 通道工厂配置
export interface ChannelFactoryConfig {
  defaultType?: ChannelType
  enablePooling?: boolean
  maxPoolSize?: number
  enableMetrics?: boolean
}

// 通道管理器接口
export interface ChannelManager {
  createChannel(config: ChannelConfig): Promise<Channel>
  getChannel(id: string): Channel | undefined
  getAllChannels(): Channel[]
  destroyChannel(id: string): Promise<void>
  destroyAllChannels(): Promise<void>
}