/**
 * 通信系统类型定义
 */

export * from './event'
export * from './message'
export * from './channel'

// 通信管理器相关类型
export interface CommunicationManagerOptions {
  maxListeners?: number
  enableWildcard?: boolean
  defaultChannels?: ChannelConfig[]
}

export interface ChannelConfig {
  id: string
  type: ChannelType
  options?: MessageChannelOptions
}

export type ChannelType = 'broadcast' | 'postmessage' | 'worker'

export interface MessageChannelOptions {
  channelName?: string
  targetWindow?: Window
  targetOrigin?: string
  workerScript?: string
  timeout?: number
  retries?: number
  [key: string]: any
}

// 状态管理相关类型
export interface CommunicationState {
  isInitialized: boolean
  activeChannels: number
  totalMessages: number
  errors: ErrorInfo[]
  channels?: string[]
  eventBusStats?: EventBusStats
}

export interface ErrorInfo {
  id?: string
  error: any
  code: string
  context?: any
  timestamp: number
}

export interface EventBusStats {
  totalEvents: number
  activeSubscriptions: number
  eventTypes: string[]
}

export interface StateSnapshot {
  id: string
  timestamp: number
  state: CommunicationState
  changes: Record<string, any>
}

export interface StateMetrics {
  uptime: number
  messageRate: number
  errorRate: number
  channelUtilization: number
  memoryUsage: number
  performance: number
}

export type StateChangeListener = (
  currentState: CommunicationState,
  previousState: CommunicationState,
  changes: Record<string, any>
) => void

// 中间件相关类型
export interface MessageData {
  channelId?: string
  targetId?: string
  event?: string
  data?: any
  timestamp?: number
  [key: string]: any
}

export interface MiddlewareContext {
  channelId?: string
  userId?: string
  sessionId?: string
  metadata?: Record<string, any>
  [key: string]: any
}

export type MiddlewareFunction = (
  type: 'send' | 'receive',
  data: MessageData,
  context?: MiddlewareContext,
  next?: (data?: MessageData) => Promise<MessageData>
) => Promise<MessageData>

export interface MiddlewareChain {
  execute(data: MessageData): Promise<MessageData>
}

// 通道状态类型
export interface ChannelStatus {
  connected: boolean
  lastActivity: number
  messageCount: number
  errorCount: number
}

// 通信统计类型
export interface CommunicationStats {
  totalChannels: number
  activeChannels: number
  totalMessages: number
  messagesPerSecond: number
  errorRate: number
  averageLatency: number
  uptime: number
}

// 通信配置类型
export interface CommunicationConfig {
  enableLogging?: boolean
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  maxRetries?: number
  timeout?: number
  heartbeatInterval?: number
  reconnectDelay?: number
  maxReconnectAttempts?: number
}