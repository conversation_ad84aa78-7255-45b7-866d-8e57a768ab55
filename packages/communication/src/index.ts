/**
 * @micro-core/communication
 * 应用间通信系统
 */

// 事件总线
export { EventBus } from './event-bus/event-bus'
export { SubscriberManager } from './event-bus/subscriber'
export { PublisherManager } from './event-bus/publisher'

// 消息通道
export { MessageChannel } from './message/channel'
export { BroadcastChannelAdapter, PostMessageChannel } from './message/broadcast'
export { SharedWorkerChannel } from './message/worker'

// 通信管理器
export { CommunicationManager } from './manager/communication-manager'
export { StateManager } from './manager/state'
export { MiddlewareManager, builtinMiddlewares } from './manager/middleware'

// 类型定义
export type * from './types'

// 默认导出
export { CommunicationManager as default } from './manager/communication-manager'

/**
 * 创建通信管理器实例
 */
export function createCommunicationManager(options?: import('./types').CommunicationManagerOptions) {
  return new CommunicationManager(options)
}

/**
 * 创建事件总线实例
 */
export function createEventBus(options?: import('./types').EventBusOptions) {
  return new EventBus(options)
}

/**
 * 创建中间件管理器实例
 */
export function createMiddlewareManager() {
  return new MiddlewareManager()
}

/**
 * 版本信息
 */
export const version = '0.1.0'