/**
 * 订阅者管理器
 * 管理事件订阅的生命周期和状态
 */

import type { EventHandler, EventSubscription, EventSubscriptionOptions } from '../types/event'
import { MicroCoreError } from '@micro-core/shared'

export interface SubscriberInfo {
  id: string
  appId: string
  subscriptions: EventSubscription[]
  createdAt: number
  lastActiveAt: number
  isActive: boolean
}

export class SubscriberManager {
  private readonly subscribers = new Map<string, SubscriberInfo>()
  private readonly subscriptionToSubscriber = new Map<string, string>()
  private subscriberIdCounter = 0

  /**
   * 注册订阅者
   */
  registerSubscriber(appId: string): string {
    const subscriberId = this.generateSubscriberId()
    
    const subscriberInfo: SubscriberInfo = {
      id: subscriberId,
      appId,
      subscriptions: [],
      createdAt: Date.now(),
      lastActiveAt: Date.now(),
      isActive: true
    }

    this.subscribers.set(subscriberId, subscriberInfo)
    return subscriberId
  }

  /**
   * 注销订阅者
   */
  unregisterSubscriber(subscriberId: string): boolean {
    const subscriber = this.subscribers.get(subscriberId)
    if (!subscriber) {
      return false
    }

    // 清理所有订阅映射
    subscriber.subscriptions.forEach(subscription => {
      this.subscriptionToSubscriber.delete(subscription.id)
    })

    this.subscribers.delete(subscriberId)
    return true
  }

  /**
   * 添加订阅到订阅者
   */
  addSubscription(subscriberId: string, subscription: EventSubscription): void {
    const subscriber = this.subscribers.get(subscriberId)
    if (!subscriber) {
      throw new MicroCoreError(
        `Subscriber not found: ${subscriberId}`,
        'SUBSCRIBER_NOT_FOUND'
      )
    }

    subscriber.subscriptions.push(subscription)
    subscriber.lastActiveAt = Date.now()
    this.subscriptionToSubscriber.set(subscription.id, subscriberId)
  }

  /**
   * 从订阅者移除订阅
   */
  removeSubscription(subscriptionId: string): boolean {
    const subscriberId = this.subscriptionToSubscriber.get(subscriptionId)
    if (!subscriberId) {
      return false
    }

    const subscriber = this.subscribers.get(subscriberId)
    if (!subscriber) {
      return false
    }

    const index = subscriber.subscriptions.findIndex(sub => sub.id === subscriptionId)
    if (index === -1) {
      return false
    }

    subscriber.subscriptions.splice(index, 1)
    subscriber.lastActiveAt = Date.now()
    this.subscriptionToSubscriber.delete(subscriptionId)
    return true
  }

  /**
   * 获取订阅者信息
   */
  getSubscriber(subscriberId: string): SubscriberInfo | undefined {
    return this.subscribers.get(subscriberId)
  }

  /**
   * 获取所有订阅者
   */
  getAllSubscribers(): SubscriberInfo[] {
    return Array.from(this.subscribers.values())
  }

  /**
   * 获取活跃订阅者
   */
  getActiveSubscribers(): SubscriberInfo[] {
    return Array.from(this.subscribers.values()).filter(sub => sub.isActive)
  }

  /**
   * 根据应用ID获取订阅者
   */
  getSubscribersByAppId(appId: string): SubscriberInfo[] {
    return Array.from(this.subscribers.values()).filter(sub => sub.appId === appId)
  }

  /**
   * 设置订阅者状态
   */
  setSubscriberStatus(subscriberId: string, isActive: boolean): boolean {
    const subscriber = this.subscribers.get(subscriberId)
    if (!subscriber) {
      return false
    }

    subscriber.isActive = isActive
    subscriber.lastActiveAt = Date.now()
    return true
  }

  /**
   * 更新订阅者活跃时间
   */
  updateLastActive(subscriberId: string): boolean {
    const subscriber = this.subscribers.get(subscriberId)
    if (!subscriber) {
      return false
    }

    subscriber.lastActiveAt = Date.now()
    return true
  }

  /**
   * 清理非活跃订阅者
   */
  cleanupInactiveSubscribers(maxInactiveTime: number = 300000): number {
    const now = Date.now()
    let cleanedCount = 0

    for (const [subscriberId, subscriber] of this.subscribers.entries()) {
      if (!subscriber.isActive || (now - subscriber.lastActiveAt) > maxInactiveTime) {
        this.unregisterSubscriber(subscriberId)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  /**
   * 获取订阅统计信息
   */
  getStats() {
    const subscribers = Array.from(this.subscribers.values())
    const totalSubscriptions = subscribers.reduce((sum, sub) => sum + sub.subscriptions.length, 0)
    
    return {
      totalSubscribers: subscribers.length,
      activeSubscribers: subscribers.filter(sub => sub.isActive).length,
      totalSubscriptions,
      averageSubscriptionsPerSubscriber: subscribers.length > 0 ? totalSubscriptions / subscribers.length : 0,
      oldestSubscriber: Math.min(...subscribers.map(sub => sub.createdAt)),
      newestSubscriber: Math.max(...subscribers.map(sub => sub.createdAt))
    }
  }

  /**
   * 清除所有订阅者
   */
  clear(): void {
    this.subscribers.clear()
    this.subscriptionToSubscriber.clear()
  }

  private generateSubscriberId(): string {
    return `subscriber_${++this.subscriberIdCounter}_${Date.now()}`
  }
}