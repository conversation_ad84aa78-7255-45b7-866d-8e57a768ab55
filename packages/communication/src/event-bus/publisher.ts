/**
 * 发布者管理
 * 负责事件的发布和分发
 */

import type { EventBusOptions, EventSubscription } from '../types'
import { MicroCoreError, logger } from '@micro-core/shared'

export class Publisher {
  constructor(
    private subscribers: Map<string, Set<EventSubscription>>,
    private middlewares: Array<(event: string, data: any) => any>,
    private options: Required<EventBusOptions>
  ) {}

  /**
   * 发布事件
   */
  public publish(event: string, data?: any): void {
    this.validateEvent(event)

    try {
      // 应用中间件
      const processedData = this.applyMiddlewares(event, data)
      
      // 获取匹配的订阅者
      const matchedSubscriptions = this.getMatchedSubscriptions(event)
      
      // 分发事件
      this.dispatchEvent(event, processedData, matchedSubscriptions)
    } catch (error) {
      logger.error('事件发布失败', { event, error })
      throw new MicroCoreError(
        `事件发布失败: ${error instanceof Error ? error.message : String(error)}`,
        'EVENT_PUBLISH_FAILED',
        { event, originalError: error }
      )
    }
  }

  /**
   * 异步发布事件
   */
  public async publishAsync(event: string, data?: any): Promise<void> {
    if (!this.options.enableAsync) {
      throw new MicroCoreError('异步事件发布未启用', 'ASYNC_DISABLED')
    }

    this.validateEvent(event)

    try {
      // 应用中间件
      const processedData = this.applyMiddlewares(event, data)
      
      // 获取匹配的订阅者
      const matchedSubscriptions = this.getMatchedSubscriptions(event)
      
      // 异步分发事件
      await this.dispatchEventAsync(event, processedData, matchedSubscriptions)
    } catch (error) {
      logger.error('异步事件发布失败', { event, error })
      throw new MicroCoreError(
        `异步事件发布失败: ${error instanceof Error ? error.message : String(error)}`,
        'ASYNC_EVENT_PUBLISH_FAILED',
        { event, originalError: error }
      )
    }
  }

  /**
   * 应用中间件
   */
  private applyMiddlewares(event: string, data: any): any {
    if (!this.options.enableMiddleware || this.middlewares.length === 0) {
      return data
    }

    let processedData = data
    for (const middleware of this.middlewares) {
      try {
        processedData = middleware(event, processedData)
      } catch (error) {
        logger.warn('中间件执行失败', { event, error })
        // 中间件失败不应该阻止事件发布
      }
    }

    return processedData
  }

  /**
   * 获取匹配的订阅者
   */
  private getMatchedSubscriptions(event: string): EventSubscription[] {
    const matched: EventSubscription[] = []

    // 精确匹配
    const exactSubscriptions = this.subscribers.get(event)
    if (exactSubscriptions) {
      matched.push(...Array.from(exactSubscriptions))
    }

    // 通配符匹配
    if (this.options.enableWildcard) {
      for (const [subscribedEvent, subscriptions] of this.subscribers) {
        if (subscribedEvent !== event && this.isWildcardMatch(event, subscribedEvent)) {
          matched.push(...Array.from(subscriptions))
        }
      }
    }

    return matched
  }

  /**
   * 分发事件
   */
  private dispatchEvent(event: string, data: any, subscriptions: EventSubscription[]): void {
    const toRemove: EventSubscription[] = []

    for (const subscription of subscriptions) {
      try {
        // 执行事件处理器
        subscription.handler(data, event)

        // 如果是一次性订阅，标记为待移除
        if (subscription.once) {
          toRemove.push(subscription)
        }
      } catch (error) {
        logger.error('事件处理器执行失败', { 
          event, 
          subscriptionId: subscription.id, 
          error 
        })
        // 处理器失败不应该影响其他处理器
      }
    }

    // 移除一次性订阅
    this.removeSubscriptions(toRemove)
  }

  /**
   * 异步分发事件
   */
  private async dispatchEventAsync(event: string, data: any, subscriptions: EventSubscription[]): Promise<void> {
    const toRemove: EventSubscription[] = []
    const promises: Promise<void>[] = []

    for (const subscription of subscriptions) {
      const promise = this.executeHandlerAsync(subscription, data, event)
        .then(() => {
          // 如果是一次性订阅，标记为待移除
          if (subscription.once) {
            toRemove.push(subscription)
          }
        })
        .catch((error) => {
          logger.error('异步事件处理器执行失败', { 
            event, 
            subscriptionId: subscription.id, 
            error 
          })
        })

      promises.push(promise)
    }

    // 等待所有处理器执行完成
    await Promise.allSettled(promises)

    // 移除一次性订阅
    this.removeSubscriptions(toRemove)
  }

  /**
   * 异步执行事件处理器
   */
  private async executeHandlerAsync(subscription: EventSubscription, data: any, event: string): Promise<void> {
    const result = subscription.handler(data, event)
    
    // 如果处理器返回 Promise，等待其完成
    if (result && typeof result.then === 'function') {
      await result
    }
  }

  /**
   * 移除订阅
   */
  private removeSubscriptions(subscriptions: EventSubscription[]): void {
    for (const subscription of subscriptions) {
      const eventSubscriptions = this.subscribers.get(subscription.event)
      if (eventSubscriptions) {
        eventSubscriptions.delete(subscription)
        
        // 如果没有订阅者了，删除事件
        if (eventSubscriptions.size === 0) {
          this.subscribers.delete(subscription.event)
        }
      }
    }
  }

  /**
   * 通配符匹配
   */
  private isWildcardMatch(event: string, pattern: string): boolean {
    if (!pattern.includes('*')) {
      return false
    }

    // 将通配符模式转换为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
    
    const regex = new RegExp(`^${regexPattern}$`)
    return regex.test(event)
  }

  /**
   * 验证事件名称
   */
  private validateEvent(event: string): void {
    if (!event || typeof event !== 'string') {
      throw new MicroCoreError('事件名称必须是非空字符串', 'INVALID_EVENT_NAME')
    }
  }
}