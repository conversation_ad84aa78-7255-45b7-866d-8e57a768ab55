/**
 * 事件总线实现
 * 高性能的发布订阅模式，支持通配符、异步处理、中间件等特性
 */

import type {
  EventHandler,
  EventContext,
  EventData,
  EventSubscription,
  EventSubscriptionOptions,
  EventBusConfig,
  EventStats
} from '../types/event'
import { MicroCoreError } from '@micro-core/shared'

export class EventBus {
  private readonly config: Required<EventBusConfig>
  private readonly subscriptions = new Map<string, EventSubscription[]>()
  private readonly wildcardSubscriptions = new Map<string, EventSubscription[]>()
  private readonly stats: EventStats = {
    totalEvents: 0,
    eventsByType: {},
    averageProcessingTime: 0,
    errorRate: 0,
    lastEventTime: 0
  }
  private subscriptionIdCounter = 0

  constructor(config: EventBusConfig = {}) {
    this.config = {
      maxListeners: config.maxListeners ?? 100,
      enableWildcard: config.enableWildcard ?? true,
      enableAsync: config.enableAsync ?? true,
      timeout: config.timeout ?? 5000,
      enableDebug: config.enableDebug ?? false
    }
  }

  /**
   * 订阅事件
   */
  subscribe<T = any>(
    eventName: string,
    handler: EventHandler<T>,
    options: EventSubscriptionOptions = {}
  ): string {
    this.validateEventName(eventName)
    this.validateHandler(handler)

    const subscription: EventSubscription = {
      id: this.generateSubscriptionId(),
      eventName,
      handler: handler as EventHandler,
      options: {
        once: options.once ?? false,
        priority: options.priority ?? 0,
        timeout: options.timeout ?? this.config.timeout,
        filter: options.filter
      },
      createdAt: Date.now()
    }

    // 检查监听器数量限制
    const existingSubscriptions = this.getSubscriptionsForEvent(eventName)
    if (existingSubscriptions.length >= this.config.maxListeners) {
      throw new MicroCoreError(
        `Maximum listeners (${this.config.maxListeners}) exceeded for event: ${eventName}`,
        'EVENT_MAX_LISTENERS_EXCEEDED'
      )
    }

    // 添加订阅
    if (this.isWildcardPattern(eventName)) {
      if (!this.config.enableWildcard) {
        throw new MicroCoreError('Wildcard patterns are disabled', 'EVENT_WILDCARD_DISABLED')
      }
      this.addWildcardSubscription(subscription)
    } else {
      this.addDirectSubscription(subscription)
    }

    this.debugLog(`Subscribed to event: ${eventName}, subscription ID: ${subscription.id}`)
    return subscription.id
  }

  /**
   * 取消订阅
   */
  unsubscribe(subscriptionId: string): boolean {
    // 从直接订阅中移除
    for (const [eventName, subscriptions] of this.subscriptions.entries()) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId)
      if (index !== -1) {
        subscriptions.splice(index, 1)
        if (subscriptions.length === 0) {
          this.subscriptions.delete(eventName)
        }
        this.debugLog(`Unsubscribed from event: ${eventName}, subscription ID: ${subscriptionId}`)
        return true
      }
    }

    // 从通配符订阅中移除
    for (const [pattern, subscriptions] of this.wildcardSubscriptions.entries()) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId)
      if (index !== -1) {
        subscriptions.splice(index, 1)
        if (subscriptions.length === 0) {
          this.wildcardSubscriptions.delete(pattern)
        }
        this.debugLog(`Unsubscribed from wildcard pattern: ${pattern}, subscription ID: ${subscriptionId}`)
        return true
      }
    }

    return false
  }

  /**
   * 发布事件
   */
  async publish<T = any>(eventName: string, data: T, context?: Partial<EventContext>): Promise<void> {
    this.validateEventName(eventName)

    const startTime = performance.now()
    const eventContext: EventContext = {
      eventName,
      timestamp: Date.now(),
      source: 'event-bus',
      ...context
    }

    const eventData: EventData<T> = {
      type: eventName,
      payload: data,
      context: eventContext
    }

    try {
      // 获取所有匹配的订阅
      const matchingSubscriptions = this.getMatchingSubscriptions(eventName)
      
      if (matchingSubscriptions.length === 0) {
        this.debugLog(`No subscribers for event: ${eventName}`)
        return
      }

      // 按优先级排序
      matchingSubscriptions.sort((a, b) => (b.options.priority ?? 0) - (a.options.priority ?? 0))

      // 执行处理器
      const promises: Promise<void>[] = []
      const subscriptionsToRemove: EventSubscription[] = []

      for (const subscription of matchingSubscriptions) {
        // 应用过滤器
        if (subscription.options.filter && !subscription.options.filter(data)) {
          continue
        }

        // 创建处理器执行 Promise
        const handlerPromise = this.executeHandler(subscription, eventData)
        
        if (this.config.enableAsync) {
          promises.push(handlerPromise)
        } else {
          await handlerPromise
        }

        // 标记一次性订阅待移除
        if (subscription.options.once) {
          subscriptionsToRemove.push(subscription)
        }
      }

      // 等待所有异步处理器完成
      if (this.config.enableAsync && promises.length > 0) {
        await Promise.allSettled(promises)
      }

      // 移除一次性订阅
      subscriptionsToRemove.forEach(subscription => {
        this.unsubscribe(subscription.id)
      })

      // 更新统计信息
      this.updateStats(eventName, performance.now() - startTime, false)
      this.debugLog(`Published event: ${eventName}, ${matchingSubscriptions.length} handlers executed`)

    } catch (error) {
      this.updateStats(eventName, performance.now() - startTime, true)
      throw new MicroCoreError(
        `Failed to publish event: ${eventName}`,
        'EVENT_PUBLISH_FAILED',
        { originalError: error }
      )
    }
  }

  /**
   * 获取事件统计信息
   */
  getStats(): EventStats {
    return { ...this.stats }
  }

  /**
   * 清除所有订阅
   */
  clear(): void {
    this.subscriptions.clear()
    this.wildcardSubscriptions.clear()
    this.debugLog('All subscriptions cleared')
  }

  /**
   * 获取所有事件名称
   */
  getEventNames(): string[] {
    return Array.from(this.subscriptions.keys())
  }

  /**
   * 获取事件的订阅数量
   */
  getSubscriptionCount(eventName: string): number {
    return this.getSubscriptionsForEvent(eventName).length
  }

  /**
   * 销毁事件总线
   */
  destroy(): void {
    this.clear()
    this.debugLog('EventBus destroyed')
  }

  // 私有方法

  private validateEventName(eventName: string): void {
    if (!eventName || typeof eventName !== 'string') {
      throw new MicroCoreError('Event name must be a non-empty string', 'EVENT_INVALID_NAME')
    }
  }

  private validateHandler(handler: EventHandler): void {
    if (typeof handler !== 'function') {
      throw new MicroCoreError('Event handler must be a function', 'EVENT_INVALID_HANDLER')
    }
  }

  private generateSubscriptionId(): string {
    return `sub_${++this.subscriptionIdCounter}_${Date.now()}`
  }

  private isWildcardPattern(eventName: string): boolean {
    return eventName.includes('*') || eventName.includes('?')
  }

  private addDirectSubscription(subscription: EventSubscription): void {
    const subscriptions = this.subscriptions.get(subscription.eventName) ?? []
    subscriptions.push(subscription)
    this.subscriptions.set(subscription.eventName, subscriptions)
  }

  private addWildcardSubscription(subscription: EventSubscription): void {
    const subscriptions = this.wildcardSubscriptions.get(subscription.eventName) ?? []
    subscriptions.push(subscription)
    this.wildcardSubscriptions.set(subscription.eventName, subscriptions)
  }

  private getSubscriptionsForEvent(eventName: string): EventSubscription[] {
    return this.subscriptions.get(eventName) ?? []
  }

  private getMatchingSubscriptions(eventName: string): EventSubscription[] {
    const directSubscriptions = this.getSubscriptionsForEvent(eventName)
    
    if (!this.config.enableWildcard) {
      return directSubscriptions
    }

    const wildcardSubscriptions: EventSubscription[] = []
    for (const [pattern, subscriptions] of this.wildcardSubscriptions.entries()) {
      if (this.matchesWildcardPattern(eventName, pattern)) {
        wildcardSubscriptions.push(...subscriptions)
      }
    }

    return [...directSubscriptions, ...wildcardSubscriptions]
  }

  private matchesWildcardPattern(eventName: string, pattern: string): boolean {
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
    const regex = new RegExp(`^${regexPattern}$`)
    return regex.test(eventName)
  }

  private async executeHandler(subscription: EventSubscription, eventData: EventData): Promise<void> {
    const timeout = subscription.options.timeout ?? this.config.timeout

    try {
      const handlerPromise = Promise.resolve(subscription.handler(eventData.payload, eventData.context))
      
      if (timeout > 0) {
        await Promise.race([
          handlerPromise,
          new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Handler timeout')), timeout)
          })
        ])
      } else {
        await handlerPromise
      }
    } catch (error) {
      this.debugLog(`Handler error for event ${eventData.type}:`, error)
      throw error
    }
  }

  private updateStats(eventName: string, processingTime: number, hasError: boolean): void {
    this.stats.totalEvents++
    this.stats.eventsByType[eventName] = (this.stats.eventsByType[eventName] ?? 0) + 1
    this.stats.lastEventTime = Date.now()

    // 更新平均处理时间
    const totalTime = this.stats.averageProcessingTime * (this.stats.totalEvents - 1) + processingTime
    this.stats.averageProcessingTime = totalTime / this.stats.totalEvents

    // 更新错误率
    if (hasError) {
      const errorCount = Math.floor(this.stats.errorRate * (this.stats.totalEvents - 1)) + 1
      this.stats.errorRate = errorCount / this.stats.totalEvents
    } else {
      const errorCount = Math.floor(this.stats.errorRate * (this.stats.totalEvents - 1))
      this.stats.errorRate = errorCount / this.stats.totalEvents
    }
  }

  private debugLog(message: string, ...args: any[]): void {
    if (this.config.enableDebug) {
      console.log(`[EventBus] ${message}`, ...args)
    }
  }
}