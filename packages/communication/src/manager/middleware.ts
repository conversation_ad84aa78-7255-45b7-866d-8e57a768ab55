/**
 * 通信中间件系统
 * 提供消息处理的中间件机制
 */

import type { 
  MiddlewareFunction,
  MiddlewareContext,
  MiddlewareChain,
  MessageData
} from '../types'
import { MicroCoreError } from '@micro-core/shared'

export class MiddlewareManager {
  private middlewares: MiddlewareFunction[] = []
  private namedMiddlewares: Map<string, MiddlewareFunction> = new Map()

  /**
   * 添加中间件
   */
  use(middleware: MiddlewareFunction, name?: string): void {
    this.middlewares.push(middleware)
    
    if (name) {
      this.namedMiddlewares.set(name, middleware)
    }
  }

  /**
   * 在指定位置插入中间件
   */
  insertAt(index: number, middleware: MiddlewareFunction, name?: string): void {
    if (index < 0 || index > this.middlewares.length) {
      throw new MicroCoreError(
        `Invalid middleware index: ${index}`,
        'INVALID_MIDDLEWARE_INDEX'
      )
    }

    this.middlewares.splice(index, 0, middleware)
    
    if (name) {
      this.namedMiddlewares.set(name, middleware)
    }
  }

  /**
   * 在指定中间件之前插入
   */
  insertBefore(targetName: string, middleware: MiddlewareFunction, name?: string): void {
    const targetMiddleware = this.namedMiddlewares.get(targetName)
    if (!targetMiddleware) {
      throw new MicroCoreError(
        `Middleware ${targetName} not found`,
        'MIDDLEWARE_NOT_FOUND'
      )
    }

    const index = this.middlewares.indexOf(targetMiddleware)
    this.insertAt(index, middleware, name)
  }

  /**
   * 在指定中间件之后插入
   */
  insertAfter(targetName: string, middleware: MiddlewareFunction, name?: string): void {
    const targetMiddleware = this.namedMiddlewares.get(targetName)
    if (!targetMiddleware) {
      throw new MicroCoreError(
        `Middleware ${targetName} not found`,
        'MIDDLEWARE_NOT_FOUND'
      )
    }

    const index = this.middlewares.indexOf(targetMiddleware)
    this.insertAt(index + 1, middleware, name)
  }

  /**
   * 移除中间件
   */
  remove(middleware: MiddlewareFunction): boolean {
    const index = this.middlewares.indexOf(middleware)
    if (index > -1) {
      this.middlewares.splice(index, 1)
      
      // 从命名中间件中移除
      for (const [name, namedMiddleware] of this.namedMiddlewares) {
        if (namedMiddleware === middleware) {
          this.namedMiddlewares.delete(name)
          break
        }
      }
      
      return true
    }
    return false
  }

  /**
   * 根据名称移除中间件
   */
  removeByName(name: string): boolean {
    const middleware = this.namedMiddlewares.get(name)
    if (middleware) {
      this.namedMiddlewares.delete(name)
      return this.remove(middleware)
    }
    return false
  }

  /**
   * 清除所有中间件
   */
  clear(): void {
    this.middlewares = []
    this.namedMiddlewares.clear()
  }

  /**
   * 执行中间件链
   */
  async execute(
    type: 'send' | 'receive',
    data: MessageData,
    context: MiddlewareContext = {}
  ): Promise<MessageData> {
    if (this.middlewares.length === 0) {
      return data
    }

    const chain = this.createChain(type, context)
    return await chain.execute(data)
  }

  /**
   * 获取中间件列表
   */
  getMiddlewares(): MiddlewareFunction[] {
    return [...this.middlewares]
  }

  /**
   * 获取命名中间件
   */
  getNamedMiddleware(name: string): MiddlewareFunction | undefined {
    return this.namedMiddlewares.get(name)
  }

  /**
   * 获取中间件统计信息
   */
  getStats(): {
    total: number
    named: number
    names: string[]
  } {
    return {
      total: this.middlewares.length,
      named: this.namedMiddlewares.size,
      names: Array.from(this.namedMiddlewares.keys())
    }
  }

  /**
   * 创建中间件执行链
   */
  private createChain(type: 'send' | 'receive', context: MiddlewareContext): MiddlewareChain {
    return new MiddlewareChain(this.middlewares, type, context)
  }
}

/**
 * 中间件执行链
 */
class MiddlewareChain {
  private index = 0

  constructor(
    private middlewares: MiddlewareFunction[],
    private type: 'send' | 'receive',
    private context: MiddlewareContext
  ) {}

  /**
   * 执行中间件链
   */
  async execute(data: MessageData): Promise<MessageData> {
    return await this.next(data)
  }

  /**
   * 执行下一个中间件
   */
  private async next(data: MessageData): Promise<MessageData> {
    if (this.index >= this.middlewares.length) {
      return data
    }

    const middleware = this.middlewares[this.index++]
    
    try {
      return await middleware(this.type, data, this.context, (nextData?: MessageData) => {
        return this.next(nextData || data)
      })
    } catch (error) {
      throw new MicroCoreError(
        `Middleware execution failed: ${error}`,
        'MIDDLEWARE_EXECUTION_FAILED',
        { middlewareIndex: this.index - 1, type: this.type }
      )
    }
  }
}

/**
 * 内置中间件
 */
export const builtinMiddlewares = {
  /**
   * 日志中间件
   */
  logger: (options: { level?: 'debug' | 'info' | 'warn' | 'error' } = {}): MiddlewareFunction => {
    const level = options.level || 'info'
    
    return async (type, data, context, next) => {
      const startTime = Date.now()
      
      if (level === 'debug') {
        console.log(`[Communication] ${type.toUpperCase()}:`, data)
      }
      
      const result = await next(data)
      
      const duration = Date.now() - startTime
      if (level === 'debug' || level === 'info') {
        console.log(`[Communication] ${type.toUpperCase()} completed in ${duration}ms`)
      }
      
      return result
    }
  },

  /**
   * 数据验证中间件
   */
  validator: (schema: any): MiddlewareFunction => {
    return async (type, data, context, next) => {
      // 简单的数据验证示例
      if (schema.required && schema.required.length > 0) {
        for (const field of schema.required) {
          if (!(field in data)) {
            throw new MicroCoreError(
              `Required field ${field} is missing`,
              'VALIDATION_FAILED'
            )
          }
        }
      }
      
      return await next(data)
    }
  },

  /**
   * 数据转换中间件
   */
  transformer: (transformFn: (data: MessageData) => MessageData): MiddlewareFunction => {
    return async (type, data, context, next) => {
      const transformedData = transformFn(data)
      return await next(transformedData)
    }
  },

  /**
   * 错误处理中间件
   */
  errorHandler: (handler: (error: any, data: MessageData) => MessageData | null): MiddlewareFunction => {
    return async (type, data, context, next) => {
      try {
        return await next(data)
      } catch (error) {
        const recoveredData = handler(error, data)
        if (recoveredData) {
          return recoveredData
        }
        throw error
      }
    }
  },

  /**
   * 限流中间件
   */
  rateLimit: (options: { maxRequests: number; windowMs: number }): MiddlewareFunction => {
    const requests = new Map<string, number[]>()
    
    return async (type, data, context, next) => {
      const key = context.channelId || 'default'
      const now = Date.now()
      const windowStart = now - options.windowMs
      
      // 清理过期记录
      const requestTimes = requests.get(key) || []
      const validRequests = requestTimes.filter(time => time > windowStart)
      
      if (validRequests.length >= options.maxRequests) {
        throw new MicroCoreError(
          'Rate limit exceeded',
          'RATE_LIMIT_EXCEEDED'
        )
      }
      
      validRequests.push(now)
      requests.set(key, validRequests)
      
      return await next(data)
    }
  },

  /**
   * 缓存中间件
   */
  cache: (options: { ttl: number; maxSize?: number }): MiddlewareFunction => {
    const cache = new Map<string, { data: MessageData; expires: number }>()
    const maxSize = options.maxSize || 100
    
    return async (type, data, context, next) => {
      if (type === 'receive') {
        return await next(data)
      }
      
      const key = JSON.stringify(data)
      const cached = cache.get(key)
      
      if (cached && cached.expires > Date.now()) {
        return cached.data
      }
      
      const result = await next(data)
      
      // 缓存结果
      if (cache.size >= maxSize) {
        const firstKey = cache.keys().next().value
        cache.delete(firstKey)
      }
      
      cache.set(key, {
        data: result,
        expires: Date.now() + options.ttl
      })
      
      return result
    }
  }
}