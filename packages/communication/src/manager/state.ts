/**
 * 通信状态管理
 * 管理通信系统的状态和统计信息
 */

import type { 
  CommunicationState,
  StateSnapshot,
  StateChangeListener,
  StateMetrics
} from '../types'

export class StateManager {
  private state: CommunicationState
  private listeners: Set<StateChangeListener> = new Set()
  private snapshots: StateSnapshot[] = []
  private maxSnapshots: number = 50

  constructor(initialState?: Partial<CommunicationState>) {
    this.state = {
      isInitialized: false,
      activeChannels: 0,
      totalMessages: 0,
      errors: [],
      channels: [],
      eventBusStats: {
        totalEvents: 0,
        activeSubscriptions: 0,
        eventTypes: []
      },
      ...initialState
    }
  }

  /**
   * 获取当前状态
   */
  getState(): CommunicationState {
    return { ...this.state }
  }

  /**
   * 更新状态
   */
  updateState(updates: Partial<CommunicationState>): void {
    const previousState = { ...this.state }
    this.state = { ...this.state, ...updates }

    // 创建快照
    this.createSnapshot(previousState, this.state)

    // 通知监听器
    this.notifyListeners(previousState, this.state)
  }

  /**
   * 增加计数器
   */
  incrementCounter(key: keyof Pick<CommunicationState, 'activeChannels' | 'totalMessages'>): void {
    this.updateState({
      [key]: this.state[key] + 1
    })
  }

  /**
   * 减少计数器
   */
  decrementCounter(key: keyof Pick<CommunicationState, 'activeChannels'>): void {
    this.updateState({
      [key]: Math.max(0, this.state[key] - 1)
    })
  }

  /**
   * 添加错误
   */
  addError(error: any): void {
    const errors = [...this.state.errors, {
      ...error,
      id: Date.now().toString(),
      timestamp: Date.now()
    }]

    // 保持错误列表大小
    if (errors.length > 100) {
      errors.shift()
    }

    this.updateState({ errors })
  }

  /**
   * 清除错误
   */
  clearErrors(): void {
    this.updateState({ errors: [] })
  }

  /**
   * 添加通道
   */
  addChannel(channelId: string): void {
    if (!this.state.channels.includes(channelId)) {
      this.updateState({
        channels: [...this.state.channels, channelId],
        activeChannels: this.state.activeChannels + 1
      })
    }
  }

  /**
   * 移除通道
   */
  removeChannel(channelId: string): void {
    const channels = this.state.channels.filter(id => id !== channelId)
    this.updateState({
      channels,
      activeChannels: Math.max(0, this.state.activeChannels - 1)
    })
  }

  /**
   * 更新事件总线统计
   */
  updateEventBusStats(stats: CommunicationState['eventBusStats']): void {
    this.updateState({ eventBusStats: stats })
  }

  /**
   * 添加状态变化监听器
   */
  addListener(listener: StateChangeListener): () => void {
    this.listeners.add(listener)
    
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * 移除状态变化监听器
   */
  removeListener(listener: StateChangeListener): void {
    this.listeners.delete(listener)
  }

  /**
   * 获取状态快照
   */
  getSnapshots(): StateSnapshot[] {
    return [...this.snapshots]
  }

  /**
   * 获取最新快照
   */
  getLatestSnapshot(): StateSnapshot | null {
    return this.snapshots[this.snapshots.length - 1] || null
  }

  /**
   * 清除快照
   */
  clearSnapshots(): void {
    this.snapshots = []
  }

  /**
   * 获取状态指标
   */
  getMetrics(): StateMetrics {
    const now = Date.now()
    const recentSnapshots = this.snapshots.filter(
      snapshot => now - snapshot.timestamp < 60000 // 最近1分钟
    )

    const messageRates = recentSnapshots.map(snapshot => 
      snapshot.state.totalMessages
    )

    const avgMessageRate = messageRates.length > 1
      ? (messageRates[messageRates.length - 1] - messageRates[0]) / (messageRates.length - 1)
      : 0

    return {
      uptime: this.state.isInitialized ? now - (this.snapshots[0]?.timestamp || now) : 0,
      messageRate: avgMessageRate,
      errorRate: this.state.errors.length / Math.max(1, this.snapshots.length),
      channelUtilization: this.state.activeChannels > 0 
        ? this.state.totalMessages / this.state.activeChannels 
        : 0,
      memoryUsage: this.calculateMemoryUsage(),
      performance: this.calculatePerformanceScore()
    }
  }

  /**
   * 重置状态
   */
  reset(): void {
    this.state = {
      isInitialized: false,
      activeChannels: 0,
      totalMessages: 0,
      errors: [],
      channels: [],
      eventBusStats: {
        totalEvents: 0,
        activeSubscriptions: 0,
        eventTypes: []
      }
    }
    this.snapshots = []
    this.listeners.clear()
  }

  /**
   * 创建状态快照
   */
  private createSnapshot(previousState: CommunicationState, currentState: CommunicationState): void {
    const snapshot: StateSnapshot = {
      id: Date.now().toString(),
      timestamp: Date.now(),
      state: { ...currentState },
      changes: this.calculateChanges(previousState, currentState)
    }

    this.snapshots.push(snapshot)

    // 保持快照数量限制
    if (this.snapshots.length > this.maxSnapshots) {
      this.snapshots.shift()
    }
  }

  /**
   * 通知状态变化监听器
   */
  private notifyListeners(previousState: CommunicationState, currentState: CommunicationState): void {
    const changes = this.calculateChanges(previousState, currentState)
    
    if (Object.keys(changes).length > 0) {
      this.listeners.forEach(listener => {
        try {
          listener(currentState, previousState, changes)
        } catch (error) {
          console.error('State listener error:', error)
        }
      })
    }
  }

  /**
   * 计算状态变化
   */
  private calculateChanges(
    previousState: CommunicationState, 
    currentState: CommunicationState
  ): Record<string, any> {
    const changes: Record<string, any> = {}

    for (const key in currentState) {
      if (currentState[key as keyof CommunicationState] !== previousState[key as keyof CommunicationState]) {
        changes[key] = {
          from: previousState[key as keyof CommunicationState],
          to: currentState[key as keyof CommunicationState]
        }
      }
    }

    return changes
  }

  /**
   * 计算内存使用情况
   */
  private calculateMemoryUsage(): number {
    // 简单的内存使用估算
    const stateSize = JSON.stringify(this.state).length
    const snapshotsSize = this.snapshots.reduce((total, snapshot) => 
      total + JSON.stringify(snapshot).length, 0
    )
    
    return stateSize + snapshotsSize
  }

  /**
   * 计算性能分数
   */
  private calculatePerformanceScore(): number {
    const metrics = {
      errorRate: this.state.errors.length / Math.max(1, this.state.totalMessages),
      channelEfficiency: this.state.activeChannels > 0 ? 1 : 0,
      messageVolume: Math.min(1, this.state.totalMessages / 1000) // 标准化到0-1
    }

    // 简单的性能评分算法
    const score = (
      (1 - metrics.errorRate) * 0.4 +
      metrics.channelEfficiency * 0.3 +
      metrics.messageVolume * 0.3
    ) * 100

    return Math.max(0, Math.min(100, score))
  }
}