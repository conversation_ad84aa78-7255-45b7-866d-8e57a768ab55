/**
 * 通信管理器实现
 * 统一管理事件总线和消息通道
 */

import type { 
  CommunicationManagerOptions, 
  MessageHandler, 
  MessageData,
  EventHandler,
  CommunicationContext,
  MiddlewareFunction
} from '../types'
import { EventBus } from '../event-bus'
import { MessageChannel } from '../message'
import { CommunicationState } from './state'
import { CommunicationMiddleware } from './middleware'
import { MicroCoreError, logger } from '@micro-core/shared'

export class CommunicationManager {
  private eventBus: EventBus
  private messageChannel: MessageChannel
  private state: CommunicationState
  private middleware: CommunicationMiddleware
  private options: Required<CommunicationManagerOptions>

  constructor(options: CommunicationManagerOptions = {}) {
    this.options = {
      enableEventBus: true,
      enableMessageChannel: true,
      enableMiddleware: true,
      enableState: true,
      eventBusOptions: {},
      messageChannelOptions: {},
      ...options
    }

    this.state = new CommunicationState()
    this.middleware = new CommunicationMiddleware()
    
    // 初始化事件总线
    if (this.options.enableEventBus) {
      this.eventBus = new EventBus(this.options.eventBusOptions)
    }

    // 初始化消息通道
    if (this.options.enableMessageChannel) {
      this.messageChannel = new MessageChannel(this.options.messageChannelOptions)
    }

    logger.info('通信管理器初始化完成', { options: this.options })
  }

  /**
   * 发布事件
   */
  public async publishEvent(event: string, data: any, context?: CommunicationContext): Promise<void> {
    if (!this.eventBus) {
      throw new MicroCoreError('事件总线未启用', 'EVENT_BUS_DISABLED')
    }

    const fullContext: CommunicationContext = {
      timestamp: Date.now(),
      source: 'event-bus',
      ...context
    }

    // 执行中间件
    if (this.options.enableMiddleware) {
      await this.middleware.execute('before-publish', { event, data, context: fullContext })
    }

    // 更新状态
    if (this.options.enableState) {
      this.state.recordEvent(event, 'publish', fullContext)
    }

    try {
      await this.eventBus.publish(event, data)
      
      // 执行后置中间件
      if (this.options.enableMiddleware) {
        await this.middleware.execute('after-publish', { event, data, context: fullContext })
      }

      logger.debug('事件发布成功', { event, context: fullContext })
    } catch (error) {
      logger.error('事件发布失败', { event, error })
      throw new MicroCoreError(
        `事件发布失败: ${error instanceof Error ? error.message : String(error)}`,
        'EVENT_PUBLISH_FAILED',
        { event, originalError: error }
      )
    }
  }

  /**
   * 订阅事件
   */
  public subscribeEvent(event: string, handler: EventHandler, context?: CommunicationContext): () => void {
    if (!this.eventBus) {
      throw new MicroCoreError('事件总线未启用', 'EVENT_BUS_DISABLED')
    }

    const fullContext: CommunicationContext = {
      timestamp: Date.now(),
      source: 'event-bus',
      ...context
    }

    // 包装处理器以支持中间件
    const wrappedHandler: EventHandler = async (data: any) => {
      // 执行前置中间件
      if (this.options.enableMiddleware) {
        await this.middleware.execute('before-handle', { event, data, context: fullContext })
      }

      // 更新状态
      if (this.options.enableState) {
        this.state.recordEvent(event, 'handle', fullContext)
      }

      try {
        await handler(data)
        
        // 执行后置中间件
        if (this.options.enableMiddleware) {
          await this.middleware.execute('after-handle', { event, data, context: fullContext })
        }
      } catch (error) {
        logger.error('事件处理失败', { event, error })
        throw error
      }
    }

    const unsubscribe = this.eventBus.subscribe(event, wrappedHandler)

    // 更新状态
    if (this.options.enableState) {
      this.state.recordEvent(event, 'subscribe', fullContext)
    }

    logger.debug('事件订阅成功', { event, context: fullContext })

    return () => {
      unsubscribe()
      if (this.options.enableState) {
        this.state.recordEvent(event, 'unsubscribe', fullContext)
      }
    }
  }

  /**
   * 发送消息
   */
  public async sendMessage(channel: string, data: MessageData, context?: CommunicationContext): Promise<void> {
    if (!this.messageChannel) {
      throw new MicroCoreError('消息通道未启用', 'MESSAGE_CHANNEL_DISABLED')
    }

    const fullContext: CommunicationContext = {
      timestamp: Date.now(),
      source: 'message-channel',
      ...context
    }

    // 执行中间件
    if (this.options.enableMiddleware) {
      await this.middleware.execute('before-send', { channel, data, context: fullContext })
    }

    // 更新状态
    if (this.options.enableState) {
      this.state.recordMessage(channel, 'send', fullContext)
    }

    try {
      await this.messageChannel.send(channel, data)
      
      // 执行后置中间件
      if (this.options.enableMiddleware) {
        await this.middleware.execute('after-send', { channel, data, context: fullContext })
      }

      logger.debug('消息发送成功', { channel, context: fullContext })
    } catch (error) {
      logger.error('消息发送失败', { channel, error })
      throw new MicroCoreError(
        `消息发送失败: ${error instanceof Error ? error.message : String(error)}`,
        'MESSAGE_SEND_FAILED',
        { channel, originalError: error }
      )
    }
  }

  /**
   * 订阅消息
   */
  public subscribeMessage(channel: string, handler: MessageHandler, context?: CommunicationContext): () => void {
    if (!this.messageChannel) {
      throw new MicroCoreError('消息通道未启用', 'MESSAGE_CHANNEL_DISABLED')
    }

    const fullContext: CommunicationContext = {
      timestamp: Date.now(),
      source: 'message-channel',
      ...context
    }

    // 包装处理器以支持中间件
    const wrappedHandler: MessageHandler = async (data: MessageData, channelName: string) => {
      // 执行前置中间件
      if (this.options.enableMiddleware) {
        await this.middleware.execute('before-receive', { channel: channelName, data, context: fullContext })
      }

      // 更新状态
      if (this.options.enableState) {
        this.state.recordMessage(channelName, 'receive', fullContext)
      }

      try {
        await handler(data, channelName)
        
        // 执行后置中间件
        if (this.options.enableMiddleware) {
          await this.middleware.execute('after-receive', { channel: channelName, data, context: fullContext })
        }
      } catch (error) {
        logger.error('消息处理失败', { channel: channelName, error })
        throw error
      }
    }

    const unsubscribe = this.messageChannel.subscribe(channel, wrappedHandler)

    // 更新状态
    if (this.options.enableState) {
      this.state.recordMessage(channel, 'subscribe', fullContext)
    }

    logger.debug('消息订阅成功', { channel, context: fullContext })

    return () => {
      unsubscribe()
      if (this.options.enableState) {
        this.state.recordMessage(channel, 'unsubscribe', fullContext)
      }
    }
  }

  /**
   * 广播消息
   */
  public async broadcast(data: MessageData, context?: CommunicationContext): Promise<void> {
    if (!this.messageChannel) {
      throw new MicroCoreError('消息通道未启用', 'MESSAGE_CHANNEL_DISABLED')
    }

    const fullContext: CommunicationContext = {
      timestamp: Date.now(),
      source: 'message-channel',
      ...context
    }

    // 执行中间件
    if (this.options.enableMiddleware) {
      await this.middleware.execute('before-broadcast', { data, context: fullContext })
    }

    // 更新状态
    if (this.options.enableState) {
      this.state.recordMessage('__broadcast__', 'send', fullContext)
    }

    try {
      await this.messageChannel.broadcast(data)
      
      // 执行后置中间件
      if (this.options.enableMiddleware) {
        await this.middleware.execute('after-broadcast', { data, context: fullContext })
      }

      logger.debug('广播消息成功', { context: fullContext })
    } catch (error) {
      logger.error('广播消息失败', { error })
      throw new MicroCoreError(
        `广播消息失败: ${error instanceof Error ? error.message : String(error)}`,
        'BROADCAST_FAILED',
        { originalError: error }
      )
    }
  }

  /**
   * 添加中间件
   */
  public use(phase: string, middleware: MiddlewareFunction): void {
    if (!this.options.enableMiddleware) {
      throw new MicroCoreError('中间件未启用', 'MIDDLEWARE_DISABLED')
    }

    this.middleware.use(phase, middleware)
    logger.debug('添加中间件', { phase })
  }

  /**
   * 获取通信状态
   */
  public getState(): any {
    if (!this.options.enableState) {
      throw new MicroCoreError('状态管理未启用', 'STATE_DISABLED')
    }

    return {
      communication: this.state.getState(),
      eventBus: this.eventBus?.getStatus(),
      messageChannel: this.messageChannel?.getStatus()
    }
  }

  /**
   * 销毁通信管理器
   */
  public destroy(): void {
    if (this.eventBus) {
      this.eventBus.destroy()
    }

    if (this.messageChannel) {
      this.messageChannel.destroy()
    }

    if (this.state) {
      this.state.clear()
    }

    if (this.middleware) {
      this.middleware.clear()
    }

    logger.info('通信管理器已销毁')
  }
}