/**
 * SharedWorker 通信通道
 * 支持跨标签页通信
 */

import { MessageChannel } from './channel'
import type { MessageChannelOptions, ChannelMessage } from '../types/message'
import { MicroCoreError } from '@micro-core/shared'

export class SharedWorkerChannel extends MessageChannel {
  private worker: SharedWorker | null = null
  private port: MessagePort | null = null
  private readonly workerScript: string

  constructor(
    channelId: string, 
    workerScript: string,
    options: MessageChannelOptions = {}
  ) {
    super(channelId, options)
    this.workerScript = workerScript
  }

  /**
   * 初始化 SharedWorker 连接
   */
  protected async initializeConnection(): Promise<void> {
    if (!this.isSharedWorkerSupported()) {
      throw new MicroCoreError(
        'SharedWorker is not supported in this environment',
        'SHARED_WORKER_NOT_SUPPORTED'
      )
    }

    try {
      // 创建 SharedWorker
      this.worker = new SharedWorker(this.workerScript, {
        name: `micro-core-worker-${this.channelId}`
      })

      this.port = this.worker.port
      
      // 设置消息处理器
      this.port.onmessage = (event) => {
        this.handleWorkerMessage(event.data)
      }

      this.port.onerror = (error) => {
        console.error('SharedWorker port error:', error)
      }

      // 启动端口
      this.port.start()

      // 发送初始化消息
      this.port.postMessage({
        type: 'init',
        channelId: this.channelId,
        timestamp: Date.now()
      })

      // 等待 worker 确认
      await this.waitForWorkerReady()

    } catch (error) {
      throw new MicroCoreError(
        `Failed to initialize SharedWorker: ${error}`,
        'SHARED_WORKER_INIT_FAILED'
      )
    }
  }

  /**
   * 清理 SharedWorker 连接
   */
  protected async cleanupConnection(): Promise<void> {
    if (this.port) {
      this.port.postMessage({
        type: 'disconnect',
        channelId: this.channelId,
        timestamp: Date.now()
      })
      
      this.port.close()
      this.port = null
    }

    this.worker = null
  }

  /**
   * 通过 SharedWorker 发送消息
   */
  protected async sendMessage(message: ChannelMessage): Promise<void> {
    if (!this.port) {
      throw new MicroCoreError(
        'SharedWorker port is not available',
        'SHARED_WORKER_PORT_UNAVAILABLE'
      )
    }

    try {
      this.port.postMessage({
        type: 'message',
        message,
        timestamp: Date.now()
      })
    } catch (error) {
      throw new MicroCoreError(
        `Failed to send message through SharedWorker: ${error}`,
        'SHARED_WORKER_SEND_FAILED'
      )
    }
  }

  /**
   * 处理来自 SharedWorker 的消息
   */
  private async handleWorkerMessage(data: any): Promise<void> {
    try {
      switch (data.type) {
        case 'ready':
          // Worker 已准备就绪
          break
          
        case 'message':
          // 接收到其他通道的消息
          if (data.message && data.message.sourceId !== this.channelId) {
            await this.handleMessage(data.message)
          }
          break
          
        case 'error':
          console.error('SharedWorker error:', data.error)
          break
          
        default:
          console.warn('Unknown message type from SharedWorker:', data.type)
      }
    } catch (error) {
      console.error('Error handling SharedWorker message:', error)
    }
  }

  /**
   * 等待 Worker 准备就绪
   */
  private async waitForWorkerReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('SharedWorker ready timeout'))
      }, 5000)

      const originalHandler = this.port!.onmessage
      
      this.port!.onmessage = (event) => {
        if (event.data.type === 'ready') {
          clearTimeout(timeout)
          this.port!.onmessage = originalHandler
          resolve()
        } else if (originalHandler) {
          originalHandler(event)
        }
      }
    })
  }

  /**
   * 检查 SharedWorker 支持
   */
  private isSharedWorkerSupported(): boolean {
    return typeof SharedWorker !== 'undefined'
  }

  /**
   * 获取 Worker 状态
   */
  getWorkerStatus() {
    return {
      ...this.getStatus(),
      hasWorker: !!this.worker,
      hasPort: !!this.port,
      workerScript: this.workerScript,
      isSupported: this.isSharedWorkerSupported()
    }
  }
}

/**
 * SharedWorker 脚本内容
 * 用于在不同标签页之间转发消息
 */
export const createSharedWorkerScript = (): string => {
  return `
    // SharedWorker 脚本
    const channels = new Map();
    
    self.addEventListener('connect', (event) => {
      const port = event.ports[0];
      let channelId = null;
      
      port.onmessage = (e) => {
        const { type, channelId: id, message } = e.data;
        
        switch (type) {
          case 'init':
            channelId = id;
            channels.set(channelId, port);
            port.postMessage({ type: 'ready', channelId });
            break;
            
          case 'message':
            // 转发消息给其他通道
            for (const [id, p] of channels.entries()) {
              if (id !== channelId && p !== port) {
                try {
                  p.postMessage({ type: 'message', message });
                } catch (error) {
                  console.error('Failed to forward message:', error);
                }
              }
            }
            break;
            
          case 'disconnect':
            if (channelId) {
              channels.delete(channelId);
            }
            break;
        }
      };
      
      port.onerror = (error) => {
        console.error('SharedWorker port error:', error);
        if (channelId) {
          channels.delete(channelId);
        }
      };
      
      port.start();
    });
  `
}