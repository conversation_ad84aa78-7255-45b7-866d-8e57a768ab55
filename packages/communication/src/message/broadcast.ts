/**
 * BroadcastChannel 通信适配器
 * 支持同源页面间通信
 */

import { MessageChannel } from './channel'
import type { MessageChannelOptions, ChannelMessage } from '../types/message'
import { MicroCoreError } from '@micro-core/shared'

export class BroadcastChannelAdapter extends MessageChannel {
  private broadcastChannel: BroadcastChannel | null = null
  private readonly channelName: string

  constructor(
    channelId: string,
    channelName: string,
    options: MessageChannelOptions = {}
  ) {
    super(channelId, options)
    this.channelName = channelName
  }

  /**
   * 初始化 BroadcastChannel 连接
   */
  protected async initializeConnection(): Promise<void> {
    if (!this.isBroadcastChannelSupported()) {
      throw new MicroCoreError(
        'BroadcastChannel is not supported in this environment',
        'BROADCAST_CHANNEL_NOT_SUPPORTED'
      )
    }

    try {
      // 创建 BroadcastChannel
      this.broadcastChannel = new BroadcastChannel(this.channelName)
      
      // 设置消息处理器
      this.broadcastChannel.onmessage = (event) => {
        this.handleBroadcastMessage(event.data)
      }

      this.broadcastChannel.onerror = (error) => {
        console.error('BroadcastChannel error:', error)
      }

      // 发送连接通知
      this.broadcastChannel.postMessage({
        type: 'channel_connected',
        channelId: this.channelId,
        timestamp: Date.now()
      })

    } catch (error) {
      throw new MicroCoreError(
        `Failed to initialize BroadcastChannel: ${error}`,
        'BROADCAST_CHANNEL_INIT_FAILED'
      )
    }
  }

  /**
   * 清理 BroadcastChannel 连接
   */
  protected async cleanupConnection(): Promise<void> {
    if (this.broadcastChannel) {
      // 发送断开通知
      this.broadcastChannel.postMessage({
        type: 'channel_disconnected',
        channelId: this.channelId,
        timestamp: Date.now()
      })
      
      this.broadcastChannel.close()
      this.broadcastChannel = null
    }
  }

  /**
   * 通过 BroadcastChannel 发送消息
   */
  protected async sendMessage(message: ChannelMessage): Promise<void> {
    if (!this.broadcastChannel) {
      throw new MicroCoreError(
        'BroadcastChannel is not available',
        'BROADCAST_CHANNEL_UNAVAILABLE'
      )
    }

    try {
      this.broadcastChannel.postMessage({
        type: 'message',
        message,
        timestamp: Date.now()
      })
    } catch (error) {
      throw new MicroCoreError(
        `Failed to send message through BroadcastChannel: ${error}`,
        'BROADCAST_CHANNEL_SEND_FAILED'
      )
    }
  }

  /**
   * 处理来自 BroadcastChannel 的消息
   */
  private async handleBroadcastMessage(data: any): Promise<void> {
    try {
      switch (data.type) {
        case 'channel_connected':
          // 其他通道连接
          if (data.channelId !== this.channelId) {
            console.log(`Channel connected: ${data.channelId}`)
          }
          break
          
        case 'channel_disconnected':
          // 其他通道断开
          if (data.channelId !== this.channelId) {
            console.log(`Channel disconnected: ${data.channelId}`)
          }
          break
          
        case 'message':
          // 接收到其他通道的消息
          if (data.message && data.message.sourceId !== this.channelId) {
            await this.handleMessage(data.message)
          }
          break
          
        default:
          console.warn('Unknown message type from BroadcastChannel:', data.type)
      }
    } catch (error) {
      console.error('Error handling BroadcastChannel message:', error)
    }
  }

  /**
   * 检查 BroadcastChannel 支持
   */
  private isBroadcastChannelSupported(): boolean {
    return typeof BroadcastChannel !== 'undefined'
  }

  /**
   * 获取 BroadcastChannel 状态
   */
  getBroadcastStatus() {
    return {
      ...this.getStatus(),
      hasBroadcastChannel: !!this.broadcastChannel,
      channelName: this.channelName,
      isSupported: this.isBroadcastChannelSupported()
    }
  }

  /**
   * 发送心跳消息
   */
  async sendHeartbeat(): Promise<void> {
    if (this.broadcastChannel) {
      this.broadcastChannel.postMessage({
        type: 'heartbeat',
        channelId: this.channelId,
        timestamp: Date.now()
      })
    }
  }

  /**
   * 获取通道信息
   */
  async getChannelInfo(): Promise<void> {
    if (this.broadcastChannel) {
      this.broadcastChannel.postMessage({
        type: 'channel_info_request',
        channelId: this.channelId,
        timestamp: Date.now()
      })
    }
  }
}

/**
 * PostMessage 通信适配器
 * 支持跨域通信
 */
export class PostMessageChannel extends MessageChannel {
  private readonly targetWindow: Window
  private readonly targetOrigin: string
  private messageListener: ((event: MessageEvent) => void) | null = null

  constructor(
    channelId: string,
    targetWindow: Window,
    targetOrigin: string = '*',
    options: MessageChannelOptions = {}
  ) {
    super(channelId, options)
    this.targetWindow = targetWindow
    this.targetOrigin = targetOrigin
  }

  /**
   * 初始化 PostMessage 连接
   */
  protected async initializeConnection(): Promise<void> {
    try {
      // 设置消息监听器
      this.messageListener = (event: MessageEvent) => {
        // 验证来源
        if (this.targetOrigin !== '*' && event.origin !== this.targetOrigin) {
          return
        }

        this.handlePostMessage(event.data)
      }

      window.addEventListener('message', this.messageListener)

      // 发送连接通知
      this.targetWindow.postMessage({
        type: 'channel_connected',
        channelId: this.channelId,
        timestamp: Date.now()
      }, this.targetOrigin)

    } catch (error) {
      throw new MicroCoreError(
        `Failed to initialize PostMessage channel: ${error}`,
        'POST_MESSAGE_INIT_FAILED'
      )
    }
  }

  /**
   * 清理 PostMessage 连接
   */
  protected async cleanupConnection(): Promise<void> {
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener)
      this.messageListener = null
    }

    // 发送断开通知
    try {
      this.targetWindow.postMessage({
        type: 'channel_disconnected',
        channelId: this.channelId,
        timestamp: Date.now()
      }, this.targetOrigin)
    } catch (error) {
      // 忽略发送失败的错误
      console.warn('Failed to send disconnect notification:', error)
    }
  }

  /**
   * 通过 PostMessage 发送消息
   */
  protected async sendMessage(message: ChannelMessage): Promise<void> {
    try {
      this.targetWindow.postMessage({
        type: 'message',
        message,
        timestamp: Date.now()
      }, this.targetOrigin)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to send message through PostMessage: ${error}`,
        'POST_MESSAGE_SEND_FAILED'
      )
    }
  }

  /**
   * 处理来自 PostMessage 的消息
   */
  private async handlePostMessage(data: any): Promise<void> {
    try {
      switch (data.type) {
        case 'channel_connected':
          // 其他通道连接
          if (data.channelId !== this.channelId) {
            console.log(`PostMessage channel connected: ${data.channelId}`)
          }
          break
          
        case 'channel_disconnected':
          // 其他通道断开
          if (data.channelId !== this.channelId) {
            console.log(`PostMessage channel disconnected: ${data.channelId}`)
          }
          break
          
        case 'message':
          // 接收到消息
          if (data.message && data.message.sourceId !== this.channelId) {
            await this.handleMessage(data.message)
          }
          break
          
        default:
          // 忽略未知消息类型
          break
      }
    } catch (error) {
      console.error('Error handling PostMessage:', error)
    }
  }

  /**
   * 获取 PostMessage 状态
   */
  getPostMessageStatus() {
    return {
      ...this.getStatus(),
      hasTargetWindow: !!this.targetWindow,
      targetOrigin: this.targetOrigin,
      hasListener: !!this.messageListener
    }
  }
}