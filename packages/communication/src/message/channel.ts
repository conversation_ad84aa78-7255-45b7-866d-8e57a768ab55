/**
 * 消息通道实现
 * 提供统一的消息传递接口
 */

import type { 
  MessageChannelOptions, 
  MessageHandler, 
  MessageData, 
  ChannelMessage,
  MessageChannelInterface 
} from '../types/message'
import { MicroCoreError } from '@micro-core/shared'

export class MessageChannel implements MessageChannelInterface {
  private readonly channelId: string
  private readonly options: Required<MessageChannelOptions>
  private readonly handlers = new Map<string, Set<MessageHandler>>()
  private readonly messageQueue: ChannelMessage[] = []
  private isConnected = false
  private messageIdCounter = 0

  constructor(channelId: string, options: MessageChannelOptions = {}) {
    this.channelId = channelId
    this.options = {
      maxQueueSize: options.maxQueueSize ?? 1000,
      enablePersistence: options.enablePersistence ?? false,
      timeout: options.timeout ?? 5000,
      retryAttempts: options.retryAttempts ?? 3,
      enableCompression: options.enableCompression ?? false,
      enableEncryption: options.enableEncryption ?? false
    }
  }

  /**
   * 连接消息通道
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      return
    }

    try {
      // 初始化连接逻辑
      await this.initializeConnection()
      this.isConnected = true
      
      // 处理队列中的消息
      await this.processQueuedMessages()
    } catch (error) {
      throw new MicroCoreError(
        `Failed to connect message channel: ${error}`,
        'CHANNEL_CONNECTION_FAILED'
      )
    }
  }

  /**
   * 断开消息通道
   */
  async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return
    }

    try {
      await this.cleanupConnection()
      this.isConnected = false
    } catch (error) {
      throw new MicroCoreError(
        `Failed to disconnect message channel: ${error}`,
        'CHANNEL_DISCONNECTION_FAILED'
      )
    }
  }

  /**
   * 发送消息
   */
  async send(type: string, data: MessageData, targetId?: string): Promise<void> {
    const message: ChannelMessage = {
      id: this.generateMessageId(),
      type,
      data,
      sourceId: this.channelId,
      targetId,
      timestamp: Date.now(),
      retryCount: 0
    }

    if (!this.isConnected) {
      // 如果未连接，将消息加入队列
      this.enqueueMessage(message)
      return
    }

    try {
      await this.sendMessage(message)
    } catch (error) {
      // 发送失败时重试
      await this.retryMessage(message, error as Error)
    }
  }

  /**
   * 订阅消息
   */
  subscribe(type: string, handler: MessageHandler): () => void {
    if (!this.handlers.has(type)) {
      this.handlers.set(type, new Set())
    }

    const handlers = this.handlers.get(type)!
    handlers.add(handler)

    // 返回取消订阅函数
    return () => {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.handlers.delete(type)
      }
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(type: string, handler?: MessageHandler): void {
    if (!handler) {
      // 取消所有该类型的订阅
      this.handlers.delete(type)
      return
    }

    const handlers = this.handlers.get(type)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.handlers.delete(type)
      }
    }
  }

  /**
   * 广播消息
   */
  async broadcast(type: string, data: MessageData): Promise<void> {
    await this.send(type, data)
  }

  /**
   * 获取通道状态
   */
  getStatus() {
    return {
      channelId: this.channelId,
      isConnected: this.isConnected,
      handlerCount: Array.from(this.handlers.values()).reduce((sum, set) => sum + set.size, 0),
      queueSize: this.messageQueue.length,
      options: this.options
    }
  }

  /**
   * 处理接收到的消息
   */
  protected async handleMessage(message: ChannelMessage): Promise<void> {
    const handlers = this.handlers.get(message.type)
    if (!handlers || handlers.size === 0) {
      return
    }

    // 并行处理所有处理器
    const promises = Array.from(handlers).map(async handler => {
      try {
        await handler(message)
      } catch (error) {
        console.error(`Message handler error for type ${message.type}:`, error)
      }
    })

    await Promise.allSettled(promises)
  }

  /**
   * 初始化连接
   */
  protected async initializeConnection(): Promise<void> {
    // 子类实现具体的连接逻辑
  }

  /**
   * 清理连接
   */
  protected async cleanupConnection(): Promise<void> {
    // 子类实现具体的清理逻辑
  }

  /**
   * 发送消息的具体实现
   */
  protected async sendMessage(message: ChannelMessage): Promise<void> {
    // 子类实现具体的发送逻辑
    throw new MicroCoreError(
      'sendMessage method must be implemented by subclass',
      'METHOD_NOT_IMPLEMENTED'
    )
  }

  /**
   * 将消息加入队列
   */
  private enqueueMessage(message: ChannelMessage): void {
    if (this.messageQueue.length >= this.options.maxQueueSize) {
      // 移除最旧的消息
      this.messageQueue.shift()
    }
    
    this.messageQueue.push(message)
  }

  /**
   * 处理队列中的消息
   */
  private async processQueuedMessages(): Promise<void> {
    const messages = [...this.messageQueue]
    this.messageQueue.length = 0

    for (const message of messages) {
      try {
        await this.sendMessage(message)
      } catch (error) {
        console.error('Failed to process queued message:', error)
      }
    }
  }

  /**
   * 重试发送消息
   */
  private async retryMessage(message: ChannelMessage, error: Error): Promise<void> {
    if (message.retryCount >= this.options.retryAttempts) {
      throw new MicroCoreError(
        `Message send failed after ${this.options.retryAttempts} attempts: ${error.message}`,
        'MESSAGE_SEND_FAILED'
      )
    }

    message.retryCount++
    
    // 指数退避重试
    const delay = Math.pow(2, message.retryCount) * 1000
    await new Promise(resolve => setTimeout(resolve, delay))

    try {
      await this.sendMessage(message)
    } catch (retryError) {
      await this.retryMessage(message, retryError as Error)
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `${this.channelId}_${++this.messageIdCounter}_${Date.now()}`
  }
}