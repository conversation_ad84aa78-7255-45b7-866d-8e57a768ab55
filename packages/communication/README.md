# @micro-core/communication

微前端应用间通信系统，提供高性能的事件总线和消息通道。

## 特性

- 🚀 **高性能事件总线** - 基于发布订阅模式的事件系统
- 📡 **多种消息通道** - 支持 SharedWorker、BroadcastChannel、PostMessage
- 🔧 **统一通信接口** - 简化应用间通信的复杂性
- 🛡️ **类型安全** - 完整的 TypeScript 类型定义
- 🎯 **中间件支持** - 支持消息拦截和转换

## 安装

```bash
pnpm add @micro-core/communication
```

## 基础使用

### 事件总线

```typescript
import { EventBus } from '@micro-core/communication'

const eventBus = new EventBus()

// 订阅事件
eventBus.subscribe('user-login', (data) => {
  console.log('用户登录:', data)
})

// 发布事件
eventBus.publish('user-login', { userId: '123', username: 'echo' })

// 取消订阅
eventBus.unsubscribe('user-login', handler)
```

### 消息通道

```typescript
import { MessageChannel } from '@micro-core/communication'

const channel = new MessageChannel({
  type: 'broadcast', // 'broadcast' | 'worker' | 'postmessage'
  channelName: 'micro-app-channel'
})

// 发送消息
await channel.send('app-message', { data: 'hello' })

// 监听消息
channel.on('app-message', (message) => {
  console.log('收到消息:', message)
})
```

### 通信管理器

```typescript
import { CommunicationManager } from '@micro-core/communication'

const manager = new CommunicationManager({
  enableEventBus: true,
  enableMessageChannel: true,
  channelConfig: {
    type: 'broadcast',
    channelName: 'micro-core-channel'
  }
})

// 初始化
await manager.initialize()

// 发送消息
await manager.sendMessage('target-app', 'user-action', { action: 'click' })

// 监听消息
manager.onMessage('user-action', (message) => {
  console.log('收到用户操作:', message)
})
```

## API 文档

### EventBus

#### 方法

- `subscribe(event: string, handler: Function): void` - 订阅事件
- `unsubscribe(event: string, handler: Function): void` - 取消订阅
- `publish(event: string, data: any): void` - 发布事件
- `clear(): void` - 清除所有订阅
- `getSubscribers(event: string): Function[]` - 获取事件订阅者

### MessageChannel

#### 配置选项

```typescript
interface ChannelConfig {
  type: 'broadcast' | 'worker' | 'postmessage'
  channelName: string
  targetOrigin?: string // PostMessage 专用
  workerScript?: string // SharedWorker 专用
}
```

#### 方法

- `send(type: string, data: any): Promise<void>` - 发送消息
- `on(type: string, handler: Function): void` - 监听消息
- `off(type: string, handler: Function): void` - 取消监听
- `destroy(): void` - 销毁通道

### CommunicationManager

#### 配置选项

```typescript
interface CommunicationConfig {
  enableEventBus?: boolean
  enableMessageChannel?: boolean
  channelConfig?: ChannelConfig
  middleware?: CommunicationMiddleware[]
}
```

#### 方法

- `initialize(): Promise<void>` - 初始化通信管理器
- `sendMessage(target: string, type: string, data: any): Promise<void>` - 发送消息
- `onMessage(type: string, handler: Function): void` - 监听消息
- `addMiddleware(middleware: CommunicationMiddleware): void` - 添加中间件
- `getStats(): CommunicationStats` - 获取通信统计
- `destroy(): void` - 销毁管理器

## 中间件

支持自定义中间件来拦截和转换消息：

```typescript
const loggerMiddleware: CommunicationMiddleware = {
  name: 'logger',
  process: async (message, next) => {
    console.log('发送消息:', message)
    const result = await next(message)
    console.log('消息已发送')
    return result
  }
}

manager.addMiddleware(loggerMiddleware)
```

## 许可证

MIT