
> @micro-core/shared@0.1.0 build /Users/<USER>/Desktop/micro-core/packages/shared
> vite build

vite v7.0.6 building for production...
transforming...
✓ 21 modules transformed.
✗ Build failed in 1.12s
error during build:
[vite:esbuild] Transform failed with 1 error:
/Users/<USER>/Desktop/micro-core/packages/shared/src/errors/base.ts:854:10: ERROR: Expected "}" but found "parseCSVLine"
file: /Users/<USER>/Desktop/micro-core/packages/shared/src/errors/base.ts:854:10

Expected "}" but found "parseCSVLine"
852|     * 解析CSV行（处理引号和逗号）
853|     */
854|    private parseCSVLine(line: string): string[] {
   |            ^
855|      const result: string[] = [];
856|      let current = '';

    at failureErrorWithLog (/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/lib/main.js:1467:15)
    at /Users/<USER>/Desktop/micro-core/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/lib/main.js:736:50
    at responseCallbacks.<computed> (/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/lib/main.js:603:9)
    at handleIncomingPacket (/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/lib/main.js:658:12)
    at Socket.readFromStdout (/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/esbuild@0.25.8/node_modules/esbuild/lib/main.js:581:7)
    at Socket.emit (node:events:507:28)
    at addChunk (node:internal/streams/readable:559:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)
    at Readable.push (node:internal/streams/readable:390:5)
    at Pipe.onStreamRead (node:internal/stream_base_commons:189:23)
 ELIFECYCLE  Command failed with exit code 1.
