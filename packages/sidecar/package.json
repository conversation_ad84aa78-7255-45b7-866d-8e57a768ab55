{"name": "@micro-core/sidecar", "version": "0.1.0", "description": "Sidecar模式实现，提供一行代码接入和零配置启动", "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["micro-frontend", "sidecar", "zero-config", "auto-start", "migration"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/sidecar"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "@micro-core/sandbox": "workspace:*", "@micro-core/adapters": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}, "peerDependencies": {"typescript": ">=5.0.0"}}