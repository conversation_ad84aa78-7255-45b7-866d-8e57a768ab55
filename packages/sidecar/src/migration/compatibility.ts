/**
 * 兼容性检查器
 * 检查项目与微前端架构的兼容性
 */

import { logger, MicroCoreError } from '@micro-core/shared'
import type { CompatibilityReport } from '../types/migration.js'

/**
 * 兼容性检查器类
 * 负责检查项目的兼容性并生成报告
 */
export class CompatibilityChecker {
  private projectPath: string

  constructor(projectPath: string = process.cwd()) {
    this.projectPath = projectPath
  }

  /**
   * 执行兼容性检查
   * @returns Promise<CompatibilityReport>
   */
  async checkCompatibility(): Promise<CompatibilityReport> {
    try {
      logger.info('开始兼容性检查', { projectPath: this.projectPath })

      const report: CompatibilityReport = {
        overall: 'unknown',
        framework: await this.checkFrameworkCompatibility(),
        buildTool: await this.checkBuildToolCompatibility(),
        dependencies: await this.checkDependencyCompatibility(),
        browser: await this.checkBrowserCompatibility(),
        issues: [],
        recommendations: [],
        checkedAt: new Date().toISOString()
      }

      // 计算总体兼容性
      report.overall = this.calculateOverallCompatibility(report)

      // 生成问题列表
      report.issues = this.collectIssues(report)

      // 生成建议
      report.recommendations = this.generateRecommendations(report)

      logger.info('兼容性检查完成', { 
        overall: report.overall,
        issuesCount: report.issues.length
      })

      return report
    } catch (error) {
      logger.error('兼容性检查失败', { error })
      throw new MicroCoreError('兼容性检查失败', 'COMPATIBILITY_CHECK_FAILED', { error })
    }
  }

  /**
   * 检查框架兼容性
   * @returns Promise<any>
   */
  private async checkFrameworkCompatibility(): Promise<any> {
    const result = {
      status: 'unknown' as 'compatible' | 'partial' | 'incompatible' | 'unknown',
      framework: 'unknown',
      version: 'unknown',
      issues: [] as string[],
      recommendations: [] as string[]
    }

    try {
      // 模拟检测框架
      const mockPackageJson = {
        dependencies: {
          'vue': '^3.4.0',
          'vue-router': '^4.2.0'
        }
      }

      // 检查Vue
      if (mockPackageJson.dependencies['vue']) {
        const version = mockPackageJson.dependencies['vue']
        result.framework = 'vue'
        result.version = version

        if (version.includes('^3') || version.includes('^4')) {
          result.status = 'compatible'
          result.framework = 'vue3'
        } else if (version.includes('^2')) {
          result.status = 'compatible'
          result.framework = 'vue2'
        } else {
          result.status = 'partial'
          result.issues.push('Vue版本过低，建议升级到2.6+或3.0+')
        }
      }

      // 检查React
      else if (mockPackageJson.dependencies['react']) {
        const version = mockPackageJson.dependencies['react']
        result.framework = 'react'
        result.version = version
        result.status = 'compatible'
      }

      // 检查Angular
      else if (mockPackageJson.dependencies['@angular/core']) {
        const version = mockPackageJson.dependencies['@angular/core']
        result.framework = 'angular'
        result.version = version
        result.status = 'compatible'
      }

      // 未知框架
      else {
        result.status = 'unknown'
        result.issues.push('无法识别项目框架')
        result.recommendations.push('请确认项目使用的前端框架')
      }

      return result
    } catch (error) {
      logger.error('框架兼容性检查失败', { error })
      result.status = 'unknown'
      result.issues.push('框架检测失败')
      return result
    }
  }

  /**
   * 检查构建工具兼容性
   * @returns Promise<any>
   */
  private async checkBuildToolCompatibility(): Promise<any> {
    const result = {
      status: 'unknown' as 'compatible' | 'partial' | 'incompatible' | 'unknown',
      tool: 'unknown',
      version: 'unknown',
      issues: [] as string[],
      recommendations: [] as string[]
    }

    try {
      // 模拟检测构建工具
      const mockFiles = ['vite.config.ts']

      if (mockFiles.includes('vite.config.ts') || mockFiles.includes('vite.config.js')) {
        result.tool = 'vite'
        result.status = 'compatible'
        result.version = '7.0.6'
      } else if (mockFiles.includes('webpack.config.js')) {
        result.tool = 'webpack'
        result.status = 'compatible'
        result.version = '5.x'
      } else if (mockFiles.includes('rollup.config.js')) {
        result.tool = 'rollup'
        result.status = 'compatible'
        result.version = '4.x'
      } else {
        result.status = 'unknown'
        result.issues.push('无法识别构建工具')
        result.recommendations.push('建议使用Vite、Webpack或Rollup')
      }

      return result
    } catch (error) {
      logger.error('构建工具兼容性检查失败', { error })
      result.status = 'unknown'
      result.issues.push('构建工具检测失败')
      return result
    }
  }

  /**
   * 检查依赖兼容性
   * @returns Promise<any>
   */
  private async checkDependencyCompatibility(): Promise<any> {
    const result = {
      status: 'compatible' as 'compatible' | 'partial' | 'incompatible' | 'unknown',
      conflicts: [] as string[],
      outdated: [] as string[],
      missing: [] as string[],
      issues: [] as string[],
      recommendations: [] as string[]
    }

    try {
      // 模拟依赖检查
      const mockDependencies = {
        'vue': '^3.4.0',
        'typescript': '^5.7.2',
        'vite': '^7.0.6'
      }

      // 检查必需依赖
      const requiredDeps = ['typescript']
      for (const dep of requiredDeps) {
        if (!mockDependencies[dep as keyof typeof mockDependencies]) {
          result.missing.push(dep)
          result.status = 'partial'
        }
      }

      // 检查过时依赖
      // 这里应该实现实际的版本检查逻辑

      // 检查冲突依赖
      // 这里应该实现实际的冲突检查逻辑

      if (result.missing.length > 0) {
        result.issues.push(`缺少必需依赖: ${result.missing.join(', ')}`)
        result.recommendations.push('请安装缺少的依赖')
      }

      if (result.outdated.length > 0) {
        result.issues.push(`存在过时依赖: ${result.outdated.join(', ')}`)
        result.recommendations.push('建议更新过时的依赖')
      }

      if (result.conflicts.length > 0) {
        result.issues.push(`存在依赖冲突: ${result.conflicts.join(', ')}`)
        result.recommendations.push('请解决依赖冲突')
        result.status = 'incompatible'
      }

      return result
    } catch (error) {
      logger.error('依赖兼容性检查失败', { error })
      result.status = 'unknown'
      result.issues.push('依赖检测失败')
      return result
    }
  }

  /**
   * 检查浏览器兼容性
   * @returns Promise<any>
   */
  private async checkBrowserCompatibility(): Promise<any> {
    const result = {
      status: 'compatible' as 'compatible' | 'partial' | 'incompatible' | 'unknown',
      supported: [] as string[],
      unsupported: [] as string[],
      issues: [] as string[],
      recommendations: [] as string[]
    }

    try {
      // 微前端需要的浏览器特性
      const requiredFeatures = [
        'Proxy',
        'CustomElements',
        'ShadowDOM',
        'ES6Modules',
        'DynamicImport'
      ]

      // 支持的浏览器
      result.supported = [
        'Chrome 63+',
        'Firefox 63+',
        'Safari 13+',
        'Edge 79+'
      ]

      // 不支持的浏览器
      result.unsupported = [
        'IE 11',
        'Chrome < 63',
        'Firefox < 63',
        'Safari < 13'
      ]

      result.status = 'compatible'

      // 添加建议
      result.recommendations.push('建议在支持的现代浏览器中使用')
      result.recommendations.push('对于不支持的浏览器，可以考虑使用polyfill')

      return result
    } catch (error) {
      logger.error('浏览器兼容性检查失败', { error })
      result.status = 'unknown'
      result.issues.push('浏览器兼容性检测失败')
      return result
    }
  }

  /**
   * 计算总体兼容性
   * @param report 兼容性报告
   * @returns string
   */
  private calculateOverallCompatibility(report: CompatibilityReport): 'compatible' | 'partial' | 'incompatible' | 'unknown' {
    const statuses = [
      report.framework.status,
      report.buildTool.status,
      report.dependencies.status,
      report.browser.status
    ]

    if (statuses.includes('incompatible')) {
      return 'incompatible'
    }

    if (statuses.includes('unknown')) {
      return 'unknown'
    }

    if (statuses.includes('partial')) {
      return 'partial'
    }

    return 'compatible'
  }

  /**
   * 收集问题
   * @param report 兼容性报告
   * @returns string[]
   */
  private collectIssues(report: CompatibilityReport): string[] {
    const issues: string[] = []

    issues.push(...report.framework.issues)
    issues.push(...report.buildTool.issues)
    issues.push(...report.dependencies.issues)
    issues.push(...report.browser.issues)

    return issues
  }

  /**
   * 生成建议
   * @param report 兼容性报告
   * @returns string[]
   */
  private generateRecommendations(report: CompatibilityReport): string[] {
    const recommendations: string[] = []

    recommendations.push(...report.framework.recommendations)
    recommendations.push(...report.buildTool.recommendations)
    recommendations.push(...report.dependencies.recommendations)
    recommendations.push(...report.browser.recommendations)

    // 根据总体兼容性添加建议
    switch (report.overall) {
      case 'incompatible':
        recommendations.push('项目存在严重兼容性问题，建议先解决后再进行迁移')
        break
      case 'partial':
        recommendations.push('项目部分兼容，建议先解决兼容性问题')
        break
      case 'compatible':
        recommendations.push('项目兼容性良好，可以开始迁移')
        break
      default:
        recommendations.push('无法确定兼容性，建议手动检查')
    }

    return [...new Set(recommendations)] // 去重
  }
}

/**
 * 创建兼容性检查器实例
 * @param projectPath 项目路径
 * @returns CompatibilityChecker
 */
export function createCompatibilityChecker(projectPath?: string): CompatibilityChecker {
  return new CompatibilityChecker(projectPath)
}