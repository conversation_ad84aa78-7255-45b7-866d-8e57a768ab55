/**
 * 自动发现类型定义
 */

/**
 * 发现选项
 */
export interface DiscoveryOptions {
  /** 搜索路径 */
  searchPaths?: string[]
  /** 排除路径 */
  excludePaths?: string[]
  /** 搜索深度 */
  maxDepth?: number
  /** 是否包含隐藏文件 */
  includeHidden?: boolean
  /** 自定义匹配规则 */
  customRules?: DiscoveryRule[]
}

/**
 * 发现规则
 */
export interface DiscoveryRule {
  /** 规则名称 */
  name: string
  /** 匹配模式 */
  pattern: string | RegExp
  /** 规则类型 */
  type: 'include' | 'exclude'
  /** 优先级 */
  priority?: number
}

/**
 * 发现结果基类
 */
export interface DiscoveryResult {
  /** 是否成功 */
  success: boolean
  /** 发现的项目数量 */
  count: number
  /** 错误信息 */
  errors: string[]
  /** 发现时间 */
  discoveredAt: Date
}

/**
 * 应用发现结果
 */
export interface AppDiscoveryResult extends DiscoveryResult {
  /** 发现的应用列表 */
  apps: DiscoveredApp[]
}

/**
 * 发现的应用信息
 */
export interface DiscoveredApp {
  /** 应用名称 */
  name: string
  /** 应用路径 */
  path: string
  /** 应用类型 */
  type: 'main' | 'sub' | 'unknown'
  /** 框架类型 */
  framework: string
  /** 框架版本 */
  frameworkVersion: string
  /** 构建工具 */
  buildTool: string
  /** 入口文件 */
  entry: string
  /** 端口号 */
  port?: number
  /** 依赖信息 */
  dependencies: Record<string, string>
  /** 配置文件 */
  configFiles: string[]
}

/**
 * 配置发现结果
 */
export interface ConfigDiscoveryResult extends DiscoveryResult {
  /** 发现的配置列表 */
  configs: DiscoveredConfig[]
}

/**
 * 发现的配置信息
 */
export interface DiscoveredConfig {
  /** 配置名称 */
  name: string
  /** 配置文件路径 */
  path: string
  /** 配置类型 */
  type: 'build' | 'test' | 'lint' | 'typescript' | 'package' | 'other'
  /** 配置内容 */
  content: any
  /** 是否有效 */
  valid: boolean
  /** 错误信息 */
  errors: string[]
}

/**
 * 路由发现结果
 */
export interface RouteDiscoveryResult extends DiscoveryResult {
  /** 发现的路由列表 */
  routes: DiscoveredRoute[]
}

/**
 * 发现的路由信息
 */
export interface DiscoveredRoute {
  /** 路由路径 */
  path: string
  /** 路由名称 */
  name?: string
  /** 组件路径 */
  component: string
  /** 路由类型 */
  type: 'page' | 'layout' | 'component'
  /** 是否为动态路由 */
  dynamic: boolean
  /** 路由参数 */
  params: string[]
  /** 嵌套路由 */
  children?: DiscoveredRoute[]
  /** 路由元信息 */
  meta?: Record<string, any>
}

/**
 * 发现上下文
 */
export interface DiscoveryContext {
  /** 当前搜索路径 */
  currentPath: string
  /** 搜索深度 */
  depth: number
  /** 已发现的项目 */
  discovered: any[]
  /** 错误列表 */
  errors: string[]
  /** 开始时间 */
  startTime: Date
}

/**
 * 发现策略
 */
export interface DiscoveryStrategy {
  /** 策略名称 */
  name: string
  /** 策略描述 */
  description: string
  /** 匹配函数 */
  match: (path: string, context: DiscoveryContext) => boolean
  /** 发现函数 */
  discover: (path: string, context: DiscoveryContext) => Promise<any>
  /** 优先级 */
  priority: number
}