/**
 * Sidecar模式类型定义
 * 导出所有Sidecar相关的类型定义
 */

export type {
  SidecarOptions,
  SidecarConfig,
  SidecarContext,
  SidecarStatus,
  SidecarEvent
} from './sidecar.js'

export type {
  DiscoveryOptions,
  DiscoveryResult,
  AppDiscoveryResult,
  ConfigDiscoveryResult,
  RouteDiscoveryResult
} from './discovery.js'

export type {
  MigrationOptions,
  MigrationPlan,
  MigrationStep,
  MigrationAction,
  CompatibilityReport,
  UpgradeOptions
} from './migration.js'