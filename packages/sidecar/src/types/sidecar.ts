/**
 * Sidecar核心类型定义
 */

/**
 * Sidecar配置选项
 */
export interface SidecarOptions {
  /** 是否自动启动 */
  autoStart?: boolean
  /** 项目根路径 */
  projectPath?: string
  /** 配置文件路径 */
  configPath?: string
  /** 是否启用开发模式 */
  devMode?: boolean
  /** 端口号 */
  port?: number
  /** 主机地址 */
  host?: string
  /** 是否启用热更新 */
  hotReload?: boolean
  /** 自定义配置 */
  customConfig?: Record<string, any>
}

/**
 * Sidecar配置
 */
export interface SidecarConfig {
  /** 应用名称 */
  name: string
  /** 应用类型 */
  type: 'main' | 'sub'
  /** 框架类型 */
  framework: string
  /** 入口文件 */
  entry: string
  /** 构建工具 */
  buildTool: string
  /** 端口配置 */
  port: number
  /** 路由配置 */
  routes?: any[]
  /** 插件配置 */
  plugins?: string[]
  /** 自定义配置 */
  custom?: Record<string, any>
}

/**
 * Sidecar上下文
 */
export interface SidecarContext {
  /** 配置信息 */
  config: SidecarConfig
  /** 项目路径 */
  projectPath: string
  /** 运行状态 */
  status: SidecarStatus
  /** 启动时间 */
  startTime: Date
  /** 错误信息 */
  errors: Error[]
  /** 日志记录 */
  logs: string[]
}

/**
 * Sidecar状态
 */
export type SidecarStatus = 
  | 'initializing'  // 初始化中
  | 'discovering'   // 发现中
  | 'configuring'   // 配置中
  | 'starting'      // 启动中
  | 'running'       // 运行中
  | 'stopping'      // 停止中
  | 'stopped'       // 已停止
  | 'error'         // 错误状态

/**
 * Sidecar事件
 */
export interface SidecarEvent {
  /** 事件类型 */
  type: string
  /** 事件数据 */
  data: any
  /** 事件时间 */
  timestamp: Date
  /** 事件来源 */
  source: string
}

/**
 * 零配置选项
 */
export interface ZeroConfigOptions {
  /** 是否启用自动发现 */
  autoDiscovery?: boolean
  /** 是否生成默认配置 */
  generateDefaultConfig?: boolean
  /** 是否启用智能推断 */
  smartInference?: boolean
  /** 自定义推断规则 */
  customRules?: Record<string, any>
}

/**
 * 自动启动选项
 */
export interface AutoStartOptions {
  /** 启动延迟（毫秒） */
  delay?: number
  /** 最大重试次数 */
  maxRetries?: number
  /** 重试间隔（毫秒） */
  retryInterval?: number
  /** 是否在错误时退出 */
  exitOnError?: boolean
  /** 启动前钩子 */
  beforeStart?: () => Promise<void>
  /** 启动后钩子 */
  afterStart?: () => Promise<void>
}