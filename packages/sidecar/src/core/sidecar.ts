/**
 * Sidecar核心实现
 * 提供微前端应用的边车模式集成
 */

import { MicroCore } from '@micro-core/core'
import { logger, MicroCoreError } from '@micro-core/shared'
import { AppDiscovery } from '../discovery/app-discovery.js'
import { ConfigDiscovery } from '../discovery/config-discovery.js'
import { RouteDiscovery } from '../discovery/route-discovery.js'
import { MigrationHelper } from '../migration/migration-helper.js'
import type { 
  SidecarOptions, 
  SidecarConfig, 
  DiscoveryResult,
  MigrationOptions 
} from '../types/index.js'

/**
 * Sidecar类 - 微前端边车模式核心实现
 */
export class Sidecar {
  private microCore: MicroCore
  private appDiscovery: AppDiscovery
  private configDiscovery: ConfigDiscovery
  private routeDiscovery: RouteDiscovery
  private migrationHelper: MigrationHelper
  private config: SidecarConfig
  private isStarted = false

  constructor(options: SidecarOptions = {}) {
    this.config = this.mergeConfig(options)
    this.microCore = new MicroCore(this.config.microCore)
    this.appDiscovery = new AppDiscovery(this.config.discovery)
    this.configDiscovery = new ConfigDiscovery(this.config.discovery)
    this.routeDiscovery = new RouteDiscovery(this.config.discovery)
    this.migrationHelper = new MigrationHelper(this.config.migration)

    logger.info('Sidecar 初始化完成', { config: this.config })
  }

  /**
   * 自动启动微前端系统
   */
  async autoStart(): Promise<void> {
    if (this.isStarted) {
      logger.warn('Sidecar 已经启动，跳过重复启动')
      return
    }

    try {
      logger.info('开始自动启动 Sidecar 模式')

      // 1. 发现应用和配置
      const discoveryResult = await this.discover()
      
      // 2. 注册发现的应用
      await this.registerDiscoveredApps(discoveryResult)
      
      // 3. 启动微前端系统
      await this.microCore.start()
      
      this.isStarted = true
      logger.info('Sidecar 自动启动完成')
    } catch (error) {
      const sidecarError = new MicroCoreError(
        `Sidecar 自动启动失败: ${error instanceof Error ? error.message : String(error)}`,
        'SIDECAR_AUTO_START_FAILED',
        { error, config: this.config }
      )
      logger.error(sidecarError.message, sidecarError.context)
      throw sidecarError
    }
  }

  /**
   * 发现应用、配置和路由
   */
  async discover(): Promise<DiscoveryResult> {
    logger.info('开始发现应用、配置和路由')

    const [apps, configs, routes] = await Promise.all([
      this.appDiscovery.discover(),
      this.configDiscovery.discover(),
      this.routeDiscovery.discover()
    ])

    const result: DiscoveryResult = {
      apps,
      configs,
      routes,
      timestamp: Date.now()
    }

    logger.info('发现完成', { 
      appsCount: apps.length,
      configsCount: configs.length,
      routesCount: routes.length
    })

    return result
  }

  /**
   * 注册发现的应用
   */
  private async registerDiscoveredApps(discoveryResult: DiscoveryResult): Promise<void> {
    const { apps, configs, routes } = discoveryResult

    // 合并配置信息
    for (const app of apps) {
      const appConfig = configs.find(config => config.name === app.name)
      const appRoutes = routes.filter(route => route.appName === app.name)

      const mergedConfig = {
        ...app,
        ...appConfig,
        routes: appRoutes.map(route => route.path)
      }

      await this.microCore.registerApp(mergedConfig)
      logger.info(`注册应用: ${app.name}`, { config: mergedConfig })
    }
  }

  /**
   * 手动注册应用
   */
  async registerApp(config: any): Promise<void> {
    await this.microCore.registerApp(config)
    logger.info(`手动注册应用: ${config.name}`)
  }

  /**
   * 启动微前端系统
   */
  async start(): Promise<void> {
    if (!this.isStarted) {
      await this.microCore.start()
      this.isStarted = true
      logger.info('Sidecar 手动启动完成')
    }
  }

  /**
   * 停止微前端系统
   */
  async stop(): Promise<void> {
    if (this.isStarted) {
      await this.microCore.stop()
      this.isStarted = false
      logger.info('Sidecar 已停止')
    }
  }

  /**
   * 获取微前端核心实例
   */
  getMicroCore(): MicroCore {
    return this.microCore
  }

  /**
   * 获取配置
   */
  getConfig(): SidecarConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(options: Partial<SidecarOptions>): void {
    this.config = this.mergeConfig({ ...this.config, ...options })
    logger.info('Sidecar 配置已更新', { config: this.config })
  }

  /**
   * 执行迁移
   */
  async migrate(options?: MigrationOptions): Promise<void> {
    await this.migrationHelper.migrate(options)
    logger.info('迁移完成')
  }

  /**
   * 合并配置
   */
  private mergeConfig(options: SidecarOptions): SidecarConfig {
    return {
      autoStart: true,
      zeroConfig: true,
      discovery: {
        enabled: true,
        scanPaths: ['./src', './apps', './packages'],
        configFiles: ['micro.config.js', 'micro.config.ts', 'package.json'],
        ...options.discovery
      },
      migration: {
        enabled: true,
        backup: true,
        ...options.migration
      },
      microCore: {
        sandbox: 'proxy',
        ...options.microCore
      },
      ...options
    }
  }
}