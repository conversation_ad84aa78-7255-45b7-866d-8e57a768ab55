/**
 * 自动启动功能
 * 提供一行代码接入的便捷方法
 */

import { Sidecar } from './sidecar.js'
import { logger } from '@micro-core/shared'
import type { SidecarOptions } from '../types/index.js'

let globalSidecar: Sidecar | null = null

/**
 * 一行代码接入微前端
 * @param options Sidecar配置选项
 * @returns Promise<Sidecar>
 * 
 * @example
 * ```typescript
 * import { init } from '@micro-core/sidecar'
 * 
 * // 零配置启动
 * await init()
 * 
 * // 带配置启动
 * await init({
 *   autoStart: true,
 *   discovery: {
 *     scanPaths: ['./src/apps']
 *   }
 * })
 * ```
 */
export async function init(options: SidecarOptions = {}): Promise<Sidecar> {
  try {
    logger.info('开始初始化 Sidecar 模式')

    // 如果已经存在全局实例，先停止它
    if (globalSidecar) {
      await globalSidecar.stop()
      logger.info('停止之前的 Sidecar 实例')
    }

    // 创建新的 Sidecar 实例
    globalSidecar = new Sidecar(options)

    // 如果启用自动启动，则立即启动
    if (options.autoStart !== false) {
      await globalSidecar.autoStart()
    }

    logger.info('Sidecar 初始化完成')
    return globalSidecar
  } catch (error) {
    logger.error('Sidecar 初始化失败', { error, options })
    throw error
  }
}

/**
 * 获取全局 Sidecar 实例
 * @returns Sidecar | null
 */
export function getSidecar(): Sidecar | null {
  return globalSidecar
}

/**
 * 销毁全局 Sidecar 实例
 */
export async function destroy(): Promise<void> {
  if (globalSidecar) {
    await globalSidecar.stop()
    globalSidecar = null
    logger.info('全局 Sidecar 实例已销毁')
  }
}

/**
 * 快速启动方法 - 零配置启动
 * @returns Promise<Sidecar>
 */
export async function quickStart(): Promise<Sidecar> {
  return init({
    autoStart: true,
    zeroConfig: true,
    discovery: {
      enabled: true,
      scanPaths: ['./src', './apps', './packages'],
      configFiles: ['micro.config.js', 'micro.config.ts', 'package.json']
    }
  })
}

/**
 * 开发模式启动
 * @returns Promise<Sidecar>
 */
export async function devStart(): Promise<Sidecar> {
  return init({
    autoStart: true,
    zeroConfig: true,
    discovery: {
      enabled: true,
      scanPaths: ['./src', './apps', './packages'],
      configFiles: ['micro.config.js', 'micro.config.ts', 'package.json'],
      watch: true // 开发模式下启用文件监听
    },
    microCore: {
      sandbox: 'proxy',
      devMode: true
    }
  })
}

/**
 * 生产模式启动
 * @returns Promise<Sidecar>
 */
export async function prodStart(): Promise<Sidecar> {
  return init({
    autoStart: true,
    zeroConfig: false, // 生产模式下不使用零配置
    discovery: {
      enabled: false // 生产模式下禁用自动发现
    },
    microCore: {
      sandbox: 'proxy',
      devMode: false
    }
  })
}