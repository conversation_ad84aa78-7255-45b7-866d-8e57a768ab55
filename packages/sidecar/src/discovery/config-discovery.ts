/**
 * 配置自动发现功能
 * 扫描和发现微前端相关配置文件
 */

import { logger, MicroCoreError } from '@micro-core/shared'
import type { ConfigDiscoveryOptions, DiscoveredConfig } from '../types/discovery.js'

/**
 * 配置发现器
 * 负责扫描和发现微前端配置
 */
export class ConfigDiscovery {
  private options: ConfigDiscoveryOptions

  constructor(options: ConfigDiscoveryOptions = {}) {
    this.options = {
      configFiles: [
        'micro.config.js',
        'micro.config.ts',
        'micro-frontend.config.js',
        'micro-frontend.config.ts',
        'qiankun.config.js',
        'wujie.config.js'
      ],
      scanPaths: ['./', './config', './configs'],
      exclude: ['node_modules', 'dist', 'build'],
      watch: false,
      ...options
    }
  }

  /**
   * 发现所有配置文件
   * @returns Promise<DiscoveredConfig[]>
   */
  async discoverConfigs(): Promise<DiscoveredConfig[]> {
    try {
      logger.info('开始扫描配置文件')
      
      const configs: DiscoveredConfig[] = []
      
      for (const scanPath of this.options.scanPaths) {
        const pathConfigs = await this.scanConfigsInPath(scanPath)
        configs.push(...pathConfigs)
      }

      // 去重和验证
      const uniqueConfigs = this.deduplicateConfigs(configs)
      const validConfigs = await this.validateConfigs(uniqueConfigs)

      logger.info(`发现 ${validConfigs.length} 个配置文件`, { configs: validConfigs })
      return validConfigs
    } catch (error) {
      logger.error('配置发现失败', { error })
      throw new MicroCoreError('配置发现失败', 'CONFIG_DISCOVERY_FAILED', { error })
    }
  }

  /**
   * 扫描指定路径中的配置文件
   * @param path 扫描路径
   * @returns Promise<DiscoveredConfig[]>
   */
  private async scanConfigsInPath(path: string): Promise<DiscoveredConfig[]> {
    const configs: DiscoveredConfig[] = []

    try {
      // 这里应该使用文件系统API扫描目录
      // 由于运行环境限制，这里提供模拟实现
      const mockConfigs = await this.mockScanConfigs(path)
      configs.push(...mockConfigs)
    } catch (error) {
      logger.warn(`扫描配置路径失败: ${path}`, { error })
    }

    return configs
  }

  /**
   * 模拟扫描配置文件（实际实现中应该使用真实的文件系统API）
   * @param path 扫描路径
   * @returns Promise<DiscoveredConfig[]>
   */
  private async mockScanConfigs(path: string): Promise<DiscoveredConfig[]> {
    const mockConfigs: DiscoveredConfig[] = []

    if (path === './') {
      mockConfigs.push({
        name: 'micro.config.js',
        path: './micro.config.js',
        type: 'micro-core',
        content: {
          apps: [
            {
              name: 'main-app',
              entry: 'http://localhost:3000',
              container: '#app'
            }
          ],
          sandbox: 'proxy',
          plugins: ['router', 'communication']
        },
        framework: 'micro-core'
      })
    }

    return mockConfigs
  }

  /**
   * 解析配置文件内容
   * @param filePath 文件路径
   * @returns Promise<any>
   */
  private async parseConfigFile(filePath: string): Promise<any> {
    try {
      // 这里应该根据文件类型解析配置
      // JavaScript/TypeScript 文件需要动态导入
      // JSON 文件需要 JSON.parse
      
      // 模拟解析结果
      return {
        apps: [],
        sandbox: 'proxy',
        plugins: []
      }
    } catch (error) {
      logger.error(`解析配置文件失败: ${filePath}`, { error })
      throw error
    }
  }

  /**
   * 检测配置文件类型
   * @param fileName 文件名
   * @param content 文件内容
   * @returns string
   */
  private detectConfigType(fileName: string, content: any): string {
    if (fileName.includes('qiankun')) {
      return 'qiankun'
    }
    
    if (fileName.includes('wujie')) {
      return 'wujie'
    }
    
    if (fileName.includes('micro-app')) {
      return 'micro-app'
    }
    
    if (fileName.includes('micro')) {
      return 'micro-core'
    }

    // 根据内容判断
    if (content.registerMicroApps) {
      return 'qiankun'
    }
    
    if (content.setupApp) {
      return 'wujie'
    }

    return 'unknown'
  }

  /**
   * 检测配置框架类型
   * @param configType 配置类型
   * @param content 配置内容
   * @returns string
   */
  private detectFramework(configType: string, content: any): string {
    switch (configType) {
      case 'qiankun':
        return 'qiankun'
      case 'wujie':
        return 'wujie'
      case 'micro-app':
        return 'micro-app'
      case 'micro-core':
        return 'micro-core'
      default:
        return 'unknown'
    }
  }

  /**
   * 去重配置列表
   * @param configs 配置列表
   * @returns DiscoveredConfig[]
   */
  private deduplicateConfigs(configs: DiscoveredConfig[]): DiscoveredConfig[] {
    const uniqueConfigs = new Map<string, DiscoveredConfig>()

    for (const config of configs) {
      const key = `${config.path}:${config.name}`
      if (!uniqueConfigs.has(key)) {
        uniqueConfigs.set(key, config)
      }
    }

    return Array.from(uniqueConfigs.values())
  }

  /**
   * 验证配置列表
   * @param configs 配置列表
   * @returns Promise<DiscoveredConfig[]>
   */
  private async validateConfigs(configs: DiscoveredConfig[]): Promise<DiscoveredConfig[]> {
    const validConfigs: DiscoveredConfig[] = []

    for (const config of configs) {
      if (await this.validateConfig(config)) {
        validConfigs.push(config)
      }
    }

    return validConfigs
  }

  /**
   * 验证单个配置
   * @param config 配置信息
   * @returns Promise<boolean>
   */
  private async validateConfig(config: DiscoveredConfig): Promise<boolean> {
    try {
      // 验证必要字段
      if (!config.name || !config.path) {
        logger.warn('配置缺少必要字段', { config })
        return false
      }

      // 验证配置内容
      if (!config.content) {
        logger.warn('配置内容为空', { config })
        return false
      }

      return true
    } catch (error) {
      logger.warn('配置验证失败', { config, error })
      return false
    }
  }

  /**
   * 监听配置变化
   * @param callback 变化回调函数
   * @returns void
   */
  watchConfigs(callback: (configs: DiscoveredConfig[]) => void): void {
    if (!this.options.watch) {
      return
    }

    // 这里应该使用文件系统监听API
    // 由于运行环境限制，这里提供模拟实现
    logger.info('开始监听配置变化')

    // 模拟定期检查
    setInterval(async () => {
      try {
        const configs = await this.discoverConfigs()
        callback(configs)
      } catch (error) {
        logger.error('监听配置变化失败', { error })
      }
    }, 3000) // 每3秒检查一次
  }

  /**
   * 合并多个配置
   * @param configs 配置列表
   * @returns any
   */
  mergeConfigs(configs: DiscoveredConfig[]): any {
    const mergedConfig = {
      apps: [],
      plugins: [],
      sandbox: 'proxy',
      communication: {},
      performance: {}
    }

    for (const config of configs) {
      if (config.content.apps) {
        mergedConfig.apps.push(...config.content.apps)
      }
      
      if (config.content.plugins) {
        mergedConfig.plugins.push(...config.content.plugins)
      }
      
      if (config.content.sandbox) {
        mergedConfig.sandbox = config.content.sandbox
      }
      
      if (config.content.communication) {
        Object.assign(mergedConfig.communication, config.content.communication)
      }
      
      if (config.content.performance) {
        Object.assign(mergedConfig.performance, config.content.performance)
      }
    }

    // 去重应用
    const uniqueApps = new Map()
    for (const app of mergedConfig.apps) {
      uniqueApps.set(app.name, app)
    }
    mergedConfig.apps = Array.from(uniqueApps.values())

    // 去重插件
    mergedConfig.plugins = [...new Set(mergedConfig.plugins)]

    return mergedConfig
  }
}

/**
 * 创建配置发现器实例
 * @param options 发现选项
 * @returns ConfigDiscovery
 */
export function createConfigDiscovery(options?: ConfigDiscoveryOptions): ConfigDiscovery {
  return new ConfigDiscovery(options)
}