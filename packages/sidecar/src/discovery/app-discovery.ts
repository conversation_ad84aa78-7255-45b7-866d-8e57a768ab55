/**
 * 应用自动发现功能
 * 扫描项目结构，自动发现微前端应用
 */

import { logger, MicroCoreError } from '@micro-core/shared'
import type { AppDiscoveryOptions, DiscoveredApp } from '../types/discovery.js'

/**
 * 应用发现器
 * 负责扫描和发现微前端应用
 */
export class AppDiscovery {
  private options: AppDiscoveryOptions

  constructor(options: AppDiscoveryOptions = {}) {
    this.options = {
      scanPaths: ['./src', './apps', './packages'],
      configFiles: ['package.json', 'micro.config.js', 'micro.config.ts'],
      exclude: ['node_modules', 'dist', 'build'],
      watch: false,
      ...options
    }
  }

  /**
   * 发现所有微前端应用
   * @returns Promise<DiscoveredApp[]>
   */
  async discoverApps(): Promise<DiscoveredApp[]> {
    try {
      logger.info('开始扫描微前端应用')
      
      const apps: DiscoveredApp[] = []
      
      for (const scanPath of this.options.scanPaths) {
        const pathApps = await this.scanPath(scanPath)
        apps.push(...pathApps)
      }

      // 去重和验证
      const uniqueApps = this.deduplicateApps(apps)
      const validApps = await this.validateApps(uniqueApps)

      logger.info(`发现 ${validApps.length} 个微前端应用`, { apps: validApps })
      return validApps
    } catch (error) {
      logger.error('应用发现失败', { error })
      throw new MicroCoreError('应用发现失败', 'APP_DISCOVERY_FAILED', { error })
    }
  }

  /**
   * 扫描指定路径
   * @param path 扫描路径
   * @returns Promise<DiscoveredApp[]>
   */
  private async scanPath(path: string): Promise<DiscoveredApp[]> {
    const apps: DiscoveredApp[] = []

    try {
      // 这里应该使用文件系统API扫描目录
      // 由于运行环境限制，这里提供模拟实现
      const mockApps = await this.mockScanPath(path)
      apps.push(...mockApps)
    } catch (error) {
      logger.warn(`扫描路径失败: ${path}`, { error })
    }

    return apps
  }

  /**
   * 模拟扫描路径（实际实现中应该使用真实的文件系统API）
   * @param path 扫描路径
   * @returns Promise<DiscoveredApp[]>
   */
  private async mockScanPath(path: string): Promise<DiscoveredApp[]> {
    // 模拟发现的应用
    const mockApps: DiscoveredApp[] = []

    if (path === './apps') {
      mockApps.push(
        {
          name: 'main-app',
          path: './apps/main-app',
          entry: './apps/main-app/dist/index.js',
          framework: 'vue3',
          type: 'main',
          packageJson: {
            name: 'main-app',
            version: '1.0.0',
            dependencies: {
              'vue': '^3.0.0'
            }
          }
        },
        {
          name: 'sub-react',
          path: './apps/sub-react',
          entry: './apps/sub-react/dist/index.js',
          framework: 'react',
          type: 'sub',
          packageJson: {
            name: 'sub-react',
            version: '1.0.0',
            dependencies: {
              'react': '^18.0.0'
            }
          }
        }
      )
    }

    return mockApps
  }

  /**
   * 检测应用框架类型
   * @param packageJson package.json内容
   * @returns string
   */
  private detectFramework(packageJson: any): string {
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies }

    if (dependencies.react) {
      return 'react'
    }
    
    if (dependencies.vue && dependencies.vue.startsWith('^3')) {
      return 'vue3'
    }
    
    if (dependencies.vue && dependencies.vue.startsWith('^2')) {
      return 'vue2'
    }
    
    if (dependencies['@angular/core']) {
      return 'angular'
    }
    
    if (dependencies.svelte) {
      return 'svelte'
    }
    
    if (dependencies['solid-js']) {
      return 'solid'
    }
    
    if (dependencies.lit) {
      return 'lit'
    }

    return 'vanilla'
  }

  /**
   * 检测应用类型（主应用或子应用）
   * @param packageJson package.json内容
   * @param path 应用路径
   * @returns string
   */
  private detectAppType(packageJson: any, path: string): 'main' | 'sub' {
    // 检查package.json中的配置
    if (packageJson.microCore?.type) {
      return packageJson.microCore.type
    }

    // 根据路径判断
    if (path.includes('main') || path.includes('host') || path.includes('shell')) {
      return 'main'
    }

    return 'sub'
  }

  /**
   * 去重应用列表
   * @param apps 应用列表
   * @returns DiscoveredApp[]
   */
  private deduplicateApps(apps: DiscoveredApp[]): DiscoveredApp[] {
    const uniqueApps = new Map<string, DiscoveredApp>()

    for (const app of apps) {
      if (!uniqueApps.has(app.name)) {
        uniqueApps.set(app.name, app)
      }
    }

    return Array.from(uniqueApps.values())
  }

  /**
   * 验证应用列表
   * @param apps 应用列表
   * @returns Promise<DiscoveredApp[]>
   */
  private async validateApps(apps: DiscoveredApp[]): Promise<DiscoveredApp[]> {
    const validApps: DiscoveredApp[] = []

    for (const app of apps) {
      if (await this.validateApp(app)) {
        validApps.push(app)
      }
    }

    return validApps
  }

  /**
   * 验证单个应用
   * @param app 应用信息
   * @returns Promise<boolean>
   */
  private async validateApp(app: DiscoveredApp): Promise<boolean> {
    try {
      // 验证必要字段
      if (!app.name || !app.path) {
        logger.warn('应用缺少必要字段', { app })
        return false
      }

      // 验证路径是否存在
      // 这里应该使用文件系统API检查路径
      // 由于运行环境限制，这里假设路径有效

      return true
    } catch (error) {
      logger.warn('应用验证失败', { app, error })
      return false
    }
  }

  /**
   * 监听应用变化
   * @param callback 变化回调函数
   * @returns void
   */
  watchApps(callback: (apps: DiscoveredApp[]) => void): void {
    if (!this.options.watch) {
      return
    }

    // 这里应该使用文件系统监听API
    // 由于运行环境限制，这里提供模拟实现
    logger.info('开始监听应用变化')

    // 模拟定期检查
    setInterval(async () => {
      try {
        const apps = await this.discoverApps()
        callback(apps)
      } catch (error) {
        logger.error('监听应用变化失败', { error })
      }
    }, 5000) // 每5秒检查一次
  }
}

/**
 * 创建应用发现器实例
 * @param options 发现选项
 * @returns AppDiscovery
 */
export function createAppDiscovery(options?: AppDiscoveryOptions): AppDiscovery {
  return new AppDiscovery(options)
}