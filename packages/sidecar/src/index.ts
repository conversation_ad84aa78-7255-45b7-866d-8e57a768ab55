/**
 * @micro-core/sidecar
 * Sidecar模式实现，提供一行代码接入和零配置启动
 */

export * from './core/index.js'
export * from './discovery/index.js'
export * from './migration/index.js'
export * from './types/index.js'

// 主要导出
export { Sidecar } from './core/sidecar.js'
export { init } from './core/auto-start.js'
export { zeroConfig } from './core/zero-config.js'

// 发现功能
export { AppDiscovery } from './discovery/app-discovery.js'
export { ConfigDiscovery } from './discovery/config-discovery.js'
export { RouteDiscovery } from './discovery/route-discovery.js'

// 迁移功能
export { MigrationHelper } from './migration/migration-helper.js'
export { CompatibilityChecker } from './migration/compatibility.js'
export { UpgradeTool } from './migration/upgrade.js'