# @micro-core/builders

微前端构建工具适配系统，支持多种主流构建工具的微前端集成。

## 特性

- 🛠️ **多构建工具支持** - 支持 Webpack、Rollup、Vite、ESBuild、Parcel、Rspack、Turbopack
- 🔧 **统一接口** - 提供统一的构建器接口和配置管理
- ⚡ **高性能** - 针对微前端场景优化的构建配置
- 🎯 **插件化** - 支持构建插件的动态加载和配置
- 🛡️ **类型安全** - 完整的 TypeScript 类型定义

## 安装

```bash
pnpm add @micro-core/builders
```

## 基础使用

### 自动创建构建器

```typescript
import { BuilderFactory } from '@micro-core/builders'

const builder = BuilderFactory.createBuilder({
  type: 'vite',
  name: 'my-micro-app',
  entry: './src/index.ts',
  outputDir: './dist'
})

// 初始化构建器
await builder.initialize()

// 构建应用
const result = await builder.build()
console.log('构建结果:', result)

// 开发模式
const devResult = await builder.dev()
console.log('开发服务器启动:', devResult)
```

### 手动指定构建工具

```typescript
import { ViteBuilder } from '@micro-core/builders'

const builder = new ViteBuilder({
  type: 'vite',
  name: 'vite-app',
  entry: './src/main.ts',
  outputDir: './dist',
  options: {
    production: false,
    sourcemap: true,
    hmr: true,
    port: 3000
  }
})
```

### 构建配置

```typescript
const config = {
  type: 'webpack',
  name: 'webpack-app',
  entry: './src/index.js',
  outputDir: './dist',
  basePath: '/micro-app/',
  env: {
    API_URL: 'https://api.example.com'
  },
  options: {
    production: true,
    sourcemap: true,
    minify: true,
    codeSplitting: true,
    external: ['react', 'react-dom'],
    globals: {
      'react': 'React',
      'react-dom': 'ReactDOM'
    }
  },
  plugins: [
    {
      name: 'html-webpack-plugin',
      enabled: true,
      options: {
        template: './public/index.html'
      }
    }
  ],
  customConfig: {
    resolve: {
      alias: {
        '@': './src'
      }
    }
  }
}

const builder = BuilderFactory.createBuilder(config)
```

## 支持的构建工具

### Webpack
- 支持版本：5.x
- 特性：完整的模块打包、代码分割、插件生态
- 适用场景：复杂的企业级应用

### Vite
- 支持版本：7.x
- 特性：快速冷启动、热更新、ES模块
- 适用场景：现代化前端应用

### Rollup
- 支持版本：4.x
- 特性：Tree-shaking、ES模块输出、库打包
- 适用场景：库和组件开发

### ESBuild
- 支持版本：0.19.x
- 特性：极速构建、Go语言实现、内置压缩
- 适用场景：快速原型和简单应用

### Parcel
- 支持版本：2.x
- 特性：零配置、自动依赖解析、多目标输出
- 适用场景：快速开发和原型验证

### Rspack
- 支持版本：0.4.x
- 特性：Rust实现、Webpack兼容、高性能
- 适用场景：大型项目的性能优化

### Turbopack
- 支持版本：实验性
- 特性：增量构建、Rust实现、Next.js集成
- 适用场景：Next.js项目和实验性项目

## 构建器配置

```typescript
interface BuilderConfig {
  type: BuilderType                    // 构建工具类型
  name: string                         // 项目名称
  entry: string | string[] | Record<string, string>  // 入口文件
  outputDir: string                    // 输出目录
  basePath?: string                    // 基础路径
  env?: Record<string, string>         // 环境变量
  options?: BuilderOptions             // 构建选项
  plugins?: PluginConfig[]             // 插件配置
  customConfig?: Record<string, any>   // 自定义配置
}
```

## 构建选项

```typescript
interface BuilderOptions {
  production?: boolean      // 是否为生产环境
  sourcemap?: boolean      // 是否启用源码映射
  codeSplitting?: boolean  // 是否启用代码分割
  minify?: boolean         // 是否启用压缩
  hmr?: boolean           // 是否启用热更新
  port?: number           // 开发服务器端口
  host?: string           // 开发服务器主机
  open?: boolean          // 是否自动打开浏览器
  proxy?: Record<string, string | ProxyConfig>  // 代理配置
  external?: string[]      // 外部依赖
  globals?: Record<string, string>  // 全局变量
  target?: string | string[]        // 目标环境
  format?: string | string[]        // 输出格式
}
```

## 插件系统

```typescript
// 添加插件
const builder = BuilderFactory.createBuilder({
  type: 'vite',
  name: 'my-app',
  entry: './src/index.ts',
  outputDir: './dist',
  plugins: [
    {
      name: '@vitejs/plugin-react',
      enabled: true,
      options: {
        jsxRuntime: 'automatic'
      }
    },
    {
      name: '@vitejs/plugin-vue',
      enabled: true,
      condition: (config) => config.name.includes('vue')
    }
  ]
})

// 自定义插件
class CustomPlugin extends BuilderPlugin {
  name = 'custom-plugin'
  version = '1.0.0'

  apply(config: BuilderConfig) {
    // 插件逻辑
  }

  supports(builderType: BuilderType) {
    return ['vite', 'webpack'].includes(builderType)
  }
}
```

## 构建结果

```typescript
interface BuilderResult {
  success: boolean          // 是否成功
  duration: number         // 构建时间
  outputs: BuilderOutput[] // 输出文件
  stats: BuilderStats      // 构建统计
  errors?: string[]        // 错误信息
  warnings?: string[]      // 警告信息
}
```

## 开发模式

```typescript
// 启动开发服务器
const devResult = await builder.dev()

// 监听文件变化
builder.on('fileChange', (filepath) => {
  console.log('文件变更:', filepath)
})

// 监听构建完成
builder.on('buildComplete', (result) => {
  console.log('构建完成:', result)
})
```

## 生产构建

```typescript
// 清理旧文件
await builder.clean()

// 生产构建
const buildResult = await builder.build()

if (buildResult.success) {
  console.log('构建成功!')
  console.log('输出文件:', buildResult.outputs)
  console.log('构建统计:', buildResult.stats)
} else {
  console.error('构建失败:', buildResult.errors)
}
```

## 自定义构建器

```typescript
import { BuilderBase } from '@micro-core/builders'

class CustomBuilder extends BuilderBase {
  get builderType() {
    return 'custom'
  }

  get version() {
    return '1.0.0'
  }

  async initialize() {
    // 初始化逻辑
  }

  async build() {
    // 构建逻辑
  }

  async dev() {
    // 开发模式逻辑
  }

  async clean() {
    // 清理逻辑
  }
}
```

## API 文档

### BuilderFactory

#### 方法

- `createBuilder(config: BuilderConfig): BuilderBase` - 创建构建器实例
- `getSupportedBuilders(): BuilderType[]` - 获取支持的构建器列表
- `isSupported(builderType: BuilderType): boolean` - 检查是否支持指定构建器
- `detectBuilder(projectPath: string): BuilderType` - 自动检测项目构建工具
- `getDefaultConfig(builderType: BuilderType): Partial<BuilderConfig>` - 获取默认配置

### BuilderBase

#### 属性

- `builderType: BuilderType` - 构建器类型
- `version: string` - 构建器版本

#### 方法

- `initialize(): Promise<void>` - 初始化构建器
- `build(): Promise<BuilderResult>` - 构建应用
- `dev(): Promise<BuilderResult>` - 开发模式构建
- `clean(): Promise<void>` - 清理构建产物
- `getConfig(): BuilderConfig` - 获取构建配置
- `getContext(): BuilderContext | null` - 获取构建上下文

## 许可证

MIT