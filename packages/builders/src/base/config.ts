/**
 * 构建器配置管理
 */

import type { BuilderConfig, BuilderType, PluginConfig } from '../types'

/**
 * 构建器配置管理器
 */
export class BuilderConfigManager {
  private config: BuilderConfig

  constructor(config: BuilderConfig) {
    this.config = config
  }

  /**
   * 获取配置
   */
  getConfig(): BuilderConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<BuilderConfig>): void {
    this.config = { ...this.config, ...updates }
  }

  /**
   * 合并配置
   */
  mergeConfig(config: Partial<BuilderConfig>): BuilderConfig {
    return {
      ...this.config,
      ...config,
      options: {
        ...this.config.options,
        ...config.options
      },
      plugins: [
        ...(this.config.plugins || []),
        ...(config.plugins || [])
      ],
      customConfig: {
        ...this.config.customConfig,
        ...config.customConfig
      }
    }
  }

  /**
   * 验证配置
   */
  validateConfig(): void {
    if (!this.config.type) {
      throw new Error('构建器类型不能为空')
    }

    if (!this.config.name) {
      throw new Error('项目名称不能为空')
    }

    if (!this.config.entry) {
      throw new Error('入口文件不能为空')
    }

    if (!this.config.outputDir) {
      throw new Error('输出目录不能为空')
    }

    // 验证插件配置
    if (this.config.plugins) {
      this.config.plugins.forEach(plugin => {
        if (!plugin.name) {
          throw new Error('插件名称不能为空')
        }
      })
    }
  }

  /**
   * 获取启用的插件
   */
  getEnabledPlugins(): PluginConfig[] {
    if (!this.config.plugins) {
      return []
    }

    return this.config.plugins.filter(plugin => {
      // 检查是否启用
      if (plugin.enabled === false) {
        return false
      }

      // 检查应用条件
      if (plugin.condition && !plugin.condition(this.config)) {
        return false
      }

      return true
    })
  }

  /**
   * 添加插件
   */
  addPlugin(plugin: PluginConfig): void {
    if (!this.config.plugins) {
      this.config.plugins = []
    }

    // 检查是否已存在同名插件
    const existingIndex = this.config.plugins.findIndex(p => p.name === plugin.name)
    if (existingIndex >= 0) {
      this.config.plugins[existingIndex] = plugin
    } else {
      this.config.plugins.push(plugin)
    }
  }

  /**
   * 移除插件
   */
  removePlugin(pluginName: string): void {
    if (!this.config.plugins) {
      return
    }

    this.config.plugins = this.config.plugins.filter(plugin => plugin.name !== pluginName)
  }

  /**
   * 获取环境变量
   */
  getEnvVars(): Record<string, string> {
    return {
      NODE_ENV: this.config.options?.production ? 'production' : 'development',
      ...this.config.env
    }
  }

  /**
   * 是否为生产环境
   */
  isProduction(): boolean {
    return this.config.options?.production === true
  }

  /**
   * 是否为开发环境
   */
  isDevelopment(): boolean {
    return !this.isProduction()
  }
}

/**
 * 构建器插件基类
 */
export abstract class BuilderPlugin {
  abstract name: string
  abstract version: string

  /**
   * 应用插件
   */
  abstract apply(config: BuilderConfig): void

  /**
   * 检查是否支持指定构建器
   */
  supports(builderType: BuilderType): boolean {
    return true
  }

  /**
   * 获取插件选项
   */
  protected getOptions(config: BuilderConfig): Record<string, any> {
    const plugin = config.plugins?.find(p => p.name === this.name)
    return plugin?.options || {}
  }
}