/**
 * 构建器工厂
 * 
 * 根据配置自动创建对应的构建器实例
 */

import { WebpackBuilder } from '../tools/webpack'
import { RollupBuilder } from '../tools/rollup'
import { ParcelBuilder } from '../tools/parcel'
import { ViteBuilder } from '../tools/vite'
import { EsbuildBuilder } from '../tools/esbuild'
import { RspackBuilder } from '../tools/rspack'
import { TurbopackBuilder } from '../tools/turbopack'
import type { BuilderBase } from './builder-base'
import type { BuilderConfig, BuilderType } from '../types'

export class BuilderFactory {
  /**
   * 创建构建器实例
   */
  static createBuilder(config: BuilderConfig): BuilderBase {
    switch (config.type) {
      case 'webpack':
        return new WebpackBuilder(config)
      case 'rollup':
        return new RollupBuilder(config)
      case 'parcel':
        return new ParcelBuilder(config)
      case 'vite':
        return new ViteBuilder(config)
      case 'esbuild':
        return new EsbuildBuilder(config)
      case 'rspack':
        return new RspackBuilder(config)
      case 'turbopack':
        return new TurbopackBuilder(config)
      default:
        throw new Error(`不支持的构建器类型: ${config.type}`)
    }
  }

  /**
   * 获取支持的构建器类型列表
   */
  static getSupportedBuilders(): BuilderType[] {
    return [
      'webpack',
      'rollup',
      'parcel',
      'vite',
      'esbuild',
      'rspack',
      'turbopack'
    ]
  }

  /**
   * 检查是否支持指定构建器
   */
  static isSupported(builderType: BuilderType): boolean {
    return this.getSupportedBuilders().includes(builderType)
  }

  /**
   * 自动检测项目使用的构建工具
   */
  static detectBuilder(projectPath: string): BuilderType {
    // 这里可以通过检查项目文件来自动检测构建工具
    // 简化实现，实际应该检查配置文件和依赖
    
    // 检查配置文件
    const configFiles = {
      'webpack.config.js': 'webpack',
      'webpack.config.ts': 'webpack',
      'rollup.config.js': 'rollup',
      'rollup.config.ts': 'rollup',
      'vite.config.js': 'vite',
      'vite.config.ts': 'vite',
      'parcel.config.js': 'parcel',
      'rspack.config.js': 'rspack',
      'turbo.json': 'turbopack'
    } as const

    // 这里应该实际检查文件系统
    // 简化实现，返回默认值
    return 'vite'
  }

  /**
   * 根据项目自动创建构建器
   */
  static createBuilderFromProject(projectPath: string, overrides?: Partial<BuilderConfig>): BuilderBase {
    const detectedType = this.detectBuilder(projectPath)
    
    const config: BuilderConfig = {
      type: detectedType,
      name: 'auto-detected-project',
      entry: './src/index.ts',
      outputDir: './dist',
      ...overrides
    }

    return this.createBuilder(config)
  }

  /**
   * 获取构建器的默认配置
   */
  static getDefaultConfig(builderType: BuilderType): Partial<BuilderConfig> {
    const commonConfig = {
      entry: './src/index.ts',
      outputDir: './dist',
      options: {
        production: false,
        sourcemap: true,
        minify: false,
        hmr: true,
        port: 3000
      }
    }

    const specificConfigs: Record<BuilderType, Partial<BuilderConfig>> = {
      webpack: {
        ...commonConfig,
        plugins: [
          { name: 'html-webpack-plugin', enabled: true },
          { name: 'mini-css-extract-plugin', enabled: true }
        ]
      },
      rollup: {
        ...commonConfig,
        options: {
          ...commonConfig.options,
          format: ['es', 'cjs']
        }
      },
      vite: {
        ...commonConfig,
        plugins: [
          { name: '@vitejs/plugin-react', enabled: true },
          { name: '@vitejs/plugin-vue', enabled: true }
        ]
      },
      esbuild: {
        ...commonConfig,
        options: {
          ...commonConfig.options,
          format: 'esm',
          target: 'es2020'
        }
      },
      parcel: {
        ...commonConfig,
        options: {
          ...commonConfig.options,
          target: 'browser'
        }
      },
      rspack: {
        ...commonConfig,
        plugins: [
          { name: '@rspack/plugin-html', enabled: true }
        ]
      },
      turbopack: {
        ...commonConfig,
        options: {
          ...commonConfig.options,
          experimental: true
        }
      }
    }

    return {
      type: builderType,
      ...specificConfigs[builderType]
    }
  }

  /**
   * 验证构建器配置
   */
  static validateConfig(config: BuilderConfig): void {
    if (!config.type) {
      throw new Error('构建器类型不能为空')
    }

    if (!this.isSupported(config.type)) {
      throw new Error(`不支持的构建器类型: ${config.type}`)
    }

    if (!config.name) {
      throw new Error('项目名称不能为空')
    }

    if (!config.entry) {
      throw new Error('入口文件不能为空')
    }

    if (!config.outputDir) {
      throw new Error('输出目录不能为空')
    }
  }
}