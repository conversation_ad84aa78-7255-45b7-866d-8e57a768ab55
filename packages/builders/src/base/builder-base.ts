/**
 * 构建器基类
 * 
 * 定义所有构建工具适配器的统一接口
 */

import type { BuilderConfig, BuilderContext, BuilderType, BuilderResult } from '../types'

export abstract class BuilderBase {
  protected config: BuilderConfig
  protected context: BuilderContext | null = null

  constructor(config: BuilderConfig) {
    this.config = config
  }

  /**
   * 获取构建器类型
   */
  abstract get builderType(): BuilderType

  /**
   * 获取构建器版本
   */
  abstract get version(): string

  /**
   * 初始化构建器
   */
  abstract initialize(): Promise<void>

  /**
   * 构建应用
   */
  abstract build(): Promise<BuilderResult>

  /**
   * 开发模式构建
   */
  abstract dev(): Promise<BuilderResult>

  /**
   * 清理构建产物
   */
  abstract clean(): Promise<void>

  /**
   * 获取构建配置
   */
  getConfig(): BuilderConfig {
    return { ...this.config }
  }

  /**
   * 获取构建上下文
   */
  getContext(): BuilderContext | null {
    return this.context
  }

  /**
   * 设置构建上下文
   */
  protected setContext(context: BuilderContext): void {
    this.context = context
  }

  /**
   * 验证构建配置
   */
  protected validateConfig(): void {
    if (!this.config.entry) {
      throw new Error('构建入口不能为空')
    }
    if (!this.config.outputDir) {
      throw new Error('输出目录不能为空')
    }
  }

  /**
   * 处理构建错误
   */
  protected handleError(error: Error, operation: string): never {
    const message = `${this.builderType} 构建器在 ${operation} 操作中发生错误: ${error.message}`
    throw new Error(message)
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    this.context = null
  }
}