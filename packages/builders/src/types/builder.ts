/**
 * 构建器相关类型定义
 */

/**
 * 支持的构建工具类型
 */
export type BuilderType = 
  | 'webpack'
  | 'rollup'
  | 'parcel'
  | 'vite'
  | 'esbuild'
  | 'rspack'
  | 'turbopack'

/**
 * 构建器配置
 */
export interface BuilderConfig {
  /** 构建工具类型 */
  type: BuilderType
  /** 项目名称 */
  name: string
  /** 入口文件 */
  entry: string | string[] | Record<string, string>
  /** 输出目录 */
  outputDir: string
  /** 基础路径 */
  basePath?: string
  /** 环境变量 */
  env?: Record<string, string>
  /** 构建选项 */
  options?: BuilderOptions
  /** 插件配置 */
  plugins?: PluginConfig[]
  /** 自定义配置 */
  customConfig?: Record<string, any>
}

/**
 * 构建器上下文
 */
export interface BuilderContext {
  /** 构建开始时间 */
  startTime: number
  /** 构建状态 */
  status: 'idle' | 'building' | 'success' | 'error'
  /** 构建结果 */
  result?: BuilderResult
  /** 错误信息 */
  error?: Error
  /** 构建统计 */
  stats?: BuilderStats
}

/**
 * 构建器选项
 */
export interface BuilderOptions {
  /** 是否为生产环境 */
  production?: boolean
  /** 是否启用源码映射 */
  sourcemap?: boolean
  /** 是否启用代码分割 */
  codeSplitting?: boolean
  /** 是否启用压缩 */
  minify?: boolean
  /** 是否启用热更新 */
  hmr?: boolean
  /** 开发服务器端口 */
  port?: number
  /** 开发服务器主机 */
  host?: string
  /** 是否自动打开浏览器 */
  open?: boolean
  /** 代理配置 */
  proxy?: Record<string, string | ProxyConfig>
  /** 外部依赖 */
  external?: string[]
  /** 全局变量 */
  globals?: Record<string, string>
  /** 目标环境 */
  target?: string | string[]
  /** 输出格式 */
  format?: string | string[]
}

/**
 * 构建结果
 */
export interface BuilderResult {
  /** 是否成功 */
  success: boolean
  /** 构建时间 */
  duration: number
  /** 输出文件 */
  outputs: BuilderOutput[]
  /** 构建统计 */
  stats: BuilderStats
  /** 错误信息 */
  errors?: string[]
  /** 警告信息 */
  warnings?: string[]
}

/**
 * 构建输出
 */
export interface BuilderOutput {
  /** 文件名 */
  filename: string
  /** 文件路径 */
  filepath: string
  /** 文件大小 */
  size: number
  /** 文件类型 */
  type: 'js' | 'css' | 'html' | 'asset'
  /** 是否为入口文件 */
  isEntry?: boolean
  /** 是否为动态导入 */
  isDynamic?: boolean
}

/**
 * 构建统计
 */
export interface BuilderStats {
  /** 总文件数 */
  totalFiles: number
  /** 总大小 */
  totalSize: number
  /** 入口文件数 */
  entryCount: number
  /** 动态导入数 */
  dynamicImportCount: number
  /** 依赖数量 */
  dependencyCount: number
  /** 构建时间 */
  buildTime: number
}

/**
 * 插件配置
 */
export interface PluginConfig {
  /** 插件名称 */
  name: string
  /** 插件选项 */
  options?: Record<string, any>
  /** 是否启用 */
  enabled?: boolean
  /** 应用条件 */
  condition?: (config: BuilderConfig) => boolean
}

/**
 * 代理配置
 */
export interface ProxyConfig {
  /** 目标地址 */
  target: string
  /** 是否改变源 */
  changeOrigin?: boolean
  /** 路径重写 */
  pathRewrite?: Record<string, string>
  /** 是否启用 WebSocket */
  ws?: boolean
  /** 是否启用 HTTPS */
  secure?: boolean
}

/**
 * 构建钩子
 */
export interface BuilderHooks {
  /** 构建开始前 */
  beforeBuild?: (config: BuilderConfig) => Promise<void> | void
  /** 构建开始后 */
  afterBuild?: (result: BuilderResult) => Promise<void> | void
  /** 构建错误 */
  onError?: (error: Error) => Promise<void> | void
  /** 文件变更 */
  onFileChange?: (filepath: string) => Promise<void> | void
}