/**
 * 各构建工具的配置类型定义
 */

/**
 * Webpack 配置
 */
export interface WebpackConfig {
  /** 模式 */
  mode?: 'development' | 'production' | 'none'
  /** 入口 */
  entry?: string | string[] | Record<string, string>
  /** 输出 */
  output?: {
    path?: string
    filename?: string
    publicPath?: string
    library?: string
    libraryTarget?: string
  }
  /** 模块 */
  module?: {
    rules?: WebpackRule[]
  }
  /** 插件 */
  plugins?: any[]
  /** 解析 */
  resolve?: {
    extensions?: string[]
    alias?: Record<string, string>
  }
  /** 开发服务器 */
  devServer?: {
    port?: number
    host?: string
    hot?: boolean
    open?: boolean
    proxy?: Record<string, any>
  }
  /** 优化 */
  optimization?: {
    minimize?: boolean
    splitChunks?: any
  }
  /** 外部依赖 */
  externals?: Record<string, string> | string[]
}

/**
 * Webpack 规则
 */
export interface WebpackRule {
  test?: RegExp
  use?: string | string[] | any[]
  exclude?: RegExp | string
  include?: RegExp | string
}

/**
 * Rollup 配置
 */
export interface RollupConfig {
  /** 输入 */
  input?: string | string[] | Record<string, string>
  /** 输出 */
  output?: RollupOutput | RollupOutput[]
  /** 插件 */
  plugins?: any[]
  /** 外部依赖 */
  external?: string[] | ((id: string) => boolean)
  /** 监听 */
  watch?: {
    include?: string | string[]
    exclude?: string | string[]
  }
}

/**
 * Rollup 输出配置
 */
export interface RollupOutput {
  file?: string
  dir?: string
  format?: 'es' | 'cjs' | 'umd' | 'iife' | 'amd'
  name?: string
  globals?: Record<string, string>
  sourcemap?: boolean
}

/**
 * Vite 配置
 */
export interface ViteConfig {
  /** 根目录 */
  root?: string
  /** 基础路径 */
  base?: string
  /** 模式 */
  mode?: string
  /** 构建 */
  build?: {
    target?: string | string[]
    outDir?: string
    assetsDir?: string
    sourcemap?: boolean
    minify?: boolean | 'terser' | 'esbuild'
    lib?: {
      entry: string
      name?: string
      formats?: ('es' | 'cjs' | 'umd' | 'iife')[]
      fileName?: string | ((format: string) => string)
    }
    rollupOptions?: RollupConfig
  }
  /** 服务器 */
  server?: {
    port?: number
    host?: string | boolean
    open?: boolean | string
    proxy?: Record<string, string | ProxyOptions>
    hmr?: boolean | { port?: number }
  }
  /** 插件 */
  plugins?: any[]
  /** 解析 */
  resolve?: {
    alias?: Record<string, string>
    extensions?: string[]
  }
  /** 定义 */
  define?: Record<string, any>
}

/**
 * Vite 代理选项
 */
export interface ProxyOptions {
  target: string
  changeOrigin?: boolean
  rewrite?: (path: string) => string
  ws?: boolean
  secure?: boolean
}

/**
 * ESBuild 配置
 */
export interface EsbuildConfig {
  /** 入口点 */
  entryPoints?: string[] | Record<string, string>
  /** 输出目录 */
  outdir?: string
  /** 输出文件 */
  outfile?: string
  /** 包 */
  bundle?: boolean
  /** 格式 */
  format?: 'iife' | 'cjs' | 'esm'
  /** 目标 */
  target?: string | string[]
  /** 平台 */
  platform?: 'browser' | 'node' | 'neutral'
  /** 外部依赖 */
  external?: string[]
  /** 加载器 */
  loader?: Record<string, string>
  /** 解析 */
  resolveExtensions?: string[]
  /** 压缩 */
  minify?: boolean
  /** 源码映射 */
  sourcemap?: boolean | 'inline' | 'external'
  /** 监听 */
  watch?: boolean
  /** 插件 */
  plugins?: any[]
}

/**
 * Parcel 配置
 */
export interface ParcelConfig {
  /** 入口 */
  entries?: string | string[]
  /** 输出目录 */
  distDir?: string
  /** 缓存目录 */
  cacheDir?: string
  /** 目标 */
  targets?: Record<string, ParcelTarget>
  /** 优化器 */
  optimizers?: Record<string, any>
  /** 转换器 */
  transformers?: Record<string, string[]>
  /** 解析器 */
  resolvers?: string[]
  /** 打包器 */
  bundler?: string
  /** 命名器 */
  namers?: string[]
  /** 运行时 */
  runtimes?: string[]
  /** 压缩器 */
  compressors?: Record<string, any>
}

/**
 * Parcel 目标配置
 */
export interface ParcelTarget {
  distDir?: string
  publicUrl?: string
  engines?: Record<string, string>
  includeNodeModules?: boolean | string[]
  outputFormat?: 'global' | 'esmodule' | 'commonjs'
  isLibrary?: boolean
}

/**
 * Rspack 配置
 */
export interface RspackConfig extends Omit<WebpackConfig, 'plugins'> {
  /** Rspack 特有插件 */
  plugins?: any[]
  /** 实验性功能 */
  experiments?: {
    rspackFuture?: {
      newTreeshaking?: boolean
    }
  }
}

/**
 * Turbopack 配置
 */
export interface TurbopackConfig {
  /** 入口 */
  entry?: string | Record<string, string>
  /** 输出 */
  output?: {
    path?: string
    filename?: string
  }
  /** 模块 */
  module?: {
    rules?: TurbopackRule[]
  }
  /** 解析 */
  resolve?: {
    extensions?: string[]
    alias?: Record<string, string>
  }
  /** 优化 */
  optimization?: {
    minimize?: boolean
  }
}

/**
 * Turbopack 规则
 */
export interface TurbopackRule {
  test?: RegExp
  use?: string | any[]
}