/**
 * Webpack 构建器
 * 
 * 支持 Webpack 构建工具的微前端集成
 */

import { BuilderBase } from '../base/builder-base'
import type { BuilderType, BuilderConfig, BuilderResult } from '../types'

export class WebpackBuilder extends BuilderBase {
  private webpackInstance: any = null

  get builderType(): BuilderType {
    return 'webpack'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Webpack 构建器
   */
  async initialize(): Promise<void> {
    try {
      this.validateConfig()
      
      // 设置构建上下文
      this.setContext({
        startTime: Date.now(),
        status: 'idle'
      })

      // 加载 Webpack
      this.webpackInstance = await this.loadWebpack()
      
    } catch (error) {
      this.handleError(error as Error, 'initialize')
    }
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const webpackConfig = this.generateWebpackConfig()
      const compiler = this.webpackInstance(webpackConfig)

      const stats = await new Promise((resolve, reject) => {
        compiler.run((err: any, stats: any) => {
          if (err) {
            reject(err)
          } else {
            resolve(stats)
          }
        })
      })

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: this.parseOutputs(stats),
        stats: this.generateStats(stats),
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      const result: BuilderResult = {
        success: false,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 0,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [error instanceof Error ? error.message : String(error)]
      }

      if (this.context) {
        this.context.status = 'error'
        this.context.error = error as Error
        this.context.result = result
      }

      return result
    }
  }

  /**
   * 开发模式构建
   */
  async dev(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const webpackConfig = this.generateWebpackConfig(true)
      const WebpackDevServer = await this.loadWebpackDevServer()
      
      const compiler = this.webpackInstance(webpackConfig)
      const server = new WebpackDevServer(webpackConfig.devServer, compiler)

      await server.start()

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 1,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      this.handleError(error as Error, 'dev')
      throw error
    }
  }

  /**
   * 清理构建产物
   */
  async clean(): Promise<void> {
    try {
      const fs = await import('fs/promises')
      const path = await import('path')
      
      const outputPath = path.resolve(this.config.outputDir)
      
      try {
        await fs.rm(outputPath, { recursive: true, force: true })
      } catch (error) {
        // 忽略文件不存在的错误
        if ((error as any).code !== 'ENOENT') {
          throw error
        }
      }
    } catch (error) {
      this.handleError(error as Error, 'clean')
    }
  }

  /**
   * 生成 Webpack 配置
   */
  private generateWebpackConfig(isDev = false): any {
    const baseConfig = {
      mode: this.config.options?.production ? 'production' : 'development',
      entry: this.config.entry,
      output: {
        path: this.config.outputDir,
        filename: '[name].[contenthash].js',
        publicPath: this.config.basePath || '/',
        clean: true
      },
      module: {
        rules: this.generateRules()
      },
      plugins: this.generatePlugins(),
      resolve: {
        extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
        alias: this.generateAlias()
      },
      devtool: this.config.options?.sourcemap !== false ? 'source-map' : false,
      externals: this.config.options?.external || [],
      optimization: {
        minimize: this.config.options?.minify !== false,
        splitChunks: this.config.options?.codeSplitting !== false ? {
          chunks: 'all'
        } : false
      }
    }

    if (isDev) {
      baseConfig.devServer = {
        port: this.config.options?.port || 3000,
        host: this.config.options?.host || 'localhost',
        open: this.config.options?.open !== false,
        hot: this.config.options?.hmr !== false,
        proxy: this.config.options?.proxy || {}
      }
    }

    // 合并自定义配置
    if (this.config.customConfig) {
      return this.mergeConfig(baseConfig, this.config.customConfig)
    }

    return baseConfig
  }

  /**
   * 生成模块规则
   */
  private generateRules(): any[] {
    return [
      {
        test: /\.(ts|tsx)$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.(js|jsx)$/,
        use: 'babel-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|jpg|jpeg|gif|svg)$/,
        type: 'asset/resource'
      }
    ]
  }

  /**
   * 生成插件配置
   */
  private generatePlugins(): any[] {
    const plugins: any[] = []

    // 根据插件配置添加插件
    if (this.config.plugins) {
      for (const pluginConfig of this.config.plugins) {
        if (pluginConfig.enabled !== false) {
          try {
            // 这里应该动态加载插件
            // 简化实现
            plugins.push({
              name: pluginConfig.name,
              options: pluginConfig.options
            })
          } catch (error) {
            console.warn(`加载插件 ${pluginConfig.name} 失败:`, error)
          }
        }
      }
    }

    return plugins
  }

  /**
   * 生成别名配置
   */
  private generateAlias(): Record<string, string> {
    return {
      '@': './src',
      '~': './src'
    }
  }

  /**
   * 解析构建输出
   */
  private parseOutputs(stats: any): any[] {
    // 这里应该解析实际的构建统计
    // 简化实现
    return []
  }

  /**
   * 生成构建统计
   */
  private generateStats(stats: any): any {
    return {
      totalFiles: 0,
      totalSize: 0,
      entryCount: 1,
      dynamicImportCount: 0,
      dependencyCount: 0,
      buildTime: Date.now() - (this.context?.startTime || 0)
    }
  }

  /**
   * 合并配置
   */
  private mergeConfig(base: any, custom: any): any {
    // 深度合并配置对象
    const merged = { ...base }
    
    Object.keys(custom).forEach(key => {
      if (typeof custom[key] === 'object' && !Array.isArray(custom[key])) {
        merged[key] = { ...merged[key], ...custom[key] }
      } else {
        merged[key] = custom[key]
      }
    })

    return merged
  }

  /**
   * 加载 Webpack
   */
  private async loadWebpack(): Promise<any> {
    try {
      return await import('webpack')
    } catch (error) {
      throw new Error('无法加载 Webpack，请确保已安装 webpack 依赖')
    }
  }

  /**
   * 加载 Webpack Dev Server
   */
  private async loadWebpackDevServer(): Promise<any> {
    try {
      const module = await import('webpack-dev-server')
      return module.default || module
    } catch (error) {
      throw new Error('无法加载 Webpack Dev Server，请确保已安装 webpack-dev-server 依赖')
    }
  }
}