/**
 * Parcel 构建器
 * 
 * 支持 Parcel 构建工具的微前端集成
 */

import { BuilderBase } from '../base/builder-base'
import type { BuilderType, BuilderConfig, BuilderResult } from '../types'

export class ParcelBuilder extends BuilderBase {
  private parcelInstance: any = null

  get builderType(): BuilderType {
    return 'parcel'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Parcel 构建器
   */
  async initialize(): Promise<void> {
    try {
      this.validateConfig()
      
      // 设置构建上下文
      this.setContext({
        startTime: Date.now(),
        status: 'idle'
      })

      // 加载 Parcel
      this.parcelInstance = await this.loadParcel()
      
    } catch (error) {
      this.handleError(error as Error, 'initialize')
    }
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const parcelConfig = this.generateParcelConfig()
      const bundler = new this.parcelInstance.Bundler(parcelConfig.entries, parcelConfig)
      
      const bundle = await bundler.bundle()

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: this.parseOutputs(bundle),
        stats: this.generateStats(bundle),
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      const result: BuilderResult = {
        success: false,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 0,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [error instanceof Error ? error.message : String(error)]
      }

      if (this.context) {
        this.context.status = 'error'
        this.context.error = error as Error
        this.context.result = result
      }

      return result
    }
  }

  /**
   * 开发模式构建
   */
  async dev(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const parcelConfig = this.generateParcelConfig(true)
      const bundler = new this.parcelInstance.Bundler(parcelConfig.entries, parcelConfig)
      
      // 启动开发服务器
      const server = await bundler.serve(
        this.config.options?.port || 3000,
        this.config.options?.host || 'localhost',
        this.config.options?.open !== false
      )

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 1,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      this.handleError(error as Error, 'dev')
      throw error
    }
  }

  /**
   * 清理构建产物
   */
  async clean(): Promise<void> {
    try {
      const fs = await import('fs/promises')
      const path = await import('path')
      
      const outputPath = path.resolve(this.config.outputDir)
      
      try {
        await fs.rm(outputPath, { recursive: true, force: true })
      } catch (error) {
        // 忽略文件不存在的错误
        if ((error as any).code !== 'ENOENT') {
          throw error
        }
      }
    } catch (error) {
      this.handleError(error as Error, 'clean')
    }
  }

  /**
   * 生成 Parcel 配置
   */
  private generateParcelConfig(isDev = false): any {
    const entries = Array.isArray(this.config.entry) ? this.config.entry : [this.config.entry]

    const baseConfig = {
      entries,
      outDir: this.config.outputDir,
      outFile: 'index.html',
      publicUrl: this.config.basePath || '/',
      watch: isDev,
      cache: true,
      cacheDir: '.cache',
      contentHash: !isDev,
      minify: this.config.options?.minify !== false && !isDev,
      sourceMaps: this.config.options?.sourcemap !== false,
      target: this.config.options?.target || 'browser',
      https: false,
      logLevel: 3,
      hmr: isDev && this.config.options?.hmr !== false,
      hmrPort: 0
    }

    // 合并自定义配置
    if (this.config.customConfig) {
      return this.mergeConfig(baseConfig, this.config.customConfig)
    }

    return baseConfig
  }

  /**
   * 解析构建输出
   */
  private parseOutputs(bundle: any): any[] {
    if (!bundle || !bundle.childBundles) {
      return []
    }

    const outputs: any[] = []
    
    // 处理主 bundle
    if (bundle.name) {
      outputs.push({
        filename: bundle.name.split('/').pop(),
        filepath: bundle.name,
        size: bundle.bundledSize || 0,
        type: this.getFileType(bundle.name),
        isEntry: bundle.entryAsset !== null,
        isDynamic: false
      })
    }

    // 处理子 bundle
    bundle.childBundles.forEach((childBundle: any) => {
      if (childBundle.name) {
        outputs.push({
          filename: childBundle.name.split('/').pop(),
          filepath: childBundle.name,
          size: childBundle.bundledSize || 0,
          type: this.getFileType(childBundle.name),
          isEntry: false,
          isDynamic: true
        })
      }
    })

    return outputs
  }

  /**
   * 获取文件类型
   */
  private getFileType(filepath: string): 'js' | 'css' | 'html' | 'asset' {
    const ext = filepath.split('.').pop()?.toLowerCase()
    
    switch (ext) {
      case 'js':
      case 'mjs':
        return 'js'
      case 'css':
        return 'css'
      case 'html':
        return 'html'
      default:
        return 'asset'
    }
  }

  /**
   * 生成构建统计
   */
  private generateStats(bundle: any): any {
    const getAllBundles = (bundle: any): any[] => {
      const bundles = [bundle]
      if (bundle.childBundles) {
        bundle.childBundles.forEach((child: any) => {
          bundles.push(...getAllBundles(child))
        })
      }
      return bundles
    }

    const allBundles = getAllBundles(bundle)
    const totalSize = allBundles.reduce((total, b) => total + (b.bundledSize || 0), 0)

    return {
      totalFiles: allBundles.length,
      totalSize,
      entryCount: 1,
      dynamicImportCount: allBundles.filter(b => b.entryAsset === null).length,
      dependencyCount: 0,
      buildTime: Date.now() - (this.context?.startTime || 0)
    }
  }

  /**
   * 合并配置
   */
  private mergeConfig(base: any, custom: any): any {
    // 深度合并配置对象
    const merged = { ...base }
    
    Object.keys(custom).forEach(key => {
      if (typeof custom[key] === 'object' && !Array.isArray(custom[key])) {
        merged[key] = { ...merged[key], ...custom[key] }
      } else {
        merged[key] = custom[key]
      }
    })

    return merged
  }

  /**
   * 加载 Parcel
   */
  private async loadParcel(): Promise<any> {
    try {
      return await import('parcel-bundler')
    } catch (error) {
      throw new Error('无法加载 Parcel，请确保已安装 parcel-bundler 依赖')
    }
  }
}