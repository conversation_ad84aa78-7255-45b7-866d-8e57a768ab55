/**
 * ESBuild 构建器
 * 
 * 支持 ESBuild 构建工具的微前端集成
 */

import { BuilderBase } from '../base/builder-base'
import type { BuilderType, BuilderConfig, BuilderResult } from '../types'

export class EsbuildBuilder extends BuilderBase {
  private esbuildInstance: any = null

  get builderType(): BuilderType {
    return 'esbuild'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 ESBuild 构建器
   */
  async initialize(): Promise<void> {
    try {
      this.validateConfig()
      
      // 设置构建上下文
      this.setContext({
        startTime: Date.now(),
        status: 'idle'
      })

      // 加载 ESBuild
      this.esbuildInstance = await this.loadEsbuild()
      
    } catch (error) {
      this.handleError(error as Error, 'initialize')
    }
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const esbuildConfig = this.generateEsbuildConfig()
      const buildResult = await this.esbuildInstance.build(esbuildConfig)

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: this.parseOutputs(buildResult),
        stats: this.generateStats(buildResult),
        errors: buildResult.errors || [],
        warnings: buildResult.warnings || []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      const result: BuilderResult = {
        success: false,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 0,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [error instanceof Error ? error.message : String(error)]
      }

      if (this.context) {
        this.context.status = 'error'
        this.context.error = error as Error
        this.context.result = result
      }

      return result
    }
  }

  /**
   * 开发模式构建
   */
  async dev(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const esbuildConfig = this.generateEsbuildConfig(true)
      const context = await this.esbuildInstance.context(esbuildConfig)
      
      // 启动开发服务器
      if (this.config.options?.hmr) {
        await context.serve({
          port: this.config.options?.port || 3000,
          host: this.config.options?.host || 'localhost'
        })
      } else {
        // 监听模式
        await context.watch()
      }

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 1,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      this.handleError(error as Error, 'dev')
      throw error
    }
  }

  /**
   * 清理构建产物
   */
  async clean(): Promise<void> {
    try {
      const fs = await import('fs/promises')
      const path = await import('path')
      
      const outputPath = path.resolve(this.config.outputDir)
      
      try {
        await fs.rm(outputPath, { recursive: true, force: true })
      } catch (error) {
        // 忽略文件不存在的错误
        if ((error as any).code !== 'ENOENT') {
          throw error
        }
      }
    } catch (error) {
      this.handleError(error as Error, 'clean')
    }
  }

  /**
   * 生成 ESBuild 配置
   */
  private generateEsbuildConfig(isDev = false): any {
    const baseConfig = {
      entryPoints: Array.isArray(this.config.entry) ? this.config.entry : [this.config.entry],
      outdir: this.config.outputDir,
      bundle: true,
      format: this.config.options?.format || 'esm',
      target: this.config.options?.target || 'es2020',
      platform: 'browser',
      external: this.config.options?.external || [],
      minify: this.config.options?.minify !== false,
      sourcemap: this.config.options?.sourcemap !== false,
      plugins: this.generatePlugins(),
      define: this.generateDefines(),
      loader: this.generateLoaders()
    }

    if (isDev) {
      baseConfig.watch = true
    }

    // 合并自定义配置
    if (this.config.customConfig) {
      return this.mergeConfig(baseConfig, this.config.customConfig)
    }

    return baseConfig
  }

  /**
   * 生成插件配置
   */
  private generatePlugins(): any[] {
    const plugins: any[] = []

    // 根据插件配置添加插件
    if (this.config.plugins) {
      for (const pluginConfig of this.config.plugins) {
        if (pluginConfig.enabled !== false) {
          try {
            // 这里应该动态加载插件
            // 简化实现
            plugins.push({
              name: pluginConfig.name,
              options: pluginConfig.options
            })
          } catch (error) {
            console.warn(`加载插件 ${pluginConfig.name} 失败:`, error)
          }
        }
      }
    }

    return plugins
  }

  /**
   * 生成定义配置
   */
  private generateDefines(): Record<string, string> {
    const defines: Record<string, string> = {}

    // 添加环境变量
    if (this.config.env) {
      Object.entries(this.config.env).forEach(([key, value]) => {
        defines[`process.env.${key}`] = JSON.stringify(value)
      })
    }

    // 添加默认定义
    defines['process.env.NODE_ENV'] = JSON.stringify(
      this.config.options?.production ? 'production' : 'development'
    )

    return defines
  }

  /**
   * 生成加载器配置
   */
  private generateLoaders(): Record<string, string> {
    return {
      '.png': 'file',
      '.jpg': 'file',
      '.jpeg': 'file',
      '.gif': 'file',
      '.svg': 'file',
      '.woff': 'file',
      '.woff2': 'file',
      '.ttf': 'file',
      '.eot': 'file'
    }
  }

  /**
   * 解析构建输出
   */
  private parseOutputs(buildResult: any): any[] {
    if (!buildResult.outputFiles) {
      return []
    }

    return buildResult.outputFiles.map((file: any) => ({
      filename: file.path.split('/').pop(),
      filepath: file.path,
      size: file.contents.length,
      type: this.getFileType(file.path),
      isEntry: false,
      isDynamic: false
    }))
  }

  /**
   * 获取文件类型
   */
  private getFileType(filepath: string): 'js' | 'css' | 'html' | 'asset' {
    const ext = filepath.split('.').pop()?.toLowerCase()
    
    switch (ext) {
      case 'js':
      case 'mjs':
        return 'js'
      case 'css':
        return 'css'
      case 'html':
        return 'html'
      default:
        return 'asset'
    }
  }

  /**
   * 生成构建统计
   */
  private generateStats(buildResult: any): any {
    const outputFiles = buildResult.outputFiles || []
    
    return {
      totalFiles: outputFiles.length,
      totalSize: outputFiles.reduce((total: number, file: any) => total + file.contents.length, 0),
      entryCount: 1,
      dynamicImportCount: 0,
      dependencyCount: 0,
      buildTime: Date.now() - (this.context?.startTime || 0)
    }
  }

  /**
   * 合并配置
   */
  private mergeConfig(base: any, custom: any): any {
    // 深度合并配置对象
    const merged = { ...base }
    
    Object.keys(custom).forEach(key => {
      if (typeof custom[key] === 'object' && !Array.isArray(custom[key])) {
        merged[key] = { ...merged[key], ...custom[key] }
      } else {
        merged[key] = custom[key]
      }
    })

    return merged
  }

  /**
   * 加载 ESBuild
   */
  private async loadEsbuild(): Promise<any> {
    try {
      return await import('esbuild')
    } catch (error) {
      throw new Error('无法加载 ESBuild，请确保已安装 esbuild 依赖')
    }
  }
}