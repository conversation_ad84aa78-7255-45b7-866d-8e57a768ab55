/**
 * Vite 构建器
 * 
 * 支持 Vite 构建工具的微前端集成
 */

import { BuilderBase } from '../base/builder-base'
import type { BuilderType, BuilderConfig, BuilderResult, BuilderContext } from '../types'

export class ViteBuilder extends BuilderBase {
  private viteInstance: any = null

  get builderType(): BuilderType {
    return 'vite'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Vite 构建器
   */
  async initialize(): Promise<void> {
    try {
      this.validateConfig()
      
      // 设置构建上下文
      this.setContext({
        startTime: Date.now(),
        status: 'idle'
      })

      // 加载 Vite
      this.viteInstance = await this.loadVite()
      
    } catch (error) {
      this.handleError(error as Error, 'initialize')
    }
  }

  /**
   * 构建应用
   */
  async build(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const viteConfig = this.generateViteConfig()
      const buildResult = await this.viteInstance.build(viteConfig)

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: this.parseOutputs(buildResult),
        stats: this.generateStats(buildResult),
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      const result: BuilderResult = {
        success: false,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 0,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [error instanceof Error ? error.message : String(error)]
      }

      if (this.context) {
        this.context.status = 'error'
        this.context.error = error as Error
        this.context.result = result
      }

      return result
    }
  }

  /**
   * 开发模式构建
   */
  async dev(): Promise<BuilderResult> {
    try {
      if (this.context) {
        this.context.status = 'building'
        this.context.startTime = Date.now()
      }

      const viteConfig = this.generateViteConfig(true)
      const devServer = await this.viteInstance.createServer(viteConfig)
      
      await devServer.listen()

      const result: BuilderResult = {
        success: true,
        duration: Date.now() - (this.context?.startTime || 0),
        outputs: [],
        stats: {
          totalFiles: 0,
          totalSize: 0,
          entryCount: 1,
          dynamicImportCount: 0,
          dependencyCount: 0,
          buildTime: 0
        },
        errors: [],
        warnings: []
      }

      if (this.context) {
        this.context.status = 'success'
        this.context.result = result
      }

      return result

    } catch (error) {
      this.handleError(error as Error, 'dev')
      throw error
    }
  }

  /**
   * 清理构建产物
   */
  async clean(): Promise<void> {
    try {
      const fs = await import('fs/promises')
      const path = await import('path')
      
      const outputPath = path.resolve(this.config.outputDir)
      
      try {
        await fs.rm(outputPath, { recursive: true, force: true })
      } catch (error) {
        // 忽略文件不存在的错误
        if ((error as any).code !== 'ENOENT') {
          throw error
        }
      }
    } catch (error) {
      this.handleError(error as Error, 'clean')
    }
  }

  /**
   * 生成 Vite 配置
   */
  private generateViteConfig(isDev = false): any {
    const baseConfig = {
      root: process.cwd(),
      base: this.config.basePath || '/',
      mode: this.config.options?.production ? 'production' : 'development',
      build: {
        outDir: this.config.outputDir,
        sourcemap: this.config.options?.sourcemap !== false,
        minify: this.config.options?.minify !== false,
        target: this.config.options?.target || 'es2015',
        rollupOptions: {
          input: this.config.entry,
          external: this.config.options?.external || [],
          output: {
            globals: this.config.options?.globals || {}
          }
        }
      },
      server: isDev ? {
        port: this.config.options?.port || 3000,
        host: this.config.options?.host || 'localhost',
        open: this.config.options?.open !== false,
        hmr: this.config.options?.hmr !== false,
        proxy: this.config.options?.proxy || {}
      } : undefined,
      plugins: this.generatePlugins(),
      resolve: {
        alias: this.generateAlias()
      },
      define: this.generateDefines()
    }

    // 合并自定义配置
    if (this.config.customConfig) {
      return this.mergeConfig(baseConfig, this.config.customConfig)
    }

    return baseConfig
  }

  /**
   * 生成插件配置
   */
  private generatePlugins(): any[] {
    const plugins: any[] = []

    // 根据插件配置添加插件
    if (this.config.plugins) {
      for (const pluginConfig of this.config.plugins) {
        if (pluginConfig.enabled !== false) {
          try {
            // 这里应该动态加载插件
            // 简化实现
            plugins.push({
              name: pluginConfig.name,
              options: pluginConfig.options
            })
          } catch (error) {
            console.warn(`加载插件 ${pluginConfig.name} 失败:`, error)
          }
        }
      }
    }

    return plugins
  }

  /**
   * 生成别名配置
   */
  private generateAlias(): Record<string, string> {
    return {
      '@': './src',
      '~': './src'
    }
  }

  /**
   * 生成定义配置
   */
  private generateDefines(): Record<string, any> {
    const defines: Record<string, any> = {}

    // 添加环境变量
    if (this.config.env) {
      Object.entries(this.config.env).forEach(([key, value]) => {
        defines[`process.env.${key}`] = JSON.stringify(value)
      })
    }

    // 添加默认定义
    defines['process.env.NODE_ENV'] = JSON.stringify(
      this.config.options?.production ? 'production' : 'development'
    )

    return defines
  }

  /**
   * 解析构建输出
   */
  private parseOutputs(buildResult: any): any[] {
    // 这里应该解析实际的构建结果
    // 简化实现
    return []
  }

  /**
   * 生成构建统计
   */
  private generateStats(buildResult: any): any {
    return {
      totalFiles: 0,
      totalSize: 0,
      entryCount: 1,
      dynamicImportCount: 0,
      dependencyCount: 0,
      buildTime: Date.now() - (this.context?.startTime || 0)
    }
  }

  /**
   * 合并配置
   */
  private mergeConfig(base: any, custom: any): any {
    // 深度合并配置对象
    const merged = { ...base }
    
    Object.keys(custom).forEach(key => {
      if (typeof custom[key] === 'object' && !Array.isArray(custom[key])) {
        merged[key] = { ...merged[key], ...custom[key] }
      } else {
        merged[key] = custom[key]
      }
    })

    return merged
  }

  /**
   * 加载 Vite
   */
  private async loadVite(): Promise<any> {
    try {
      return await import('vite')
    } catch (error) {
      throw new Error('无法加载 Vite，请确保已安装 vite 依赖')
    }
  }
}