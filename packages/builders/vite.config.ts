import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    dts({
      insertTypesEntry: true,
      rollupTypes: true
    })
  ],
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MicroCoreBuilders',
      formats: ['es', 'cjs'],
      fileName: (format) => `index.${format === 'es' ? 'js' : 'cjs'}`
    },
    rollupOptions: {
      external: ['@micro-core/core', '@micro-core/shared'],
      output: {
        globals: {
          '@micro-core/core': 'MicroCore',
          '@micro-core/shared': 'MicroCoreShared'
        }
      }
    },
    sourcemap: true,
    minify: false
  }
})