{"name": "@micro-core/compatibility", "version": "0.1.0", "description": "微前端兼容模式包 - 支持qiankun、wujie、micro-app完全兼容", "type": "module", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./qiankun": {"types": "./dist/qiankun/index.d.ts", "import": "./dist/qiankun/index.mjs", "require": "./dist/qiankun/index.js"}, "./wujie": {"types": "./dist/wujie/index.d.ts", "import": "./dist/wujie/index.mjs", "require": "./dist/wujie/index.js"}, "./micro-app": {"types": "./dist/micro-app/index.d.ts", "import": "./dist/micro-app/index.mjs", "require": "./dist/micro-app/index.js"}}, "files": ["dist", "README.md"], "scripts": {"dev": "vite build --watch", "build": "vite build && tsc --emitDeclarationOnly", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["micro-frontend", "compatibility", "qiankun", "wujie", "micro-app", "migration", "bridge"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/compatibility"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "@micro-core/sandbox": "workspace:*", "@micro-core/communication": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "typescript": "^5.3.3", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4", "rimraf": "^5.0.5"}, "peerDependencies": {"qiankun": ">=2.0.0", "wujie": ">=1.0.0", "microapp": ">=1.0.0"}, "peerDependenciesMeta": {"qiankun": {"optional": true}, "wujie": {"optional": true}, "microapp": {"optional": true}}, "engines": {"node": ">=16.0.0"}}