/**
 * wujie API 兼容层
 * 提供与 wujie 完全兼容的 API 接口
 */

import type { 
  WujieApp, 
  WujieStartOptions, 
  WujiePreloadApp,
  WujieEventBus
} from '../types/wujie'
import { WujieAdapter } from './adapter'

// 全局 wujie 适配器实例
let wujieAdapter: WujieAdapter | null = null

/**
 * 获取或创建 wujie 适配器实例
 */
function getWujieAdapter(): WujieAdapter {
  if (!wujieAdapter) {
    wujieAdapter = new WujieAdapter()
  }
  return wujieAdapter
}

/**
 * 启动 wujie
 * 与 wujie.setupApp 完全兼容
 */
export async function setupApp(options: WujieStartOptions): Promise<void> {
  const adapter = getWujieAdapter()
  await adapter.setupApp(options)
}

/**
 * 启动子应用
 * 与 wujie.startApp 完全兼容
 */
export async function startApp(name: string, options: WujieApp): Promise<void> {
  const adapter = getWujieAdapter()
  await adapter.startApp(name, options)
}

/**
 * 销毁子应用
 * 与 wujie.destroyApp 完全兼容
 */
export async function destroyApp(name: string): Promise<void> {
  const adapter = getWujieAdapter()
  await adapter.destroyApp(name)
}

/**
 * 预加载子应用
 * 与 wujie.preloadApp 完全兼容
 */
export async function preloadApp(options: WujiePreloadApp): Promise<void> {
  const adapter = getWujieAdapter()
  await adapter.preloadApp(options)
}

/**
 * 获取子应用实例
 * 与 wujie.getWujieById 完全兼容
 */
export function getWujieById(id: string): any {
  const adapter = getWujieAdapter()
  return adapter.getWujieById(id)
}

/**
 * 创建事件总线
 * 与 wujie.$wujie.bus 完全兼容
 */
export function createEventBus(): WujieEventBus {
  const adapter = getWujieAdapter()
  return adapter.createEventBus()
}

/**
 * 获取所有子应用
 */
export function getAllApps(): string[] {
  const adapter = getWujieAdapter()
  return adapter.getAllApps()
}

/**
 * 检查应用是否存在
 */
export function hasApp(name: string): boolean {
  const adapter = getWujieAdapter()
  return adapter.hasApp(name)
}

/**
 * 检查应用是否预加载
 */
export function isPreloaded(name: string): boolean {
  const adapter = getWujieAdapter()
  return adapter.isPreloaded(name)
}

/**
 * 清理所有应用
 */
export async function clearApps(): Promise<void> {
  const adapter = getWujieAdapter()
  await adapter.clearApps()
}

// 导出所有 wujie 兼容 API
export const wujie = {
  setupApp,
  startApp,
  destroyApp,
  preloadApp,
  getWujieById,
  createEventBus,
  getAllApps,
  hasApp,
  isPreloaded,
  clearApps
}

// 默认导出，支持 import wujie from '@micro-core/compatibility/wujie'
export default wujie