/**
 * wujie 适配器实现
 * 提供与 wujie 完全兼容的 API 适配
 */

import type { 
  WujieApp, 
  WujieStartOptions, 
  WujieLifeCycles,
  WujiePreloadApp,
  WujieEventBus,
  WujieProps
} from '../types/wujie'
import { MicroCore } from '@micro-core/core'
import { MicroCoreError } from '@micro-core/shared'

/**
 * wujie 适配器类
 */
export class WujieAdapter {
  private microCore: MicroCore
  private eventBus: Map<string, Function[]> = new Map()
  private preloadedApps: Set<string> = new Set()
  private startOptions: WujieStartOptions = {}

  constructor() {
    this.microCore = new MicroCore()
  }

  /**
   * 启动 wujie
   * 兼容 wujie.setupApp 和 wujie.startApp
   */
  async setupApp(options: WujieStartOptions): Promise<void> {
    try {
      this.startOptions = { ...options }
      
      // 转换 wujie 配置为 micro-core 配置
      const microCoreConfig = this.convertStartOptions(options)
      
      // 启动 micro-core
      await this.microCore.start(microCoreConfig)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to setup wujie app: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WUJIE_SETUP_ERROR',
        { options, error }
      )
    }
  }

  /**
   * 启动子应用
   * 兼容 wujie.startApp
   */
  async startApp(name: string, options: WujieApp): Promise<void> {
    try {
      // 转换 wujie 应用配置
      const microApp = this.convertAppConfig(name, options)
      
      // 注册并启动应用
      await this.microCore.registerApp(microApp)
      await this.microCore.loadApp(name)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to start wujie app: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WUJIE_START_APP_ERROR',
        { name, options, error }
      )
    }
  }

  /**
   * 销毁子应用
   * 兼容 wujie.destroyApp
   */
  async destroyApp(name: string): Promise<void> {
    try {
      await this.microCore.unloadApp(name)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to destroy wujie app: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WUJIE_DESTROY_APP_ERROR',
        { name, error }
      )
    }
  }

  /**
   * 预加载子应用
   * 兼容 wujie.preloadApp
   */
  async preloadApp(options: WujiePreloadApp): Promise<void> {
    try {
      const { name, url, ...restOptions } = options
      
      // 转换为 micro-core 预加载配置
      const preloadConfig = {
        name,
        entry: url,
        ...this.convertPreloadOptions(restOptions)
      }
      
      await this.microCore.preloadApp(preloadConfig)
      this.preloadedApps.add(name)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to preload wujie app: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'WUJIE_PRELOAD_ERROR',
        { options, error }
      )
    }
  }

  /**
   * 事件通信
   * 兼容 wujie.$wujie.bus
   */
  createEventBus(): WujieEventBus {
    return {
      $on: (event: string, callback: Function) => {
        if (!this.eventBus.has(event)) {
          this.eventBus.set(event, [])
        }
        this.eventBus.get(event)!.push(callback)
      },
      
      $off: (event: string, callback?: Function) => {
        if (!this.eventBus.has(event)) return
        
        if (callback) {
          const callbacks = this.eventBus.get(event)!
          const index = callbacks.indexOf(callback)
          if (index > -1) {
            callbacks.splice(index, 1)
          }
        } else {
          this.eventBus.delete(event)
        }
      },
      
      $emit: (event: string, ...args: any[]) => {
        if (!this.eventBus.has(event)) return
        
        const callbacks = this.eventBus.get(event)!
        callbacks.forEach(callback => {
          try {
            callback(...args)
          } catch (error) {
            console.error(`Error in wujie event callback for ${event}:`, error)
          }
        })
      },
      
      $clear: () => {
        this.eventBus.clear()
      }
    }
  }

  /**
   * 获取子应用实例
   * 兼容 wujie.getWujieById
   */
  getWujieById(id: string): any {
    const app = this.microCore.getApp(id)
    if (!app) {
      return null
    }

    return {
      id,
      name: app.name,
      url: app.entry,
      alive: app.status === 'mounted',
      bus: this.createEventBus(),
      destroy: () => this.destroyApp(id),
      show: () => this.microCore.showApp(id),
      hide: () => this.microCore.hideApp(id)
    }
  }

  /**
   * 转换启动选项
   */
  private convertStartOptions(options: WujieStartOptions): any {
    return {
      sandbox: {
        enabled: true,
        strategy: options.iframe ? 'iframe' : 'proxy'
      },
      prefetch: options.prefetch !== false,
      singular: options.degrade,
      ...options
    }
  }

  /**
   * 转换应用配置
   */
  private convertAppConfig(name: string, options: WujieApp): any {
    return {
      name,
      entry: options.url,
      container: options.el || `#${name}`,
      activeRule: options.path || `/${name}`,
      props: options.props || {},
      sandbox: {
        enabled: true,
        strategy: options.iframe ? 'iframe' : 'proxy'
      },
      alive: options.alive,
      fetch: options.fetch,
      replace: options.replace,
      sync: options.sync,
      prefix: options.prefix,
      fiber: options.fiber,
      degrade: options.degrade
    }
  }

  /**
   * 转换预加载选项
   */
  private convertPreloadOptions(options: Omit<WujiePreloadApp, 'name' | 'url'>): any {
    return {
      fetch: options.fetch,
      cache: true,
      ...options
    }
  }

  /**
   * 获取所有子应用
   */
  getAllApps(): string[] {
    return this.microCore.getAllApps().map(app => app.name)
  }

  /**
   * 检查应用是否存在
   */
  hasApp(name: string): boolean {
    return this.microCore.hasApp(name)
  }

  /**
   * 检查应用是否预加载
   */
  isPreloaded(name: string): boolean {
    return this.preloadedApps.has(name)
  }

  /**
   * 清理所有应用
   */
  async clearApps(): Promise<void> {
    const apps = this.getAllApps()
    await Promise.all(apps.map(name => this.destroyApp(name)))
    this.preloadedApps.clear()
    this.eventBus.clear()
  }
}