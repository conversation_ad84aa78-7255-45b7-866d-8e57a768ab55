/**
 * qiankun API 兼容层
 * 提供与 qiankun 完全兼容的 API 接口
 */

import type { 
  QiankunApp, 
  QiankunLifeCycles, 
  QiankunStartOptions,
  QiankunLoadableApp,
  QiankunParcelConfig
} from '../types/qiankun'
import { QiankunAdapter } from './adapter'

// 全局 qiankun 适配器实例
let qiankunAdapter: QiankunAdapter | null = null

/**
 * 获取或创建 qiankun 适配器实例
 */
function getQiankunAdapter(): QiankunAdapter {
  if (!qiankunAdapter) {
    qiankunAdapter = new QiankunAdapter()
  }
  return qiankunAdapter
}

/**
 * 注册微应用
 * 与 qiankun.registerMicroApps 完全兼容
 */
export function registerMicroApps<T extends Record<string, unknown>>(
  apps: Array<QiankunApp<T>>,
  lifeCycles?: QiankunLifeCycles<T>
): void {
  const adapter = getQiankunAdapter()
  adapter.registerMicroApps(apps, lifeCycles)
}

/**
 * 启动 qiankun
 * 与 qiankun.start 完全兼容
 */
export function start(options?: QiankunStartOptions): void {
  const adapter = getQiankunAdapter()
  adapter.start(options)
}

/**
 * 手动加载微应用
 * 与 qiankun.loadMicroApp 完全兼容
 */
export function loadMicroApp<T extends Record<string, unknown>>(
  app: QiankunLoadableApp<T>,
  configuration?: QiankunParcelConfig,
  lifeCycles?: QiankunLifeCycles<T>
): Promise<QiankunParcelConfig> {
  const adapter = getQiankunAdapter()
  return adapter.loadMicroApp(app, configuration, lifeCycles)
}

/**
 * 卸载微应用
 * 与 qiankun.unloadMicroApp 完全兼容
 */
export function unloadMicroApp(app: QiankunParcelConfig): Promise<void> {
  const adapter = getQiankunAdapter()
  return adapter.unloadMicroApp(app)
}

/**
 * 初始化全局状态
 * 与 qiankun.initGlobalState 完全兼容
 */
export function initGlobalState(state: Record<string, any>) {
  const adapter = getQiankunAdapter()
  return adapter.initGlobalState(state)
}

/**
 * 预加载微应用
 * 与 qiankun.prefetchApps 完全兼容
 */
export function prefetchApps(
  apps: Array<QiankunApp>,
  importEntryOpts?: {
    fetch?: typeof window.fetch
    getPublicPath?: (entry: string) => string
    getTemplate?: (tpl: string) => string
  }
): void {
  const adapter = getQiankunAdapter()
  adapter.prefetchApps(apps, importEntryOpts)
}

/**
 * 添加全局未捕获错误处理器
 * 与 qiankun.addGlobalUncaughtErrorHandler 完全兼容
 */
export function addGlobalUncaughtErrorHandler(errorHandler: (event: Event | string) => void): void {
  const adapter = getQiankunAdapter()
  adapter.addGlobalUncaughtErrorHandler(errorHandler)
}

/**
 * 移除全局未捕获错误处理器
 * 与 qiankun.removeGlobalUncaughtErrorHandler 完全兼容
 */
export function removeGlobalUncaughtErrorHandler(errorHandler: (event: Event | string) => void): void {
  const adapter = getQiankunAdapter()
  adapter.removeGlobalUncaughtErrorHandler(errorHandler)
}

/**
 * 设置默认挂载应用
 * 与 qiankun.setDefaultMountApp 完全兼容
 */
export function setDefaultMountApp(defaultAppLink: string): void {
  const adapter = getQiankunAdapter()
  adapter.setDefaultMountApp(defaultAppLink)
}

/**
 * 运行默认挂载应用
 * 与 qiankun.runDefaultMountApp 完全兼容
 */
export function runDefaultMountApp(defaultAppLink?: string): void {
  const adapter = getQiankunAdapter()
  adapter.runDefaultMountApp(defaultAppLink)
}

// 导出所有 qiankun 兼容 API
export const qiankun = {
  registerMicroApps,
  start,
  loadMicroApp,
  unloadMicroApp,
  initGlobalState,
  prefetchApps,
  addGlobalUncaughtErrorHandler,
  removeGlobalUncaughtErrorHandler,
  setDefaultMountApp,
  runDefaultMountApp
}

// 默认导出，支持 import qiankun from '@micro-core/compatibility/qiankun'
export default qiankun