/**
 * qiankun 迁移工具
 * 提供从 qiankun 到 micro-core 的迁移支持
 */

import type { 
  QiankunApp, 
  QiankunLifeCycles, 
  QiankunStartOptions,
  QiankunMigrationConfig,
  QiankunMigrationResult
} from '../types/qiankun'
import type { MicroApp } from '@micro-core/shared'
import { MicroCoreError } from '@micro-core/shared'

/**
 * qiankun 迁移助手类
 */
export class QiankunMigrationHelper {
  private migrationConfig: QiankunMigrationConfig
  private migrationResults: QiankunMigrationResult[] = []

  constructor(config: QiankunMigrationConfig = {}) {
    this.migrationConfig = {
      preserveOriginalConfig: true,
      generateBackup: true,
      validateAfterMigration: true,
      ...config
    }
  }

  /**
   * 将 qiankun 应用配置转换为 micro-core 配置
   */
  convertAppConfig<T extends Record<string, unknown>>(
    qiankunApp: QiankunApp<T>
  ): MicroApp {
    try {
      const microApp: MicroApp = {
        name: qiankunApp.name,
        entry: qiankunApp.entry,
        container: qiankunApp.container,
        activeRule: this.convertActiveRule(qiankunApp.activeRule),
        props: qiankunApp.props || {}
      }

      // 记录迁移结果
      this.migrationResults.push({
        type: 'app-config',
        source: qiankunApp,
        target: microApp,
        status: 'success',
        timestamp: Date.now()
      })

      return microApp
    } catch (error) {
      const migrationError = new MicroCoreError(
        `Failed to convert qiankun app config: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'QIANKUN_MIGRATION_ERROR',
        { qiankunApp, error }
      )

      this.migrationResults.push({
        type: 'app-config',
        source: qiankunApp,
        target: null,
        status: 'error',
        error: migrationError,
        timestamp: Date.now()
      })

      throw migrationError
    }
  }

  /**
   * 转换激活规则
   */
  private convertActiveRule(
    activeRule: string | string[] | ((location: Location) => boolean)
  ): string | ((location: Location) => boolean) {
    if (typeof activeRule === 'function') {
      return activeRule
    }

    if (Array.isArray(activeRule)) {
      // 将数组形式的规则转换为函数
      return (location: Location) => {
        return activeRule.some(rule => {
          if (typeof rule === 'string') {
            return location.pathname.startsWith(rule)
          }
          return false
        })
      }
    }

    return activeRule
  }

  /**
   * 转换生命周期钩子
   */
  convertLifeCycles<T extends Record<string, unknown>>(
    qiankunLifeCycles?: QiankunLifeCycles<T>
  ): any {
    if (!qiankunLifeCycles) {
      return undefined
    }

    return {
      beforeLoad: qiankunLifeCycles.beforeLoad,
      beforeMount: qiankunLifeCycles.beforeMount,
      afterMount: qiankunLifeCycles.afterMount,
      beforeUnmount: qiankunLifeCycles.beforeUnmount,
      afterUnmount: qiankunLifeCycles.afterUnmount
    }
  }

  /**
   * 转换启动选项
   */
  convertStartOptions(qiankunOptions?: QiankunStartOptions): any {
    if (!qiankunOptions) {
      return undefined
    }

    return {
      prefetch: qiankunOptions.prefetch,
      sandbox: this.convertSandboxOptions(qiankunOptions.sandbox),
      singular: qiankunOptions.singular,
      fetch: qiankunOptions.fetch,
      getPublicPath: qiankunOptions.getPublicPath,
      getTemplate: qiankunOptions.getTemplate,
      excludeAssetFilter: qiankunOptions.excludeAssetFilter
    }
  }

  /**
   * 转换沙箱选项
   */
  private convertSandboxOptions(
    sandbox?: boolean | { strictStyleIsolation?: boolean; experimentalStyleIsolation?: boolean }
  ): any {
    if (typeof sandbox === 'boolean') {
      return {
        enabled: sandbox,
        strategy: sandbox ? 'proxy' : 'none'
      }
    }

    if (sandbox && typeof sandbox === 'object') {
      return {
        enabled: true,
        strategy: 'proxy',
        css: {
          strictIsolation: sandbox.strictStyleIsolation,
          experimentalIsolation: sandbox.experimentalStyleIsolation
        }
      }
    }

    return {
      enabled: true,
      strategy: 'proxy'
    }
  }

  /**
   * 批量迁移应用配置
   */
  migrateApps<T extends Record<string, unknown>>(
    qiankunApps: Array<QiankunApp<T>>
  ): MicroApp[] {
    const migratedApps: MicroApp[] = []
    const errors: Error[] = []

    for (const qiankunApp of qiankunApps) {
      try {
        const microApp = this.convertAppConfig(qiankunApp)
        migratedApps.push(microApp)
      } catch (error) {
        errors.push(error as Error)
        if (!this.migrationConfig.continueOnError) {
          break
        }
      }
    }

    if (errors.length > 0 && !this.migrationConfig.continueOnError) {
      throw new MicroCoreError(
        `Migration failed with ${errors.length} errors`,
        'QIANKUN_BATCH_MIGRATION_ERROR',
        { errors, migratedApps }
      )
    }

    return migratedApps
  }

  /**
   * 验证迁移结果
   */
  validateMigration(): boolean {
    const errors = this.migrationResults.filter(result => result.status === 'error')
    
    if (errors.length > 0) {
      console.warn(`Migration completed with ${errors.length} errors:`, errors)
      return false
    }

    console.log(`Migration completed successfully. ${this.migrationResults.length} items migrated.`)
    return true
  }

  /**
   * 获取迁移报告
   */
  getMigrationReport(): {
    total: number
    successful: number
    failed: number
    results: QiankunMigrationResult[]
  } {
    const successful = this.migrationResults.filter(result => result.status === 'success').length
    const failed = this.migrationResults.filter(result => result.status === 'error').length

    return {
      total: this.migrationResults.length,
      successful,
      failed,
      results: [...this.migrationResults]
    }
  }

  /**
   * 清理迁移结果
   */
  clearMigrationResults(): void {
    this.migrationResults = []
  }

  /**
   * 生成迁移配置文件
   */
  generateMigrationConfig(
    qiankunApps: Array<QiankunApp>,
    qiankunLifeCycles?: QiankunLifeCycles,
    qiankunStartOptions?: QiankunStartOptions
  ): string {
    const config = {
      apps: qiankunApps.map(app => this.convertAppConfig(app)),
      lifeCycles: this.convertLifeCycles(qiankunLifeCycles),
      startOptions: this.convertStartOptions(qiankunStartOptions),
      migrationInfo: {
        migratedAt: new Date().toISOString(),
        migratedBy: 'qiankun-migration-helper',
        version: '0.1.0'
      }
    }

    return JSON.stringify(config, null, 2)
  }
}

/**
 * 创建 qiankun 迁移助手实例
 */
export function createQiankunMigrationHelper(config?: QiankunMigrationConfig): QiankunMigrationHelper {
  return new QiankunMigrationHelper(config)
}

/**
 * 快速迁移 qiankun 应用
 */
export function migrateFromQiankun<T extends Record<string, unknown>>(
  qiankunApps: Array<QiankunApp<T>>,
  options?: {
    lifeCycles?: QiankunLifeCycles<T>
    startOptions?: QiankunStartOptions
    migrationConfig?: QiankunMigrationConfig
  }
): {
  apps: MicroApp[]
  config: string
  report: ReturnType<QiankunMigrationHelper['getMigrationReport']>
} {
  const helper = createQiankunMigrationHelper(options?.migrationConfig)
  
  const apps = helper.migrateApps(qiankunApps)
  const config = helper.generateMigrationConfig(
    qiankunApps,
    options?.lifeCycles,
    options?.startOptions
  )
  const report = helper.getMigrationReport()

  return { apps, config, report }
}