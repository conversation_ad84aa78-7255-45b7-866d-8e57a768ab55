/**
 * qiankun 适配器实现
 */

import type { MicroCore } from '@micro-core/core'
import type { MicroApp, AppConfig } from '@micro-core/shared'
import type {
  QiankunAppConfig,
  QiankunApi,
  QiankunLifecycle,
  QiankunSubAppLifecycle,
  QiankunGlobalConfig
} from '../types/qiankun'

export * from '../types/qiankun'

/**
 * qiankun 适配器类
 */
export class QiankunAdapter implements QiankunApi {
  private microCore: MicroCore
  private apps: Map<string, QiankunAppConfig> = new Map()
  private globalConfig: QiankunGlobalConfig = {}
  private isStarted = false

  constructor(microCore: MicroCore) {
    this.microCore = microCore
  }

  /**
   * 注册微应用
   */
  registerMicroApps = (
    apps: QiankunAppConfig[],
    lifeCycles?: QiankunLifecycle
  ): void => {
    apps.forEach(app => {
      // 转换 qiankun 配置为 micro-core 配置
      const microCoreConfig: AppConfig = {
        name: app.name,
        entry: app.entry,
        container: app.container,
        activeRule: app.activeRule,
        props: app.props,
        loader: app.loader
      }

      // 注册到 micro-core
      this.microCore.registerApp(microCoreConfig)

      // 保存原始配置
      this.apps.set(app.name, app)
    })

    // 设置全局生命周期
    if (lifeCycles) {
      this.setGlobalLifecycles(lifeCycles)
    }
  }

  /**
   * 启动 qiankun
   */
  start = (options?: QiankunGlobalConfig): void => {
    if (this.isStarted) {
      console.warn('[qiankun] qiankun has already been started')
      return
    }

    // 合并配置
    this.globalConfig = { ...this.globalConfig, ...options }

    // 启动 micro-core
    this.microCore.start({
      prefetch: this.globalConfig.prefetch,
      sandbox: this.globalConfig.sandbox !== false,
      singular: this.globalConfig.singular,
      fetch: this.globalConfig.fetch,
      getPublicPath: this.globalConfig.getPublicPath,
      getTemplate: this.globalConfig.getTemplate,
      excludeAssetFilter: this.globalConfig.excludeAssetFilter
    })

    this.isStarted = true
  }

  /**
   * 设置默认挂载应用
   */
  setDefaultMountApp = (appLink: string): void => {
    this.microCore.setDefaultApp(appLink)
  }

  /**
   * 运行时加载微应用
   */
  loadMicroApp = (
    app: QiankunAppConfig,
    configuration?: QiankunGlobalConfig
  ): any => {
    const microCoreConfig: AppConfig = {
      name: app.name,
      entry: app.entry,
      container: app.container,
      activeRule: app.activeRule,
      props: app.props,
      loader: app.loader
    }

    return this.microCore.loadApp(microCoreConfig, configuration)
  }

  /**
   * 预加载微应用
   */
  prefetchApps = (apps: QiankunAppConfig[]): void => {
    const configs = apps.map(app => ({
      name: app.name,
      entry: app.entry
    }))

    this.microCore.prefetchApps(configs)
  }

  /**
   * 添加全局未捕获错误处理器
   */
  addGlobalUncaughtErrorHandler = (errorHandler: (event: Event | string) => void): void => {
    this.microCore.addErrorHandler(errorHandler)
  }

  /**
   * 移除全局未捕获错误处理器
   */
  removeGlobalUncaughtErrorHandler = (errorHandler: (event: Event | string) => void): void => {
    this.microCore.removeErrorHandler(errorHandler)
  }

  /**
   * 初始化全局状态
   */
  initGlobalState = (state: Record<string, any>) => {
    return this.microCore.initGlobalState(state)
  }

  /**
   * 获取当前激活的应用
   */
  getActiveApps = (): string[] => {
    return this.microCore.getActiveApps()
  }

  /**
   * 获取所有应用状态
   */
  getApps = (): any[] => {
    return Array.from(this.apps.values())
  }

  /**
   * 设置全局生命周期
   */
  private setGlobalLifecycles(lifeCycles: QiankunLifecycle): void {
    if (lifeCycles.beforeLoad) {
      this.microCore.addLifecycleHook('beforeLoad', lifeCycles.beforeLoad)
    }

    if (lifeCycles.beforeMount) {
      this.microCore.addLifecycleHook('beforeMount', lifeCycles.beforeMount)
    }

    if (lifeCycles.afterMount) {
      this.microCore.addLifecycleHook('afterMount', lifeCycles.afterMount)
    }

    if (lifeCycles.beforeUnmount) {
      this.microCore.addLifecycleHook('beforeUnmount', lifeCycles.beforeUnmount)
    }

    if (lifeCycles.afterUnmount) {
      this.microCore.addLifecycleHook('afterUnmount', lifeCycles.afterUnmount)
    }
  }
}

/**
 * 创建 qiankun 适配器实例
 */
export function createQiankunAdapter(microCore: MicroCore): QiankunAdapter {
  return new QiankunAdapter(microCore)
}