/**
 * @micro-core/compatibility
 * 兼容模式主入口 - 提供与现有微前端框架的完全兼容
 */

// qiankun 兼容
export * from './qiankun'
export { QiankunAdapter } from './qiankun/adapter'
export { qiankunApi } from './qiankun/api'
export { qiankunMigration } from './qiankun/migration'

// wujie 兼容
export * from './wujie'
export { WujieAdapter } from './wujie/adapter'
export { wujieApi } from './wujie/api'
export { wujieMigration } from './wujie/migration'

// micro-app 兼容
export * from './micro-app'
export { MicroAppAdapter } from './micro-app/adapter'
export { microAppApi } from './micro-app/api'
export { microAppMigration } from './micro-app/migration'

// 兼容桥接
export * from './bridge'
export { ApiBridge } from './bridge/api-bridge'
export { LifecycleBridge } from './bridge/lifecycle-bridge'
export { EventBridge } from './bridge/event-bridge'

// 类型定义
export * from './types'

// 版本信息
export const version = '0.1.0'

// 默认导出
export default {
  version,
  qiankun: {
    QiankunAdapter,
    api: qiankunApi,
    migration: qiankunMigration
  },
  wujie: {
    WujieAdapter,
    api: wujieApi,
    migration: wujieMigration
  },
  microApp: {
    MicroAppAdapter,
    api: microAppApi,
    migration: microAppMigration
  },
  bridge: {
    ApiBridge,
    LifecycleBridge,
    EventBridge
  }
}