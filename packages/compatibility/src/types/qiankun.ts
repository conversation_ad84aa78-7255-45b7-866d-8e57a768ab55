/**
 * qiankun 兼容类型定义
 */

// qiankun 应用配置
export interface QiankunAppConfig {
  name: string
  entry: string | { scripts?: string[]; styles?: string[]; html?: string }
  container: string | HTMLElement
  activeRule: string | ((location: Location) => boolean) | Array<string | ((location: Location) => boolean)>
  props?: Record<string, any>
  loader?: (loading: boolean) => void
}

// qiankun 生命周期
export interface QiankunLifecycle {
  beforeLoad?: (app: QiankunAppConfig) => Promise<any> | any
  beforeMount?: (app: QiankunAppConfig) => Promise<any> | any
  afterMount?: (app: QiankunAppConfig) => Promise<any> | any
  beforeUnmount?: (app: QiankunAppConfig) => Promise<any> | any
  afterUnmount?: (app: QiankunAppConfig) => Promise<any> | any
}

// qiankun 启动配置
export interface QiankunStartOptions {
  prefetch?: boolean | 'all' | string[] | ((apps: QiankunAppConfig[]) => { criticalAppNames: string[]; minorAppsName: string[] })
  sandbox?: boolean | { strictStyleIsolation?: boolean; experimentalStyleIsolation?: boolean }
  singular?: boolean | ((app: QiankunAppConfig) => boolean)
  fetch?: typeof window.fetch
  getPublicPath?: (entry: string | { scripts?: string[]; styles?: string[]; html?: string }) => string
  getTemplate?: (tpl: string) => string
  excludeAssetFilter?: (assetUrl: string) => boolean
}

// qiankun 手动加载应用配置
export interface QiankunLoadableApp extends QiankunAppConfig {
  configuration?: Record<string, any>
}

// qiankun API 类型
export interface QiankunApi {
  registerMicroApps: (apps: QiankunAppConfig[], lifeCycles?: QiankunLifecycle) => void
  start: (options?: QiankunStartOptions) => void
  setDefaultMountApp: (appLink: string) => void
  runAfterFirstMounted: (effect: () => void) => void
  loadMicroApp: (app: QiankunLoadableApp, configuration?: Record<string, any>) => {
    mount(): Promise<null>
    unmount(): Promise<null>
    update(customProps: Record<string, any>): Promise<any>
    getStatus(): 'NOT_LOADED' | 'LOADING_SOURCE_CODE' | 'NOT_BOOTSTRAPPED' | 'BOOTSTRAPPING' | 'NOT_MOUNTED' | 'MOUNTING' | 'MOUNTED' | 'UPDATING' | 'UNMOUNTING' | 'UNLOADING' | 'SKIP_BECAUSE_BROKEN' | 'LOAD_ERROR'
    loadPromise: Promise<null>
    bootstrapPromise: Promise<null>
    mountPromise: Promise<null>
    unmountPromise: Promise<null>
  }
  unloadMicroApp: (app: any) => Promise<void>
  initGlobalState: (state: Record<string, any>) => {
    onGlobalStateChange: (callback: (state: Record<string, any>, prevState: Record<string, any>) => void, fireImmediately?: boolean) => void
    setGlobalState: (state: Record<string, any>) => boolean
    offGlobalStateChange: () => boolean
  }
  addGlobalUncaughtErrorHandler: (errorHandler: (event: Event | string) => void) => void
  removeGlobalUncaughtErrorHandler: (errorHandler: (event: Event | string) => void) => void
}

// qiankun 子应用生命周期
export interface QiankunSubAppLifecycle {
  bootstrap?: (props?: any) => Promise<any>
  mount?: (props?: any) => Promise<any>
  unmount?: (props?: any) => Promise<any>
  update?: (props?: any) => Promise<any>
}