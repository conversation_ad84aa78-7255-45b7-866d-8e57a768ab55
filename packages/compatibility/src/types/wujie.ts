/**
 * wujie 兼容类型定义
 */

// wujie 应用配置
export interface WujieAppConfig {
  name: string
  url: string
  el?: string | HTMLElement
  props?: Record<string, any>
  attrs?: Record<string, any>
  replace?: boolean
  sync?: boolean
  prefix?: { [key: string]: string }
  fiber?: boolean
  degrade?: boolean
  plugins?: Array<{
    htmlLoader?: (code: string, url: string) => string
    jsLoader?: (code: string, url: string) => string
    cssLoader?: (code: string, url: string) => string
    replace?: (code: string, url: string) => string
  }>
  beforeLoad?: (appWindow: Window) => void
  beforeMount?: (appWindow: Window) => void
  afterMount?: (appWindow: Window) => void
  beforeUnmount?: (appWindow: Window) => void
  afterUnmount?: (appWindow: Window) => void
  activated?: (appWindow: Window) => void
  deactivated?: (appWindow: Window) => void
  loadError?: (url: string, e: Error) => void
}

// wujie 预加载配置
export interface WujiePreloadConfig {
  name: string
  url: string
  attrs?: Record<string, any>
  replace?: boolean
  prefix?: { [key: string]: string }
  fiber?: boolean
  degrade?: boolean
  plugins?: Array<{
    htmlLoader?: (code: string, url: string) => string
    jsLoader?: (code: string, url: string) => string
    cssLoader?: (code: string, url: string) => string
    replace?: (code: string, url: string) => string
  }>
}

// wujie 生命周期
export interface WujieLifecycle {
  beforeLoad?: (appWindow: Window) => void
  beforeMount?: (appWindow: Window) => void
  afterMount?: (appWindow: Window) => void
  beforeUnmount?: (appWindow: Window) => void
  afterUnmount?: (appWindow: Window) => void
  activated?: (appWindow: Window) => void
  deactivated?: (appWindow: Window) => void
  loadError?: (url: string, e: Error) => void
}

// wujie 总线事件
export interface WujieBusEvent {
  $on: (event: string, callback: Function) => void
  $off: (event: string, callback?: Function) => void
  $once: (event: string, callback: Function) => void
  $emit: (event: string, ...args: any[]) => void
}

// wujie API 类型
export interface WujieApi {
  startApp: (config: WujieAppConfig) => Promise<void>
  destroyApp: (name: string) => void
  preloadApp: (config: WujiePreloadConfig) => void
  bus: WujieBusEvent
  setupApp: (config: {
    name: string
    url: string
    el?: string | HTMLElement
    props?: Record<string, any>
    attrs?: Record<string, any>
    replace?: boolean
    sync?: boolean
    prefix?: { [key: string]: string }
    fiber?: boolean
    degrade?: boolean
    plugins?: Array<{
      htmlLoader?: (code: string, url: string) => string
      jsLoader?: (code: string, url: string) => string
      cssLoader?: (code: string, url: string) => string
      replace?: (code: string, url: string) => string
    }>
  }) => Promise<void>
}

// wujie 子应用生命周期
export interface WujieSubAppLifecycle {
  mount?: (props?: any) => Promise<any>
  unmount?: (props?: any) => Promise<any>
  bootstrap?: (props?: any) => Promise<any>
  update?: (props?: any) => Promise<any>
}