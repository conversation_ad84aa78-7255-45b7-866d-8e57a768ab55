/**
 * 兼容类型定义主入口
 */

export * from './qiankun'
export * from './wujie'
export * from './micro-app'

// 通用兼容类型
export interface CompatibilityConfig {
  /** 兼容模式类型 */
  mode: 'qiankun' | 'wujie' | 'micro-app'
  /** 是否启用严格兼容模式 */
  strict?: boolean
  /** 自定义配置 */
  custom?: Record<string, any>
}

// 迁移配置
export interface MigrationConfig {
  /** 源框架类型 */
  from: 'qiankun' | 'wujie' | 'micro-app'
  /** 目标框架类型 */
  to: 'micro-core'
  /** 迁移选项 */
  options?: {
    /** 是否保留原有配置 */
    keepOriginalConfig?: boolean
    /** 是否自动转换路由 */
    autoConvertRoutes?: boolean
    /** 是否自动转换生命周期 */
    autoConvertLifecycle?: boolean
  }
}

// 兼容适配器基类
export interface CompatibilityAdapter {
  /** 适配器名称 */
  name: string
  /** 适配器版本 */
  version: string
  /** 初始化适配器 */
  init(config: CompatibilityConfig): Promise<void>
  /** 销毁适配器 */
  destroy(): Promise<void>
  /** 获取兼容API */
  getCompatibleApi(): Record<string, any>
}

// 桥接器接口
export interface Bridge {
  /** 桥接器名称 */
  name: string
  /** 建立桥接 */
  connect(): void
  /** 断开桥接 */
  disconnect(): void
  /** 转换数据 */
  transform(data: any): any
}