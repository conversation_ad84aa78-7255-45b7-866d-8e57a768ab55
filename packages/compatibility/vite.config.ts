import { defineConfig } from 'vite'
import { resolve } from 'path'
import dts from 'vite-plugin-dts'

export default defineConfig({
  plugins: [
    dts({
      insertTypesEntry: true,
      cleanVueFileName: true,
      skipDiagnostics: false,
      tsConfigFilePath: './tsconfig.json'
    })
  ],
  build: {
    lib: {
      entry: {
        index: resolve(__dirname, 'src/index.ts'),
        'qiankun/index': resolve(__dirname, 'src/qiankun/index.ts'),
        'wujie/index': resolve(__dirname, 'src/wujie/index.ts'),
        'micro-app/index': resolve(__dirname, 'src/micro-app/index.ts')
      },
      name: 'MicroCoreCompatibility',
      formats: ['es', 'cjs']
    },
    rollupOptions: {
      external: [
        '@micro-core/core',
        '@micro-core/shared',
        '@micro-core/sandbox',
        '@micro-core/communication',
        'qiankun',
        'wujie',
        'microapp'
      ],
      output: {
        globals: {
          '@micro-core/core': 'MicroCore',
          '@micro-core/shared': 'MicroCoreShared',
          '@micro-core/sandbox': 'MicroCoreSandbox',
          '@micro-core/communication': 'MicroCoreCommunication',
          'qiankun': 'qiankun',
          'wujie': 'wujie',
          'microapp': 'microapp'
        }
      }
    },
    sourcemap: true,
    minify: false
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@micro-core/core': resolve(__dirname, '../core/src'),
      '@micro-core/shared': resolve(__dirname, '../shared/src'),
      '@micro-core/sandbox': resolve(__dirname, '../sandbox/src'),
      '@micro-core/communication': resolve(__dirname, '../communication/src')
    }
  }
})