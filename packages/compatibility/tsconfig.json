{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@micro-core/core": ["../core/src"], "@micro-core/shared": ["../shared/src"], "@micro-core/sandbox": ["../sandbox/src"], "@micro-core/communication": ["../communication/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "__tests__/**/*"], "references": [{"path": "../core"}, {"path": "../shared"}, {"path": "../sandbox"}, {"path": "../communication"}]}