# @micro-core/adapters

微前端框架适配器系统，支持多种主流前端框架的微前端集成。

## 特性

- 🎯 **多框架支持** - 支持 React、Vue2、Vue3、Angular、Svelte、Solid、Lit、HTML、Vanilla JS
- 🔧 **统一接口** - 提供统一的适配器接口和生命周期管理
- 🤖 **自动检测** - 智能检测应用使用的框架类型
- 🛡️ **类型安全** - 完整的 TypeScript 类型定义
- 🔄 **版本兼容** - 支持框架版本兼容性检查

## 安装

```bash
pnpm add @micro-core/adapters
```

## 基础使用

### 自动创建适配器

```typescript
import { AdapterFactory } from '@micro-core/adapters'

const adapter = AdapterFactory.createAdapter({
  name: 'my-app',
  entry: 'http://localhost:3000',
  // frameworkType 可选，会自动检测
})

// 初始化应用
await adapter.bootstrap()

// 挂载应用
const container = document.getElementById('app')
await adapter.mount(container, { userId: '123' })

// 更新应用属性
await adapter.update({ theme: 'dark' })

// 卸载应用
await adapter.unmount()
```

### 手动指定框架类型

```typescript
import { ReactAdapter } from '@micro-core/adapters'

const adapter = new ReactAdapter({
  name: 'react-app',
  entry: 'http://localhost:3001',
  frameworkType: 'react'
})
```

### 框架检测

```typescript
import { FrameworkDetector } from '@micro-core/adapters'

const detector = new FrameworkDetector()
const framework = detector.detect('http://localhost:3000')
console.log('检测到的框架:', framework)

// 获取详细信息
const details = detector.detectWithDetails('http://localhost:3000')
console.log('框架详情:', details)
```

### 版本兼容性检查

```typescript
import { VersionChecker } from '@micro-core/adapters'

const isCompatible = VersionChecker.isCompatible('react', '18.2.0')
console.log('版本兼容:', isCompatible)

// 获取兼容性报告
const report = VersionChecker.getCompatibilityReport('vue3', '3.4.0')
console.log('兼容性报告:', report)
```

## 支持的框架

### React
- 支持版本：16.8+, 17.x, 18.x
- 自动检测 React 和 ReactDOM
- 支持 React 18 的并发特性

### Vue 3
- 支持版本：3.0+
- 自动检测 Vue 3 应用
- 支持组合式 API

### Vue 2
- 支持版本：2.6+
- 自动检测 Vue 2 应用
- 支持选项式 API

### Angular
- 支持版本：12+
- 自动检测 Angular 应用
- 支持模块化架构

### Svelte
- 支持版本：3.x, 4.x
- 自动检测 Svelte 组件
- 支持响应式更新

### Solid.js
- 支持版本：1.x
- 自动检测 Solid 应用
- 支持细粒度响应式

### Lit Element
- 支持版本：2.x, 3.x
- 自动检测 Lit 组件
- 支持 Web Components

### HTML
- 支持纯 HTML 应用
- 自动处理脚本和样式
- 支持属性注入

### Vanilla JavaScript
- 支持原生 JavaScript 应用
- 灵活的生命周期管理
- 兼容各种 JS 模式

## 适配器配置

```typescript
interface AdapterConfig {
  name: string                    // 应用名称
  entry: string                   // 应用入口
  frameworkType?: FrameworkType   // 框架类型（可选）
  basePath?: string              // 基础路径
  props?: Record<string, any>    // 应用属性
  lifecycle?: AdapterLifecycleHooks  // 生命周期钩子
  options?: AdapterOptions       // 适配器选项
}
```

## 生命周期钩子

```typescript
const adapter = AdapterFactory.createAdapter({
  name: 'my-app',
  entry: 'http://localhost:3000',
  lifecycle: {
    beforeBootstrap: async (props) => {
      console.log('应用初始化前')
    },
    afterBootstrap: async (props) => {
      console.log('应用初始化后')
    },
    beforeMount: async (container, props) => {
      console.log('应用挂载前')
    },
    afterMount: async (container, props) => {
      console.log('应用挂载后')
    },
    beforeUnmount: async () => {
      console.log('应用卸载前')
    },
    afterUnmount: async () => {
      console.log('应用卸载后')
    },
    onError: async (error, operation) => {
      console.error('操作错误:', operation, error)
    }
  }
})
```

## 自定义适配器

```typescript
import { AdapterBase } from '@micro-core/adapters'

class CustomAdapter extends AdapterBase {
  get frameworkType() {
    return 'custom'
  }

  get version() {
    return '1.0.0'
  }

  async bootstrap(props) {
    // 自定义初始化逻辑
  }

  async mount(container, props) {
    // 自定义挂载逻辑
  }

  async unmount() {
    // 自定义卸载逻辑
  }
}
```

## API 文档

### AdapterFactory

#### 方法

- `createAdapter(config: AdapterConfig): AdapterBase` - 创建适配器实例
- `getSupportedFrameworks(): FrameworkType[]` - 获取支持的框架列表
- `isSupported(frameworkType: FrameworkType): boolean` - 检查是否支持指定框架

### AdapterBase

#### 属性

- `frameworkType: FrameworkType` - 框架类型
- `version: string` - 适配器版本

#### 方法

- `bootstrap(props?: Record<string, any>): Promise<void>` - 初始化应用
- `mount(container: Element, props?: Record<string, any>): Promise<void>` - 挂载应用
- `unmount(): Promise<void>` - 卸载应用
- `update?(props: Record<string, any>): Promise<void>` - 更新应用属性
- `isMounted(): boolean` - 检查是否已挂载
- `getConfig(): AdapterConfig` - 获取适配器配置
- `getContext(): AdapterContext | null` - 获取适配器上下文

### FrameworkDetector

#### 方法

- `detect(entry: string): FrameworkType` - 检测框架类型
- `detectWithDetails(entry: string): FrameworkDetectionResult` - 获取详细检测结果
- `clearCache(): void` - 清除检测缓存

### VersionChecker

#### 方法

- `parseVersion(versionString: string): VersionInfo` - 解析版本字符串
- `compareVersions(version1: string, version2: string): number` - 比较版本
- `isCompatible(framework: FrameworkType, version: string): boolean` - 检查版本兼容性
- `getRecommendedVersion(framework: FrameworkType): string` - 获取推荐版本
- `getCompatibilityReport(framework: FrameworkType, version: string)` - 获取兼容性报告

## 许可证

MIT