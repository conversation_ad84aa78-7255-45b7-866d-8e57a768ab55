/**
 * 生命周期相关类型定义
 */

/**
 * 生命周期阶段
 */
export type LifecyclePhase = 
  | 'bootstrap'
  | 'mount'
  | 'unmount'
  | 'update'
  | 'error'

/**
 * 生命周期钩子
 */
export type LifecycleHook = 
  | 'beforeBootstrap'
  | 'afterBootstrap'
  | 'beforeMount'
  | 'afterMount'
  | 'beforeUnmount'
  | 'afterUnmount'
  | 'beforeUpdate'
  | 'afterUpdate'
  | 'onError'

/**
 * 生命周期事件
 */
export interface LifecycleEvent {
  /** 事件类型 */
  type: LifecycleHook
  /** 应用名称 */
  appName: string
  /** 事件时间戳 */
  timestamp: number
  /** 事件数据 */
  data?: any
  /** 错误信息（如果有） */
  error?: Error
}

/**
 * 生命周期管理器
 */
export interface LifecycleManager {
  /** 注册生命周期钩子 */
  registerHook(hook: LifecycleHook, handler: Function): void
  /** 取消注册生命周期钩子 */
  unregisterHook(hook: LifecycleHook, handler: Function): void
  /** 触发生命周期钩子 */
  triggerHook(hook: LifecycleHook, ...args: any[]): Promise<void>
  /** 获取所有钩子 */
  getHooks(hook: LifecycleHook): Function[]
  /** 清除所有钩子 */
  clearHooks(): void
}