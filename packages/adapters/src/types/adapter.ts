/**
 * 适配器相关类型定义
 */

/**
 * 支持的框架类型
 */
export type FrameworkType = 
  | 'react'
  | 'vue2'
  | 'vue3'
  | 'angular'
  | 'svelte'
  | 'solid'
  | 'lit'
  | 'html'
  | 'vanilla'

/**
 * 适配器配置
 */
export interface AdapterConfig {
  /** 应用名称 */
  name: string
  /** 应用入口 */
  entry: string
  /** 框架类型（可选，会自动检测） */
  frameworkType?: FrameworkType
  /** 应用基础路径 */
  basePath?: string
  /** 应用属性 */
  props?: Record<string, any>
  /** 生命周期钩子 */
  lifecycle?: AdapterLifecycleHooks
  /** 适配器选项 */
  options?: AdapterOptions
}

/**
 * 适配器上下文
 */
export interface AdapterContext {
  /** 应用实例 */
  instance: any
  /** 容器元素 */
  container: Element
  /** 应用属性 */
  props: Record<string, any>
  /** 挂载时间 */
  mountTime: number
  /** 应用状态 */
  status: 'loading' | 'mounted' | 'unmounted' | 'error'
}

/**
 * 适配器选项
 */
export interface AdapterOptions {
  /** 是否启用沙箱 */
  sandbox?: boolean
  /** 沙箱类型 */
  sandboxType?: 'proxy' | 'iframe' | 'webcomponent'
  /** 是否启用样式隔离 */
  styleIsolation?: boolean
  /** 是否启用 JavaScript 隔离 */
  jsIsolation?: boolean
  /** 超时时间（毫秒） */
  timeout?: number
  /** 错误处理策略 */
  errorHandling?: 'throw' | 'log' | 'ignore'
  /** 自定义加载器 */
  loader?: CustomLoader
}

/**
 * 框架信息
 */
export interface FrameworkInfo {
  /** 框架类型 */
  type: FrameworkType
  /** 框架版本 */
  version: string
  /** 框架名称 */
  name: string
  /** 是否支持 */
  supported: boolean
  /** 检测置信度 */
  confidence: number
}

/**
 * 版本信息
 */
export interface VersionInfo {
  /** 主版本号 */
  major: number
  /** 次版本号 */
  minor: number
  /** 修订版本号 */
  patch: number
  /** 预发布版本 */
  prerelease?: string
  /** 构建元数据 */
  build?: string
  /** 原始版本字符串 */
  raw: string
}

/**
 * 适配器生命周期钩子
 */
export interface AdapterLifecycleHooks {
  /** 初始化前 */
  beforeBootstrap?: (props?: Record<string, any>) => Promise<void> | void
  /** 初始化后 */
  afterBootstrap?: (props?: Record<string, any>) => Promise<void> | void
  /** 挂载前 */
  beforeMount?: (container: Element, props?: Record<string, any>) => Promise<void> | void
  /** 挂载后 */
  afterMount?: (container: Element, props?: Record<string, any>) => Promise<void> | void
  /** 卸载前 */
  beforeUnmount?: () => Promise<void> | void
  /** 卸载后 */
  afterUnmount?: () => Promise<void> | void
  /** 更新前 */
  beforeUpdate?: (props: Record<string, any>) => Promise<void> | void
  /** 更新后 */
  afterUpdate?: (props: Record<string, any>) => Promise<void> | void
  /** 错误处理 */
  onError?: (error: Error, operation: string) => Promise<void> | void
}

/**
 * 自定义加载器
 */
export interface CustomLoader {
  /** 加载脚本 */
  loadScript?: (url: string) => Promise<void>
  /** 加载样式 */
  loadStyle?: (url: string) => Promise<void>
  /** 加载模块 */
  loadModule?: (url: string) => Promise<any>
}