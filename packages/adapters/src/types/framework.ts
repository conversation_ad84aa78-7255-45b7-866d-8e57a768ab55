/**
 * 框架检测相关类型定义
 */

import type { FrameworkType, FrameworkInfo } from './adapter'

/**
 * 框架检测结果
 */
export interface FrameworkDetectionResult {
  /** 检测到的框架 */
  framework: FrameworkType
  /** 框架信息 */
  info: FrameworkInfo
  /** 检测策略 */
  strategy: DetectionStrategy
  /** 检测时间 */
  detectionTime: number
}

/**
 * 检测策略
 */
export type DetectionStrategy = 
  | 'global-variable'    // 全局变量检测
  | 'package-json'       // package.json 检测
  | 'file-extension'     // 文件扩展名检测
  | 'dom-analysis'       // DOM 分析检测
  | 'script-analysis'    // 脚本分析检测
  | 'manual'             // 手动指定

/**
 * 检测器配置
 */
export interface DetectorConfig {
  /** 启用的检测策略 */
  strategies?: DetectionStrategy[]
  /** 检测超时时间 */
  timeout?: number
  /** 是否启用缓存 */
  cache?: boolean
  /** 缓存过期时间 */
  cacheExpiry?: number
}

/**
 * 全局变量检测规则
 */
export interface GlobalVariableRule {
  /** 框架类型 */
  framework: FrameworkType
  /** 全局变量名 */
  variable: string
  /** 版本获取路径 */
  versionPath?: string
  /** 检测函数 */
  detector?: (global: any) => boolean
}

/**
 * 文件扩展名检测规则
 */
export interface FileExtensionRule {
  /** 框架类型 */
  framework: FrameworkType
  /** 文件扩展名 */
  extensions: string[]
  /** 权重 */
  weight: number
}

/**
 * Package.json 检测规则
 */
export interface PackageJsonRule {
  /** 框架类型 */
  framework: FrameworkType
  /** 依赖包名 */
  dependencies: string[]
  /** 开发依赖包名 */
  devDependencies?: string[]
  /** 权重 */
  weight: number
}