/**
 * 适配器工厂
 * 
 * 自动检测框架类型并创建对应的适配器实例
 */

import { FrameworkDetector } from '../detector/framework-detector'
import { ReactAdapter } from '../frameworks/react'
import { Vue2Adapter } from '../frameworks/vue2'
import { Vue3Adapter } from '../frameworks/vue3'
import { AngularAdapter } from '../frameworks/angular'
import { SvelteAdapter } from '../frameworks/svelte'
import { SolidAdapter } from '../frameworks/solid'
import { LitAdapter } from '../frameworks/lit'
import { HtmlAdapter } from '../frameworks/html'
import { VanillaAdapter } from '../frameworks/vanilla'
import type { AdapterBase } from './adapter-base'
import type { AdapterConfig, FrameworkType } from '../types'

export class AdapterFactory {
  private static detectors = new FrameworkDetector()

  /**
   * 创建适配器实例
   */
  static createAdapter(config: AdapterConfig): AdapterBase {
    const frameworkType = config.frameworkType || this.detectFramework(config)
    
    switch (frameworkType) {
      case 'react':
        return new ReactAdapter(config)
      case 'vue2':
        return new Vue2Adapter(config)
      case 'vue3':
        return new Vue3Adapter(config)
      case 'angular':
        return new AngularAdapter(config)
      case 'svelte':
        return new SvelteAdapter(config)
      case 'solid':
        return new SolidAdapter(config)
      case 'lit':
        return new LitAdapter(config)
      case 'html':
        return new HtmlAdapter(config)
      case 'vanilla':
        return new VanillaAdapter(config)
      default:
        throw new Error(`不支持的框架类型: ${frameworkType}`)
    }
  }

  /**
   * 自动检测框架类型
   */
  private static detectFramework(config: AdapterConfig): FrameworkType {
    try {
      return this.detectors.detect(config.entry)
    } catch (error) {
      console.warn('框架检测失败，使用默认的 vanilla 适配器:', error)
      return 'vanilla'
    }
  }

  /**
   * 获取支持的框架类型列表
   */
  static getSupportedFrameworks(): FrameworkType[] {
    return [
      'react',
      'vue2', 
      'vue3',
      'angular',
      'svelte',
      'solid',
      'lit',
      'html',
      'vanilla'
    ]
  }

  /**
   * 检查是否支持指定框架
   */
  static isSupported(frameworkType: FrameworkType): boolean {
    return this.getSupportedFrameworks().includes(frameworkType)
  }
}