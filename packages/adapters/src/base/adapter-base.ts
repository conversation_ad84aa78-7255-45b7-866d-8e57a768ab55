/**
 * 适配器基类
 * 
 * 定义所有框架适配器的统一接口和生命周期
 */

import type { MicroCoreError } from '@micro-core/shared'
import type { AdapterConfig, AdapterContext, FrameworkType } from '../types'

export abstract class AdapterBase {
  protected config: AdapterConfig
  protected context: AdapterContext | null = null
  protected mounted = false

  constructor(config: AdapterConfig) {
    this.config = config
  }

  /**
   * 获取适配器支持的框架类型
   */
  abstract get frameworkType(): FrameworkType

  /**
   * 获取适配器版本
   */
  abstract get version(): string

  /**
   * 初始化应用
   */
  abstract bootstrap(props?: Record<string, any>): Promise<void>

  /**
   * 挂载应用到指定容器
   */
  abstract mount(container: Element, props?: Record<string, any>): Promise<void>

  /**
   * 卸载应用
   */
  abstract unmount(): Promise<void>

  /**
   * 更新应用属性
   */
  abstract update?(props: Record<string, any>): Promise<void>

  /**
   * 检查是否已挂载
   */
  isMounted(): boolean {
    return this.mounted
  }

  /**
   * 获取适配器配置
   */
  getConfig(): AdapterConfig {
    return { ...this.config }
  }

  /**
   * 获取适配器上下文
   */
  getContext(): AdapterContext | null {
    return this.context
  }

  /**
   * 设置适配器上下文
   */
  protected setContext(context: AdapterContext): void {
    this.context = context
  }

  /**
   * 验证容器元素
   */
  protected validateContainer(container: Element): void {
    if (!container) {
      throw new Error('容器元素不能为空')
    }
    if (!(container instanceof Element)) {
      throw new Error('容器必须是有效的 DOM 元素')
    }
  }

  /**
   * 验证应用入口
   */
  protected validateEntry(): void {
    if (!this.config.entry) {
      throw new Error('应用入口不能为空')
    }
  }

  /**
   * 处理适配器错误
   */
  protected handleError(error: Error, operation: string): never {
    const message = `${this.frameworkType} 适配器在 ${operation} 操作中发生错误: ${error.message}`
    throw new Error(message)
  }

  /**
   * 清理资源
   */
  protected cleanup(): void {
    this.context = null
    this.mounted = false
  }
}