/**
 * Solid 适配器
 * 
 * 支持 Solid.js 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class SolidAdapter extends AdapterBase {
  private solidDispose: (() => void) | null = null

  get frameworkType(): FrameworkType {
    return 'solid'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Solid 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 Solid 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 Solid 应用导出
      this.validateSolidApp(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 Solid 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('Solid 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('Solid 应用未初始化')
      }

      // 挂载 Solid 应用
      await this.mountSolidApp(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 Solid 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 Solid 应用
      await this.unmountSolidApp()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 Solid 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context) {
        throw new Error('Solid 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // Solid 应用通常需要重新挂载来更新属性
      if (this.context.container) {
        await this.unmountSolidApp()
        await this.mountSolidApp(this.context.instance, this.context.container, mergedProps)
      }

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 Solid 应用失败: ${error}`)
    }
  }

  /**
   * 验证 Solid 应用导出
   */
  private validateSolidApp(appModule: any): void {
    if (!appModule) {
      throw new Error('Solid 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`Solid 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 Solid 应用
   */
  private async mountSolidApp(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        const result = await appModule.mount(container, props)
        this.solidDispose = result?.dispose || result
        return
      }

      // 否则使用默认的 Solid 挂载逻辑
      await this.defaultSolidMount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 Solid 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Solid 挂载逻辑
   */
  private async defaultSolidMount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 尝试获取 Solid
      const { render } = await this.loadSolid()

      if (!render) {
        throw new Error('无法加载 Solid render 函数')
      }

      // 获取应用组件
      const App = appModule.default || appModule

      // 渲染 Solid 应用
      const dispose = render(() => App(props), container)
      this.solidDispose = dispose

    } catch (error) {
      throw new Error(`默认 Solid 挂载失败: ${error}`)
    }
  }

  /**
   * 卸载 Solid 应用
   */
  private async unmountSolidApp(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 否则使用默认的 Solid 卸载逻辑
      await this.defaultSolidUnmount()
    } catch (error) {
      throw new Error(`卸载 Solid 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Solid 卸载逻辑
   */
  private async defaultSolidUnmount(): Promise<void> {
    if (this.solidDispose && typeof this.solidDispose === 'function') {
      this.solidDispose()
    }

    // 清理容器内容
    if (this.context?.container) {
      this.context.container.innerHTML = ''
    }

    this.solidDispose = null
  }

  /**
   * 加载 Solid
   */
  private async loadSolid(): Promise<any> {
    try {
      return await import('solid-js/web')
    } catch (error) {
      console.warn('无法动态加载 Solid，尝试从全局变量获取')
      return (window as any).Solid || {}
    }
  }
}