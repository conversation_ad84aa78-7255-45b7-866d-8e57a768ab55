/**
 * HTML 适配器
 * 
 * 支持纯 HTML 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class HtmlAdapter extends AdapterBase {
  private htmlContent: string = ''
  private scriptElements: HTMLScriptElement[] = []
  private styleElements: HTMLStyleElement[] = []

  get frameworkType(): FrameworkType {
    return 'html'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 HTML 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 HTML 内容
      const htmlContent = await this.loadHtmlContent()
      
      // 设置上下文
      this.setContext({
        instance: { htmlContent },
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      this.htmlContent = htmlContent

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 HTML 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('HTML 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 挂载 HTML 内容
      await this.mountHtmlContent(container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 HTML 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 HTML 内容
      await this.unmountHtmlContent()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 HTML 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context) {
        throw new Error('HTML 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // 重新渲染 HTML 内容
      if (this.context.container) {
        await this.unmountHtmlContent()
        await this.mountHtmlContent(this.context.container, mergedProps)
      }

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载 HTML 内容
   */
  private async loadHtmlContent(): Promise<string> {
    try {
      const response = await fetch(this.config.entry)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      return await response.text()
    } catch (error) {
      throw new Error(`加载 HTML 内容失败: ${error}`)
    }
  }

  /**
   * 挂载 HTML 内容
   */
  private async mountHtmlContent(container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 解析 HTML 内容
      const parser = new DOMParser()
      const doc = parser.parseFromString(this.htmlContent, 'text/html')

      // 提取样式
      const styles = doc.querySelectorAll('style, link[rel="stylesheet"]')
      await this.loadStyles(styles)

      // 提取脚本
      const scripts = doc.querySelectorAll('script')
      
      // 插入 HTML 内容
      const bodyContent = doc.body?.innerHTML || this.htmlContent
      container.innerHTML = this.injectProps(bodyContent, props)

      // 执行脚本
      await this.loadScripts(scripts, props)

    } catch (error) {
      throw new Error(`挂载 HTML 内容失败: ${error}`)
    }
  }

  /**
   * 卸载 HTML 内容
   */
  private async unmountHtmlContent(): Promise<void> {
    try {
      // 清理脚本
      this.scriptElements.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script)
        }
      })
      this.scriptElements = []

      // 清理样式
      this.styleElements.forEach(style => {
        if (style.parentNode) {
          style.parentNode.removeChild(style)
        }
      })
      this.styleElements = []

      // 清理容器内容
      if (this.context?.container) {
        this.context.container.innerHTML = ''
      }

    } catch (error) {
      throw new Error(`卸载 HTML 内容失败: ${error}`)
    }
  }

  /**
   * 加载样式
   */
  private async loadStyles(styles: NodeListOf<Element>): Promise<void> {
    for (const styleElement of styles) {
      try {
        if (styleElement.tagName === 'STYLE') {
          // 内联样式
          const style = document.createElement('style')
          style.textContent = styleElement.textContent
          document.head.appendChild(style)
          this.styleElements.push(style)
        } else if (styleElement.tagName === 'LINK') {
          // 外部样式
          const link = styleElement as HTMLLinkElement
          if (link.href) {
            const style = document.createElement('style')
            const response = await fetch(link.href)
            style.textContent = await response.text()
            document.head.appendChild(style)
            this.styleElements.push(style)
          }
        }
      } catch (error) {
        console.warn('加载样式失败:', error)
      }
    }
  }

  /**
   * 加载脚本
   */
  private async loadScripts(scripts: NodeListOf<Element>, props: Record<string, any>): Promise<void> {
    for (const scriptElement of scripts) {
      try {
        const script = document.createElement('script')
        const originalScript = scriptElement as HTMLScriptElement

        // 复制属性
        Array.from(originalScript.attributes).forEach(attr => {
          script.setAttribute(attr.name, attr.value)
        })

        if (originalScript.src) {
          // 外部脚本
          await new Promise<void>((resolve, reject) => {
            script.onload = () => resolve()
            script.onerror = () => reject(new Error(`加载脚本失败: ${originalScript.src}`))
            document.head.appendChild(script)
          })
        } else {
          // 内联脚本
          const scriptContent = this.injectProps(originalScript.textContent || '', props)
          script.textContent = scriptContent
          document.head.appendChild(script)
        }

        this.scriptElements.push(script)
      } catch (error) {
        console.warn('加载脚本失败:', error)
      }
    }
  }

  /**
   * 注入属性到内容中
   */
  private injectProps(content: string, props: Record<string, any>): string {
    let result = content

    // 替换属性占位符
    Object.entries(props).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g')
      result = result.replace(placeholder, String(value))
    })

    // 注入全局属性对象
    if (Object.keys(props).length > 0) {
      const propsScript = `
        <script>
          window.microAppProps = ${JSON.stringify(props)};
        </script>
      `
      result = propsScript + result
    }

    return result
  }
}