/**
 * Angular 适配器
 * 
 * 支持 Angular 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class AngularAdapter extends AdapterBase {
  private ngModuleRef: any = null
  private applicationRef: any = null

  get frameworkType(): FrameworkType {
    return 'angular'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Angular 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 Angular 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 Angular 应用导出
      this.validateAngularApp(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 Angular 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('Angular 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('Angular 应用未初始化')
      }

      // 挂载 Angular 应用
      await this.mountAngularApp(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 Angular 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 Angular 应用
      await this.unmountAngularApp()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 Angular 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context) {
        throw new Error('Angular 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // Angular 应用通常需要重新挂载来更新属性
      if (this.context.container) {
        await this.unmountAngularApp()
        await this.mountAngularApp(this.context.instance, this.context.container, mergedProps)
      }

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 Angular 应用失败: ${error}`)
    }
  }

  /**
   * 验证 Angular 应用导出
   */
  private validateAngularApp(appModule: any): void {
    if (!appModule) {
      throw new Error('Angular 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`Angular 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 Angular 应用
   */
  private async mountAngularApp(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        const result = await appModule.mount(container, props)
        this.ngModuleRef = result?.ngModuleRef || result
        this.applicationRef = result?.applicationRef
        return
      }

      // 否则使用默认的 Angular 挂载逻辑
      await this.defaultAngularMount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 Angular 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Angular 挂载逻辑
   */
  private async defaultAngularMount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 尝试获取 Angular 平台
      const { platformBrowserDynamic } = await this.loadAngularPlatform()
      
      if (!platformBrowserDynamic) {
        throw new Error('无法加载 Angular 平台')
      }

      // 设置容器
      container.innerHTML = '<app-root></app-root>'

      // 引导 Angular 应用
      const AppModule = appModule.default || appModule
      const ngModuleRef = await platformBrowserDynamic().bootstrapModule(AppModule)
      
      this.ngModuleRef = ngModuleRef
      this.applicationRef = ngModuleRef.injector.get('ApplicationRef')

      // 注入属性
      if (props && Object.keys(props).length > 0) {
        this.injectProps(props)
      }

    } catch (error) {
      throw new Error(`默认 Angular 挂载失败: ${error}`)
    }
  }

  /**
   * 卸载 Angular 应用
   */
  private async unmountAngularApp(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 否则使用默认的 Angular 卸载逻辑
      await this.defaultAngularUnmount()
    } catch (error) {
      throw new Error(`卸载 Angular 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Angular 卸载逻辑
   */
  private async defaultAngularUnmount(): Promise<void> {
    try {
      // 销毁 Angular 模块
      if (this.ngModuleRef && typeof this.ngModuleRef.destroy === 'function') {
        this.ngModuleRef.destroy()
      }

      // 清理容器内容
      if (this.context?.container) {
        this.context.container.innerHTML = ''
      }

    } finally {
      this.ngModuleRef = null
      this.applicationRef = null
    }
  }

  /**
   * 注入属性
   */
  private injectProps(props: Record<string, any>): void {
    try {
      // 将属性注入到全局对象中，供 Angular 应用使用
      (window as any).microAppProps = props

      // 如果有应用引用，尝试触发变更检测
      if (this.applicationRef && typeof this.applicationRef.tick === 'function') {
        this.applicationRef.tick()
      }
    } catch (error) {
      console.warn('注入属性失败:', error)
    }
  }

  /**
   * 加载 Angular 平台
   */
  private async loadAngularPlatform(): Promise<any> {
    try {
      return await import('@angular/platform-browser-dynamic')
    } catch (error) {
      console.warn('无法动态加载 Angular 平台，尝试从全局变量获取')
      return (window as any).ng?.platformBrowserDynamic ? { platformBrowserDynamic: (window as any).ng.platformBrowserDynamic } : null
    }
  }
}