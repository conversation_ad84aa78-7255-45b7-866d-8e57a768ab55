/**
 * Lit 适配器
 * 
 * 支持 Lit Element 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class LitAdapter extends AdapterBase {
  private litElement: any = null

  get frameworkType(): FrameworkType {
    return 'lit'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Lit 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 Lit 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 Lit 应用导出
      this.validateLitApp(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 Lit 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('Lit 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('Lit 应用未初始化')
      }

      // 挂载 Lit 应用
      await this.mountLitApp(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 Lit 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 Lit 应用
      await this.unmountLitApp()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 Lit 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context || !this.litElement) {
        throw new Error('Lit 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // 更新 Lit 元素的属性
      Object.entries(mergedProps).forEach(([key, value]) => {
        if (this.litElement && key in this.litElement) {
          this.litElement[key] = value
        }
      })

      // 触发更新
      if (this.litElement && typeof this.litElement.requestUpdate === 'function') {
        this.litElement.requestUpdate()
      }

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 Lit 应用失败: ${error}`)
    }
  }

  /**
   * 验证 Lit 应用导出
   */
  private validateLitApp(appModule: any): void {
    if (!appModule) {
      throw new Error('Lit 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`Lit 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 Lit 应用
   */
  private async mountLitApp(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        const result = await appModule.mount(container, props)
        this.litElement = result || this.litElement
        return
      }

      // 否则使用默认的 Lit 挂载逻辑
      await this.defaultLitMount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 Lit 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Lit 挂载逻辑
   */
  private async defaultLitMount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 获取 Lit 元素类
      const LitElementClass = appModule.default || appModule

      // 生成唯一的标签名
      const tagName = this.generateTagName()

      // 定义自定义元素
      if (!customElements.get(tagName)) {
        customElements.define(tagName, LitElementClass)
      }

      // 创建元素实例
      const element = document.createElement(tagName)

      // 设置属性
      Object.entries(props).forEach(([key, value]) => {
        if (key in element) {
          (element as any)[key] = value
        } else {
          element.setAttribute(key, String(value))
        }
      })

      // 添加到容器
      container.appendChild(element)

      this.litElement = element

    } catch (error) {
      throw new Error(`默认 Lit 挂载失败: ${error}`)
    }
  }

  /**
   * 卸载 Lit 应用
   */
  private async unmountLitApp(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 否则使用默认的 Lit 卸载逻辑
      await this.defaultLitUnmount()
    } catch (error) {
      throw new Error(`卸载 Lit 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Lit 卸载逻辑
   */
  private async defaultLitUnmount(): Promise<void> {
    if (this.litElement && this.litElement.parentNode) {
      this.litElement.parentNode.removeChild(this.litElement)
    }

    // 清理容器内容
    if (this.context?.container) {
      this.context.container.innerHTML = ''
    }

    this.litElement = null
  }

  /**
   * 生成唯一标签名
   */
  private generateTagName(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    return `micro-lit-${timestamp}-${random}`
  }
}