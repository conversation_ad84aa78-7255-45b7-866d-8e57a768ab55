/**
 * React 适配器
 * 
 * 支持 React 16.8+ 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class ReactAdapter extends AdapterBase {
  private reactInstance: any = null
  private reactDOMInstance: any = null

  get frameworkType(): FrameworkType {
    return 'react'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 React 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 React 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 React 应用导出
      this.validateReactApp(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 React 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('React 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('React 应用未初始化')
      }

      // 挂载 React 应用
      await this.mountReactApp(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 React 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 React 应用
      await this.unmountReactApp()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 React 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context) {
        throw new Error('React 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // 重新渲染应用
      await this.updateReactApp(mergedProps)

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 React 应用失败: ${error}`)
    }
  }

  /**
   * 验证 React 应用导出
   */
  private validateReactApp(appModule: any): void {
    if (!appModule) {
      throw new Error('React 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`React 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 React 应用
   */
  private async mountReactApp(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        await appModule.mount(container, props)
        return
      }

      // 否则使用默认的 React 挂载逻辑
      await this.defaultReactMount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 React 应用失败: ${error}`)
    }
  }

  /**
   * 默认 React 挂载逻辑
   */
  private async defaultReactMount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    // 尝试获取 React 和 ReactDOM
    const React = (window as any).React || await this.loadReact()
    const ReactDOM = (window as any).ReactDOM || await this.loadReactDOM()

    if (!React || !ReactDOM) {
      throw new Error('无法加载 React 或 ReactDOM')
    }

    // 创建 React 元素
    const App = appModule.default || appModule
    const element = React.createElement(App, props)

    // 使用 ReactDOM 渲染
    if (ReactDOM.createRoot) {
      // React 18+
      const root = ReactDOM.createRoot(container)
      root.render(element)
      this.reactInstance = root
    } else {
      // React 16/17
      ReactDOM.render(element, container)
      this.reactInstance = { container, element }
    }

    this.reactDOMInstance = ReactDOM
  }

  /**
   * 卸载 React 应用
   */
  private async unmountReactApp(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 否则使用默认的 React 卸载逻辑
      await this.defaultReactUnmount()
    } catch (error) {
      throw new Error(`卸载 React 应用失败: ${error}`)
    }
  }

  /**
   * 默认 React 卸载逻辑
   */
  private async defaultReactUnmount(): Promise<void> {
    if (!this.reactInstance || !this.reactDOMInstance) {
      return
    }

    try {
      if (this.reactInstance.unmount) {
        // React 18+
        this.reactInstance.unmount()
      } else if (this.reactInstance.container) {
        // React 16/17
        this.reactDOMInstance.unmountComponentAtNode(this.reactInstance.container)
      }
    } finally {
      this.reactInstance = null
      this.reactDOMInstance = null
    }
  }

  /**
   * 更新 React 应用
   */
  private async updateReactApp(props: Record<string, any>): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 update 方法，使用它
      if (appModule && typeof appModule.update === 'function') {
        await appModule.update(props)
        return
      }

      // 否则重新挂载应用
      if (this.context?.container) {
        await this.unmountReactApp()
        await this.mountReactApp(appModule, this.context.container, props)
      }
    } catch (error) {
      throw new Error(`更新 React 应用失败: ${error}`)
    }
  }

  /**
   * 加载 React
   */
  private async loadReact(): Promise<any> {
    try {
      return await import('react')
    } catch (error) {
      console.warn('无法动态加载 React，尝试从全局变量获取')
      return (window as any).React
    }
  }

  /**
   * 加载 ReactDOM
   */
  private async loadReactDOM(): Promise<any> {
    try {
      return await import('react-dom')
    } catch (error) {
      console.warn('无法动态加载 ReactDOM，尝试从全局变量获取')
      return (window as any).ReactDOM
    }
  }
}