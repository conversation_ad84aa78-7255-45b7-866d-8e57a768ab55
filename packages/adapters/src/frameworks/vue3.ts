/**
 * Vue3 适配器
 * 
 * 支持 Vue 3.x 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class Vue3Adapter extends AdapterBase {
  private vueApp: any = null
  private vueInstance: any = null

  get frameworkType(): FrameworkType {
    return 'vue3'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Vue3 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 Vue3 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 Vue3 应用导出
      this.validateVue3App(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 Vue3 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('Vue3 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('Vue3 应用未初始化')
      }

      // 挂载 Vue3 应用
      await this.mountVue3App(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 Vue3 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 Vue3 应用
      await this.unmountVue3App()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 Vue3 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context || !this.vueInstance) {
        throw new Error('Vue3 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // 更新 Vue 实例的 props
      Object.assign(this.vueInstance.props, mergedProps)

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 Vue3 应用失败: ${error}`)
    }
  }

  /**
   * 验证 Vue3 应用导出
   */
  private validateVue3App(appModule: any): void {
    if (!appModule) {
      throw new Error('Vue3 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`Vue3 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 Vue3 应用
   */
  private async mountVue3App(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        const result = await appModule.mount(container, props)
        this.vueApp = result?.app || result
        this.vueInstance = result?.instance || result
        return
      }

      // 否则使用默认的 Vue3 挂载逻辑
      await this.defaultVue3Mount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 Vue3 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Vue3 挂载逻辑
   */
  private async defaultVue3Mount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    // 尝试获取 Vue
    const Vue = (window as any).Vue || await this.loadVue3()

    if (!Vue || !Vue.createApp) {
      throw new Error('无法加载 Vue 3')
    }

    // 创建 Vue 应用实例
    const App = appModule.default || appModule
    const app = Vue.createApp(App, props)

    // 挂载应用
    const instance = app.mount(container)

    this.vueApp = app
    this.vueInstance = instance
  }

  /**
   * 卸载 Vue3 应用
   */
  private async unmountVue3App(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 否则使用默认的 Vue3 卸载逻辑
      await this.defaultVue3Unmount()
    } catch (error) {
      throw new Error(`卸载 Vue3 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Vue3 卸载逻辑
   */
  private async defaultVue3Unmount(): Promise<void> {
    if (this.vueApp && typeof this.vueApp.unmount === 'function') {
      this.vueApp.unmount()
    }

    this.vueApp = null
    this.vueInstance = null
  }

  /**
   * 加载 Vue3
   */
  private async loadVue3(): Promise<any> {
    try {
      return await import('vue')
    } catch (error) {
      console.warn('无法动态加载 Vue 3，尝试从全局变量获取')
      return (window as any).Vue
    }
  }
}