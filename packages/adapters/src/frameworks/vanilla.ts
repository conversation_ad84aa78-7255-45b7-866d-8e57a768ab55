/**
 * Vanilla JavaScript 适配器
 * 
 * 支持原生 JavaScript 应用的微前端集成
 */

import { AdapterBase } from '../base/adapter-base'
import type { FrameworkType, AdapterConfig, AdapterContext } from '../types'

export class VanillaAdapter extends AdapterBase {
  private appInstance: any = null

  get frameworkType(): FrameworkType {
    return 'vanilla'
  }

  get version(): string {
    return '1.0.0'
  }

  /**
   * 初始化 Vanilla 应用
   */
  async bootstrap(props?: Record<string, any>): Promise<void> {
    try {
      await this.config.lifecycle?.beforeBootstrap?.(props)
      
      this.validateEntry()
      
      // 加载 Vanilla 应用入口
      const appModule = await this.loadAppModule()
      
      // 验证 Vanilla 应用导出
      this.validateVanillaApp(appModule)
      
      // 设置上下文
      this.setContext({
        instance: appModule,
        container: null as any,
        props: props || {},
        mountTime: 0,
        status: 'loading'
      })

      await this.config.lifecycle?.afterBootstrap?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'bootstrap')
      this.handleError(error as Error, 'bootstrap')
    }
  }

  /**
   * 挂载 Vanilla 应用
   */
  async mount(container: Element, props?: Record<string, any>): Promise<void> {
    try {
      this.validateContainer(container)
      
      if (this.mounted) {
        throw new Error('Vanilla 应用已经挂载')
      }

      await this.config.lifecycle?.beforeMount?.(container, props)

      const mergedProps = { ...this.config.props, ...props }
      
      // 获取应用实例
      const appModule = this.context?.instance
      if (!appModule) {
        throw new Error('Vanilla 应用未初始化')
      }

      // 挂载 Vanilla 应用
      await this.mountVanillaApp(appModule, container, mergedProps)

      // 更新上下文
      if (this.context) {
        this.context.container = container
        this.context.props = mergedProps
        this.context.mountTime = Date.now()
        this.context.status = 'mounted'
      }

      this.mounted = true

      await this.config.lifecycle?.afterMount?.(container, props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'mount')
      this.handleError(error as Error, 'mount')
    }
  }

  /**
   * 卸载 Vanilla 应用
   */
  async unmount(): Promise<void> {
    try {
      if (!this.mounted) {
        return
      }

      await this.config.lifecycle?.beforeUnmount?.()

      // 卸载 Vanilla 应用
      await this.unmountVanillaApp()

      // 更新状态
      if (this.context) {
        this.context.status = 'unmounted'
      }
      this.mounted = false

      await this.config.lifecycle?.afterUnmount?.()
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'unmount')
      this.handleError(error as Error, 'unmount')
    }
  }

  /**
   * 更新 Vanilla 应用属性
   */
  async update(props: Record<string, any>): Promise<void> {
    try {
      if (!this.mounted || !this.context) {
        throw new Error('Vanilla 应用未挂载')
      }

      await this.config.lifecycle?.beforeUpdate?.(props)

      // 更新属性
      const mergedProps = { ...this.context.props, ...props }
      this.context.props = mergedProps

      // 更新应用
      const appModule = this.context.instance
      if (appModule && typeof appModule.update === 'function') {
        await appModule.update(mergedProps)
      }

      await this.config.lifecycle?.afterUpdate?.(props)
    } catch (error) {
      await this.config.lifecycle?.onError?.(error as Error, 'update')
      this.handleError(error as Error, 'update')
    }
  }

  /**
   * 加载应用模块
   */
  private async loadAppModule(): Promise<any> {
    try {
      // 动态导入应用模块
      const module = await import(this.config.entry)
      return module.default || module
    } catch (error) {
      throw new Error(`加载 Vanilla 应用失败: ${error}`)
    }
  }

  /**
   * 验证 Vanilla 应用导出
   */
  private validateVanillaApp(appModule: any): void {
    if (!appModule) {
      throw new Error('Vanilla 应用模块导出为空')
    }

    // 检查是否有必要的生命周期方法
    const requiredMethods = ['bootstrap', 'mount', 'unmount']
    const missingMethods = requiredMethods.filter(method => typeof appModule[method] !== 'function')
    
    if (missingMethods.length > 0) {
      console.warn(`Vanilla 应用缺少生命周期方法: ${missingMethods.join(', ')}`)
    }
  }

  /**
   * 挂载 Vanilla 应用
   */
  private async mountVanillaApp(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    try {
      // 如果应用导出了 mount 方法，使用它
      if (typeof appModule.mount === 'function') {
        const result = await appModule.mount(container, props)
        this.appInstance = result || appModule
        return
      }

      // 否则使用默认的挂载逻辑
      await this.defaultVanillaMount(appModule, container, props)
    } catch (error) {
      throw new Error(`挂载 Vanilla 应用失败: ${error}`)
    }
  }

  /**
   * 默认 Vanilla 挂载逻辑
   */
  private async defaultVanillaMount(appModule: any, container: Element, props: Record<string, any>): Promise<void> {
    // 如果是函数，直接调用
    if (typeof appModule === 'function') {
      const result = await appModule(container, props)
      this.appInstance = result || appModule
      return
    }

    // 如果有 render 方法，调用它
    if (typeof appModule.render === 'function') {
      const result = await appModule.render(container, props)
      this.appInstance = result || appModule
      return
    }

    // 如果有 init 方法，调用它
    if (typeof appModule.init === 'function') {
      const result = await appModule.init(container, props)
      this.appInstance = result || appModule
      return
    }

    // 默认情况下，将模块作为实例
    this.appInstance = appModule
  }

  /**
   * 卸载 Vanilla 应用
   */
  private async unmountVanillaApp(): Promise<void> {
    try {
      const appModule = this.context?.instance
      
      // 如果应用导出了 unmount 方法，使用它
      if (appModule && typeof appModule.unmount === 'function') {
        await appModule.unmount()
        return
      }

      // 如果实例有 destroy 方法，调用它
      if (this.appInstance && typeof this.appInstance.destroy === 'function') {
        await this.appInstance.destroy()
      }

      // 如果实例有 cleanup 方法，调用它
      if (this.appInstance && typeof this.appInstance.cleanup === 'function') {
        await this.appInstance.cleanup()
      }

      // 清理容器内容
      if (this.context?.container) {
        this.context.container.innerHTML = ''
      }

    } catch (error) {
      throw new Error(`卸载 Vanilla 应用失败: ${error}`)
    } finally {
      this.appInstance = null
    }
  }
}