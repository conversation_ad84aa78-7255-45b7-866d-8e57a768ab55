/**
 * 框架检测器
 * 
 * 自动检测应用使用的前端框架类型
 */

import type { 
  FrameworkType, 
  FrameworkInfo,
  FrameworkDetectionResult,
  DetectionStrategy,
  DetectorConfig,
  GlobalVariableRule,
  FileExtensionRule,
  PackageJsonRule
} from '../types'

export class FrameworkDetector {
  private config: DetectorConfig
  private cache = new Map<string, FrameworkDetectionResult>()

  // 全局变量检测规则
  private globalVariableRules: GlobalVariableRule[] = [
    { framework: 'react', variable: 'React', versionPath: 'version' },
    { framework: 'vue3', variable: 'Vue', versionPath: 'version' },
    { framework: 'vue2', variable: 'Vue', versionPath: 'version', detector: (Vue) => Vue.version?.startsWith('2') },
    { framework: 'angular', variable: 'ng', versionPath: 'version.full' },
    { framework: 'svelte', variable: 'Svelte' },
    { framework: 'solid', variable: 'Solid' },
    { framework: 'lit', variable: 'LitElement' }
  ]

  // 文件扩展名检测规则
  private fileExtensionRules: FileExtensionRule[] = [
    { framework: 'react', extensions: ['.jsx', '.tsx'], weight: 0.8 },
    { framework: 'vue3', extensions: ['.vue'], weight: 0.9 },
    { framework: 'vue2', extensions: ['.vue'], weight: 0.7 },
    { framework: 'angular', extensions: ['.component.ts', '.module.ts'], weight: 0.9 },
    { framework: 'svelte', extensions: ['.svelte'], weight: 0.9 },
    { framework: 'solid', extensions: ['.jsx', '.tsx'], weight: 0.6 },
    { framework: 'lit', extensions: ['.lit.ts', '.lit.js'], weight: 0.8 }
  ]

  // Package.json 检测规则
  private packageJsonRules: PackageJsonRule[] = [
    { framework: 'react', dependencies: ['react', 'react-dom'], weight: 0.9 },
    { framework: 'vue3', dependencies: ['vue'], devDependencies: ['@vue/cli-service'], weight: 0.8 },
    { framework: 'vue2', dependencies: ['vue'], weight: 0.7 },
    { framework: 'angular', dependencies: ['@angular/core'], weight: 0.9 },
    { framework: 'svelte', dependencies: ['svelte'], weight: 0.9 },
    { framework: 'solid', dependencies: ['solid-js'], weight: 0.9 },
    { framework: 'lit', dependencies: ['lit', 'lit-element'], weight: 0.8 }
  ]

  constructor(config: DetectorConfig = {}) {
    this.config = {
      strategies: ['global-variable', 'package-json', 'file-extension'],
      timeout: 5000,
      cache: true,
      cacheExpiry: 300000, // 5分钟
      ...config
    }
  }

  /**
   * 检测框架类型
   */
  detect(entry: string): FrameworkType {
    const cacheKey = this.getCacheKey(entry)
    
    // 检查缓存
    if (this.config.cache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      if (Date.now() - cached.detectionTime < this.config.cacheExpiry!) {
        return cached.framework
      }
    }

    const result = this.performDetection(entry)
    
    // 缓存结果
    if (this.config.cache) {
      this.cache.set(cacheKey, result)
    }

    return result.framework
  }

  /**
   * 获取详细的检测结果
   */
  detectWithDetails(entry: string): FrameworkDetectionResult {
    const cacheKey = this.getCacheKey(entry)
    
    // 检查缓存
    if (this.config.cache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      if (Date.now() - cached.detectionTime < this.config.cacheExpiry!) {
        return cached
      }
    }

    const result = this.performDetection(entry)
    
    // 缓存结果
    if (this.config.cache) {
      this.cache.set(cacheKey, result)
    }

    return result
  }

  /**
   * 执行检测
   */
  private performDetection(entry: string): FrameworkDetectionResult {
    const detectionTime = Date.now()
    const results: Array<{ framework: FrameworkType; confidence: number; strategy: DetectionStrategy }> = []

    // 执行各种检测策略
    for (const strategy of this.config.strategies!) {
      try {
        const result = this.executeStrategy(strategy, entry)
        if (result) {
          results.push({ ...result, strategy })
        }
      } catch (error) {
        console.warn(`检测策略 ${strategy} 执行失败:`, error)
      }
    }

    // 选择置信度最高的结果
    const bestResult = results.reduce((best, current) => 
      current.confidence > best.confidence ? current : best,
      { framework: 'vanilla' as FrameworkType, confidence: 0, strategy: 'manual' as DetectionStrategy }
    )

    const info: FrameworkInfo = {
      type: bestResult.framework,
      version: this.getFrameworkVersion(bestResult.framework),
      name: this.getFrameworkName(bestResult.framework),
      supported: true,
      confidence: bestResult.confidence
    }

    return {
      framework: bestResult.framework,
      info,
      strategy: bestResult.strategy,
      detectionTime
    }
  }

  /**
   * 执行检测策略
   */
  private executeStrategy(strategy: DetectionStrategy, entry: string): { framework: FrameworkType; confidence: number } | null {
    switch (strategy) {
      case 'global-variable':
        return this.detectByGlobalVariable()
      case 'package-json':
        return this.detectByPackageJson(entry)
      case 'file-extension':
        return this.detectByFileExtension(entry)
      case 'dom-analysis':
        return this.detectByDomAnalysis()
      case 'script-analysis':
        return this.detectByScriptAnalysis(entry)
      default:
        return null
    }
  }

  /**
   * 通过全局变量检测
   */
  private detectByGlobalVariable(): { framework: FrameworkType; confidence: number } | null {
    for (const rule of this.globalVariableRules) {
      const globalVar = (window as any)[rule.variable]
      if (globalVar) {
        if (rule.detector && !rule.detector(globalVar)) {
          continue
        }
        return { framework: rule.framework, confidence: 0.9 }
      }
    }
    return null
  }

  /**
   * 通过 package.json 检测
   */
  private detectByPackageJson(entry: string): { framework: FrameworkType; confidence: number } | null {
    // 这里需要实际的 package.json 解析逻辑
    // 简化实现，实际应该解析 package.json 文件
    return null
  }

  /**
   * 通过文件扩展名检测
   */
  private detectByFileExtension(entry: string): { framework: FrameworkType; confidence: number } | null {
    for (const rule of this.fileExtensionRules) {
      for (const ext of rule.extensions) {
        if (entry.includes(ext)) {
          return { framework: rule.framework, confidence: rule.weight }
        }
      }
    }
    return null
  }

  /**
   * 通过 DOM 分析检测
   */
  private detectByDomAnalysis(): { framework: FrameworkType; confidence: number } | null {
    // 分析 DOM 结构特征
    const body = document.body
    if (!body) return null

    // React 特征检测
    if (body.querySelector('[data-reactroot]') || body.querySelector('div[id="root"]')) {
      return { framework: 'react', confidence: 0.7 }
    }

    // Vue 特征检测
    if (body.querySelector('[data-v-]') || body.querySelector('div[id="app"]')) {
      return { framework: 'vue3', confidence: 0.6 }
    }

    // Angular 特征检测
    if (body.querySelector('[ng-version]') || body.querySelector('app-root')) {
      return { framework: 'angular', confidence: 0.8 }
    }

    return null
  }

  /**
   * 通过脚本分析检测
   */
  private detectByScriptAnalysis(entry: string): { framework: FrameworkType; confidence: number } | null {
    // 分析脚本内容特征
    // 这里需要实际的脚本内容分析逻辑
    return null
  }

  /**
   * 获取框架版本
   */
  private getFrameworkVersion(framework: FrameworkType): string {
    const globalVar = this.getGlobalVariable(framework)
    if (globalVar) {
      return globalVar.version || 'unknown'
    }
    return 'unknown'
  }

  /**
   * 获取框架名称
   */
  private getFrameworkName(framework: FrameworkType): string {
    const names: Record<FrameworkType, string> = {
      react: 'React',
      vue2: 'Vue 2',
      vue3: 'Vue 3',
      angular: 'Angular',
      svelte: 'Svelte',
      solid: 'Solid',
      lit: 'Lit',
      html: 'HTML',
      vanilla: 'Vanilla JavaScript'
    }
    return names[framework]
  }

  /**
   * 获取全局变量
   */
  private getGlobalVariable(framework: FrameworkType): any {
    const rule = this.globalVariableRules.find(r => r.framework === framework)
    return rule ? (window as any)[rule.variable] : null
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(entry: string): string {
    return `${entry}-${this.config.strategies?.join(',')}`
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear()
  }
}