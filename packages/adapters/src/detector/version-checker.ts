/**
 * 版本兼容性检查器
 * 
 * 检查框架版本是否兼容
 */

import type { VersionInfo, FrameworkType } from '../types'

export class VersionChecker {
  // 支持的框架版本范围
  private static supportedVersions: Record<FrameworkType, string[]> = {
    react: ['16.8.0', '17.x.x', '18.x.x'],
    vue2: ['2.6.0', '2.7.x'],
    vue3: ['3.0.0', '3.1.x', '3.2.x', '3.3.x', '3.4.x'],
    angular: ['12.x.x', '13.x.x', '14.x.x', '15.x.x', '16.x.x', '17.x.x'],
    svelte: ['3.x.x', '4.x.x'],
    solid: ['1.x.x'],
    lit: ['2.x.x', '3.x.x'],
    html: ['*'],
    vanilla: ['*']
  }

  /**
   * 解析版本字符串
   */
  static parseVersion(versionString: string): VersionInfo {
    const cleanVersion = versionString.replace(/^[^0-9]*/, '')
    const parts = cleanVersion.split(/[.-]/)
    
    const major = parseInt(parts[0]) || 0
    const minor = parseInt(parts[1]) || 0
    const patch = parseInt(parts[2]) || 0
    
    let prerelease: string | undefined
    let build: string | undefined
    
    // 检查预发布版本
    const prereleaseMatch = cleanVersion.match(/-(alpha|beta|rc|dev)\.?\d*/)
    if (prereleaseMatch) {
      prerelease = prereleaseMatch[1]
    }
    
    // 检查构建元数据
    const buildMatch = cleanVersion.match(/\+(.+)$/)
    if (buildMatch) {
      build = buildMatch[1]
    }

    return {
      major,
      minor,
      patch,
      prerelease,
      build,
      raw: versionString
    }
  }

  /**
   * 比较版本
   */
  static compareVersions(version1: string, version2: string): number {
    const v1 = this.parseVersion(version1)
    const v2 = this.parseVersion(version2)

    // 比较主版本号
    if (v1.major !== v2.major) {
      return v1.major - v2.major
    }

    // 比较次版本号
    if (v1.minor !== v2.minor) {
      return v1.minor - v2.minor
    }

    // 比较修订版本号
    if (v1.patch !== v2.patch) {
      return v1.patch - v2.patch
    }

    // 比较预发布版本
    if (v1.prerelease && !v2.prerelease) return -1
    if (!v1.prerelease && v2.prerelease) return 1
    if (v1.prerelease && v2.prerelease) {
      return v1.prerelease.localeCompare(v2.prerelease)
    }

    return 0
  }

  /**
   * 检查版本是否兼容
   */
  static isCompatible(framework: FrameworkType, version: string): boolean {
    const supportedVersions = this.supportedVersions[framework]
    if (!supportedVersions) return false

    // 通配符支持
    if (supportedVersions.includes('*')) return true

    const targetVersion = this.parseVersion(version)

    return supportedVersions.some(supportedVersion => {
      if (supportedVersion.includes('x')) {
        return this.matchesPattern(targetVersion, supportedVersion)
      } else {
        return this.compareVersions(version, supportedVersion) >= 0
      }
    })
  }

  /**
   * 匹配版本模式
   */
  private static matchesPattern(version: VersionInfo, pattern: string): boolean {
    const patternParts = pattern.split('.')
    
    if (patternParts[0] !== 'x' && parseInt(patternParts[0]) !== version.major) {
      return false
    }
    
    if (patternParts[1] !== 'x' && parseInt(patternParts[1]) !== version.minor) {
      return false
    }
    
    if (patternParts[2] !== 'x' && parseInt(patternParts[2]) !== version.patch) {
      return false
    }

    return true
  }

  /**
   * 获取推荐版本
   */
  static getRecommendedVersion(framework: FrameworkType): string {
    const recommendations: Record<FrameworkType, string> = {
      react: '18.2.0',
      vue2: '2.7.14',
      vue3: '3.4.0',
      angular: '17.0.0',
      svelte: '4.0.0',
      solid: '1.8.0',
      lit: '3.0.0',
      html: 'latest',
      vanilla: 'latest'
    }
    
    return recommendations[framework] || 'latest'
  }

  /**
   * 获取支持的版本列表
   */
  static getSupportedVersions(framework: FrameworkType): string[] {
    return this.supportedVersions[framework] || []
  }

  /**
   * 检查是否为最新版本
   */
  static isLatestVersion(framework: FrameworkType, version: string): boolean {
    const recommended = this.getRecommendedVersion(framework)
    if (recommended === 'latest') return true
    
    return this.compareVersions(version, recommended) >= 0
  }

  /**
   * 获取版本兼容性报告
   */
  static getCompatibilityReport(framework: FrameworkType, version: string): {
    compatible: boolean
    recommended: string
    isLatest: boolean
    supportedVersions: string[]
    warnings: string[]
  } {
    const compatible = this.isCompatible(framework, version)
    const recommended = this.getRecommendedVersion(framework)
    const isLatest = this.isLatestVersion(framework, version)
    const supportedVersions = this.getSupportedVersions(framework)
    const warnings: string[] = []

    if (!compatible) {
      warnings.push(`版本 ${version} 不兼容，请升级到支持的版本`)
    }

    if (!isLatest) {
      warnings.push(`当前版本不是最新版本，推荐升级到 ${recommended}`)
    }

    const parsedVersion = this.parseVersion(version)
    if (parsedVersion.prerelease) {
      warnings.push('当前使用的是预发布版本，可能存在稳定性问题')
    }

    return {
      compatible,
      recommended,
      isLatest,
      supportedVersions,
      warnings
    }
  }
}