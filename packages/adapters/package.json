{"name": "@micro-core/adapters", "version": "0.1.0", "description": "微前端框架适配器系统", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}}, "files": ["dist", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["micro-frontend", "adapter", "framework", "react", "vue", "angular", "svelte", "solid", "lit", "typescript"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*"}, "devDependencies": {"@types/node": "^20.10.5", "rimraf": "^5.0.5", "typescript": "^5.7.2", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}, "peerDependencies": {"@micro-core/core": "0.1.0", "@micro-core/shared": "0.1.0"}}