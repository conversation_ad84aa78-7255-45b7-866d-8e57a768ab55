#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules/vitest/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules/vitest/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/vitest@3.2.4_@types+node@20.19.9/node_modules/vitest/vitest.mjs" "$@"
fi
