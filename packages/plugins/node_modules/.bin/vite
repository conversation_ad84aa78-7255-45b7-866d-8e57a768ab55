#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/bin/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules:/Users/<USER>/Desktop/micro-core/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/vite@7.0.6_@types+node@20.19.9_terser@5.43.1/node_modules/vite/bin/vite.js" "$@"
fi
