/**
 * 插件系统主入口
 * 提供完整的插件化架构支持
 */

// 导出基础设施
export * from './base'

// 导出核心插件
export * from './core'

// 导出扩展插件
export * from './extensions'

// 导出插件市场
export * from './market'

// 导出类型定义
export * from './types'

// 默认导出
export { PluginBase } from './base/plugin-base'
export { PluginManager } from './base/manager'
export { pluginMarket, PluginMarket } from './market'

// 便捷导出
export {
  registerPlugin,
  installPlugin,
  uninstallPlugin,
  searchPlugins,
  validatePlugin
} from './market'