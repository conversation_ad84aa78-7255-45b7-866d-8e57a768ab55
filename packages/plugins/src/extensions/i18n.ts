import type { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import { logger } from '@micro-core/shared'

/**
 * 翻译资源接口
 */
export interface TranslationResource {
  [key: string]: string | TranslationResource
}

/**
 * 语言配置接口
 */
export interface LanguageConfig {
  /** 语言代码 */
  code: string
  /** 语言名称 */
  name: string
  /** 翻译资源 */
  resources: TranslationResource
  /** 是否为默认语言 */
  default?: boolean
}

/**
 * 国际化插件选项
 */
export interface I18nPluginOptions {
  /** 默认语言 */
  defaultLanguage?: string
  /** 语言列表 */
  languages?: LanguageConfig[]
  /** 回退语言 */
  fallbackLanguage?: string
  /** 存储键名 */
  storageKey?: string
  /** 是否启用调试模式 */
  debug?: boolean
  /** 插值分隔符 */
  interpolation?: {
    prefix?: string
    suffix?: string
  }
}

/**
 * 翻译函数类型
 */
export type TranslateFunction = (key: string, params?: Record<string, any>) => string

/**
 * 国际化插件
 * 提供多语言支持和翻译功能
 */
export class I18nPlugin implements PluginBase {
  public readonly name = 'i18n'
  public readonly version = '0.1.0'

  private core?: MicroCore
  private options: Required<I18nPluginOptions>
  private languages = new Map<string, LanguageConfig>()
  private currentLanguage?: string
  private translations = new Map<string, any>()

  constructor(options: I18nPluginOptions = {}) {
    this.options = {
      defaultLanguage: 'en',
      languages: [],
      fallbackLanguage: 'en',
      storageKey: 'micro-core-language',
      debug: false,
      interpolation: {
        prefix: '{{',
        suffix: '}}'
      },
      ...options
    }
  }

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.initializeLanguages()
    this.loadStoredLanguage()
    this.setLanguage(this.currentLanguage || this.options.defaultLanguage)

    // 注册全局翻译函数
    if (typeof window !== 'undefined') {
      (window as any).__MICRO_CORE_T__ = this.translate.bind(this)
    }

    logger.info(`[I18nPlugin] 国际化插件已安装，当前语言: ${this.currentLanguage}`)
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.languages.clear()
    this.translations.clear()
    this.currentLanguage = undefined
    this.core = undefined

    // 清理全局翻译函数
    if (typeof window !== 'undefined') {
      delete (window as any).__MICRO_CORE_T__
    }

    logger.info('[I18nPlugin] 国际化插件已卸载')
  }

  /**
   * 注册语言
   */
  public registerLanguage(language: LanguageConfig): void {
    this.languages.set(language.code, language)
    this.translations.set(language.code, this.flattenTranslations(language.resources))
    
    if (language.default && !this.currentLanguage) {
      this.currentLanguage = language.code
    }

    logger.debug(`[I18nPlugin] 注册语言: ${language.code} (${language.name})`)
  }

  /**
   * 获取语言配置
   */
  public getLanguage(code: string): LanguageConfig | undefined {
    return this.languages.get(code)
  }

  /**
   * 获取所有语言
   */
  public getAllLanguages(): LanguageConfig[] {
    return Array.from(this.languages.values())
  }

  /**
   * 设置当前语言
   */
  public setLanguage(code: string): void {
    const language = this.languages.get(code)
    if (!language) {
      logger.warn(`[I18nPlugin] 语言不存在: ${code}`)
      return
    }

    const previousLanguage = this.currentLanguage
    this.currentLanguage = code
    this.saveLanguage(code)

    // 触发语言切换事件
    this.core?.emit?.('language:changed', {
      from: previousLanguage,
      to: code,
      language
    })

    logger.info(`[I18nPlugin] 切换到语言: ${code} (${language.name})`)
  }

  /**
   * 获取当前语言
   */
  public getCurrentLanguage(): string | undefined {
    return this.currentLanguage
  }

  /**
   * 翻译函数
   */
  public translate(key: string, params?: Record<string, any>): string {
    if (!this.currentLanguage) {
      return key
    }

    let translation = this.getTranslation(key, this.currentLanguage)
    
    // 如果当前语言没有翻译，尝试回退语言
    if (!translation && this.options.fallbackLanguage !== this.currentLanguage) {
      translation = this.getTranslation(key, this.options.fallbackLanguage)
    }

    // 如果仍然没有翻译，返回键名
    if (!translation) {
      if (this.options.debug) {
        logger.warn(`[I18nPlugin] 翻译缺失: ${key}`)
      }
      return key
    }

    // 处理参数插值
    if (params) {
      translation = this.interpolate(translation, params)
    }

    return translation
  }

  /**
   * 添加翻译资源
   */
  public addTranslations(languageCode: string, translations: TranslationResource): void {
    const existing = this.translations.get(languageCode) || {}
    const flattened = this.flattenTranslations(translations)
    
    this.translations.set(languageCode, {
      ...existing,
      ...flattened
    })

    logger.debug(`[I18nPlugin] 添加翻译资源: ${languageCode}`)
  }

  /**
   * 检测浏览器语言
   */
  public detectBrowserLanguage(): string | undefined {
    if (typeof navigator === 'undefined') {
      return undefined
    }

    const browserLanguages = [
      navigator.language,
      ...(navigator.languages || [])
    ]

    for (const browserLang of browserLanguages) {
      // 精确匹配
      if (this.languages.has(browserLang)) {
        return browserLang
      }

      // 语言代码匹配（如 en-US -> en）
      const langCode = browserLang.split('-')[0]
      if (langCode && this.languages.has(langCode)) {
        return langCode
      }
    }

    return undefined
  }

  /**
   * 初始化语言
   */
  private initializeLanguages(): void {
    // 注册用户提供的语言
    this.options.languages.forEach(language => {
      this.registerLanguage(language)
    })

    // 如果没有设置默认语言，尝试检测浏览器语言
    if (!this.currentLanguage) {
      const detected = this.detectBrowserLanguage()
      if (detected) {
        this.currentLanguage = detected
      }
    }
  }

  /**
   * 获取翻译
   */
  private getTranslation(key: string, languageCode: string): string | undefined {
    const translations = this.translations.get(languageCode)
    if (!translations) {
      return undefined
    }

    return translations[key]
  }

  /**
   * 扁平化翻译对象
   */
  private flattenTranslations(obj: TranslationResource, prefix = ''): Record<string, string> {
    const result: Record<string, string> = {}

    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key

      if (typeof value === 'string') {
        result[fullKey] = value
      } else if (typeof value === 'object' && value !== null) {
        Object.assign(result, this.flattenTranslations(value, fullKey))
      }
    }

    return result
  }

  /**
   * 参数插值
   */
  private interpolate(template: string, params: Record<string, any>): string {
    const { prefix, suffix } = this.options.interpolation
    
    return template.replace(
      new RegExp(`${this.escapeRegExp(prefix)}([^${this.escapeRegExp(suffix)}]+)${this.escapeRegExp(suffix)}`, 'g'),
      (match, key) => {
        const value = params[key.trim()]
        return value !== undefined ? String(value) : match
      }
    )
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  /**
   * 加载存储的语言
   */
  private loadStoredLanguage(): void {
    try {
      const stored = localStorage.getItem(this.options.storageKey)
      if (stored && this.languages.has(stored)) {
        this.currentLanguage = stored
      }
    } catch (error) {
      logger.warn('[I18nPlugin] 加载存储语言失败:', error)
    }
  }

  /**
   * 保存语言到存储
   */
  private saveLanguage(code: string): void {
    try {
      localStorage.setItem(this.options.storageKey, code)
    } catch (error) {
      logger.warn('[I18nPlugin] 保存语言失败:', error)
    }
  }
}

/**
 * 创建国际化插件实例
 */
export function createI18nPlugin(options?: I18nPluginOptions): I18nPlugin {
  return new I18nPlugin(options)
}

/**
 * 全局翻译函数
 */
export function t(key: string, params?: Record<string, any>): string {
  if (typeof window !== 'undefined' && (window as any).__MICRO_CORE_T__) {
    return (window as any).__MICRO_CORE_T__(key, params)
  }
  return key
}

export default I18nPlugin