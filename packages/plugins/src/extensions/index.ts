/**
 * 扩展插件模块
 * 提供主题、国际化、分析、缓存等扩展功能
 */

// 主题插件
export { ThemePlugin, createThemePlugin } from './theme'
export type { ThemeConfig, ThemePluginOptions } from './theme'

// 国际化插件
export { I18nPlugin, createI18nPlugin, t } from './i18n'
export type { 
  TranslationResource, 
  LanguageConfig, 
  I18nPluginOptions, 
  TranslateFunction 
} from './i18n'

// 分析插件
export { AnalyticsPlugin, createAnalyticsPlugin, ConsoleAnalyticsProvider } from './analytics'
export type { 
  AnalyticsEvent, 
  AnalyticsProvider, 
  AnalyticsPluginOptions 
} from './analytics'

// 缓存插件
export { 
  CachePlugin, 
  createCachePlugin, 
  MemoryCacheStorage, 
  LocalStorageCacheStorage,
  CacheStrategy 
} from './cache'
export type { 
  CacheItem, 
  CacheStorage, 
  CachePluginOptions, 
  CacheStats 
} from './cache'

/**
 * 所有扩展插件的集合
 */
export const extensionPlugins = {
  ThemePlugin,
  I18nPlugin,
  AnalyticsPlugin,
  CachePlugin
} as const

/**
 * 扩展插件创建函数集合
 */
export const createExtensionPlugins = {
  theme: createThemePlugin,
  i18n: createI18nPlugin,
  analytics: createAnalyticsPlugin,
  cache: createCachePlugin
} as const