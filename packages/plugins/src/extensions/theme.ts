import type { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import { logger } from '@micro-core/shared'

/**
 * 主题配置接口
 */
export interface ThemeConfig {
  /** 主题名称 */
  name: string
  /** 主题变量 */
  variables: Record<string, string>
  /** CSS 样式 */
  styles?: string
  /** 是否为暗色主题 */
  dark?: boolean
}

/**
 * 主题插件选项
 */
export interface ThemePluginOptions {
  /** 默认主题 */
  defaultTheme?: string
  /** 主题列表 */
  themes?: ThemeConfig[]
  /** 是否启用自动切换 */
  autoSwitch?: boolean
  /** 存储键名 */
  storageKey?: string
}

/**
 * 主题插件
 * 提供主题切换和管理功能
 */
export class ThemePlugin implements PluginBase {
  public readonly name = 'theme'
  public readonly version = '0.1.0'

  private core?: MicroCore
  private options: Required<ThemePluginOptions>
  private themes = new Map<string, ThemeConfig>()
  private currentTheme?: string
  private styleElement?: HTMLStyleElement

  constructor(options: ThemePluginOptions = {}) {
    this.options = {
      defaultTheme: 'light',
      themes: [],
      autoSwitch: false,
      storageKey: 'micro-core-theme',
      ...options
    }
  }

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.initializeThemes()
    this.loadStoredTheme()
    this.applyTheme(this.currentTheme || this.options.defaultTheme)
    
    if (this.options.autoSwitch) {
      this.setupAutoSwitch()
    }

    logger.info(`[ThemePlugin] 主题插件已安装，当前主题: ${this.currentTheme}`)
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.removeStyleElement()
    this.themes.clear()
    this.currentTheme = undefined
    this.core = undefined
    
    logger.info('[ThemePlugin] 主题插件已卸载')
  }

  /**
   * 注册主题
   */
  public registerTheme(theme: ThemeConfig): void {
    this.themes.set(theme.name, theme)
    logger.debug(`[ThemePlugin] 注册主题: ${theme.name}`)
  }

  /**
   * 获取主题
   */
  public getTheme(name: string): ThemeConfig | undefined {
    return this.themes.get(name)
  }

  /**
   * 获取所有主题
   */
  public getAllThemes(): ThemeConfig[] {
    return Array.from(this.themes.values())
  }

  /**
   * 切换主题
   */
  public switchTheme(name: string): void {
    const theme = this.themes.get(name)
    if (!theme) {
      logger.warn(`[ThemePlugin] 主题不存在: ${name}`)
      return
    }

    this.applyTheme(name)
    this.saveTheme(name)
    
    // 触发主题切换事件
    this.core?.emit?.('theme:changed', { 
      from: this.currentTheme, 
      to: name,
      theme 
    })

    this.currentTheme = name
    logger.info(`[ThemePlugin] 切换到主题: ${name}`)
  }

  /**
   * 获取当前主题
   */
  public getCurrentTheme(): string | undefined {
    return this.currentTheme
  }

  /**
   * 初始化主题
   */
  private initializeThemes(): void {
    // 注册默认主题
    const defaultThemes: ThemeConfig[] = [
      {
        name: 'light',
        variables: {
          '--primary-color': '#1890ff',
          '--background-color': '#ffffff',
          '--text-color': '#000000',
          '--border-color': '#d9d9d9'
        },
        dark: false
      },
      {
        name: 'dark',
        variables: {
          '--primary-color': '#177ddc',
          '--background-color': '#141414',
          '--text-color': '#ffffff',
          '--border-color': '#434343'
        },
        dark: true
      }
    ]

    // 注册默认主题
    defaultThemes.forEach(theme => this.registerTheme(theme))

    // 注册用户自定义主题
    this.options.themes.forEach(theme => this.registerTheme(theme))
  }

  /**
   * 应用主题
   */
  private applyTheme(name: string): void {
    const theme = this.themes.get(name)
    if (!theme) {
      return
    }

    // 移除旧的样式元素
    this.removeStyleElement()

    // 创建新的样式元素
    this.styleElement = document.createElement('style')
    this.styleElement.setAttribute('data-theme', name)
    
    // 生成 CSS 变量
    const cssVariables = Object.entries(theme.variables)
      .map(([key, value]) => `  ${key}: ${value};`)
      .join('\n')

    // 生成完整的 CSS
    let css = `:root {\n${cssVariables}\n}`
    
    if (theme.styles) {
      css += `\n${theme.styles}`
    }

    this.styleElement.textContent = css
    document.head.appendChild(this.styleElement)

    // 设置 body 的主题类名
    document.body.setAttribute('data-theme', name)
    if (theme.dark) {
      document.body.classList.add('dark-theme')
    } else {
      document.body.classList.remove('dark-theme')
    }
  }

  /**
   * 移除样式元素
   */
  private removeStyleElement(): void {
    if (this.styleElement) {
      this.styleElement.remove()
      this.styleElement = undefined
    }
  }

  /**
   * 加载存储的主题
   */
  private loadStoredTheme(): void {
    try {
      const stored = localStorage.getItem(this.options.storageKey)
      if (stored && this.themes.has(stored)) {
        this.currentTheme = stored
      }
    } catch (error) {
      logger.warn('[ThemePlugin] 加载存储主题失败:', error)
    }
  }

  /**
   * 保存主题到存储
   */
  private saveTheme(name: string): void {
    try {
      localStorage.setItem(this.options.storageKey, name)
    } catch (error) {
      logger.warn('[ThemePlugin] 保存主题失败:', error)
    }
  }

  /**
   * 设置自动切换
   */
  private setupAutoSwitch(): void {
    // 监听系统主题变化
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleChange = (e: MediaQueryListEvent) => {
        const themeName = e.matches ? 'dark' : 'light'
        if (this.themes.has(themeName)) {
          this.switchTheme(themeName)
        }
      }

      mediaQuery.addEventListener('change', handleChange)
      
      // 初始检查
      const isDark = mediaQuery.matches
      const initialTheme = isDark ? 'dark' : 'light'
      if (this.themes.has(initialTheme) && !this.currentTheme) {
        this.currentTheme = initialTheme
      }
    }
  }
}

/**
 * 创建主题插件实例
 */
export function createThemePlugin(options?: ThemePluginOptions): ThemePlugin {
  return new ThemePlugin(options)
}

export default ThemePlugin