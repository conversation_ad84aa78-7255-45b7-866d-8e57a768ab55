import type { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import { logger } from '@micro-core/shared'

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /** 缓存值 */
  value: T
  /** 过期时间 */
  expireTime?: number
  /** 创建时间 */
  createTime: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccessTime: number
  /** 缓存大小（字节） */
  size?: number
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  /** 最近最少使用 */
  LRU = 'lru',
  /** 最不经常使用 */
  LFU = 'lfu',
  /** 先进先出 */
  FIFO = 'fifo',
  /** 生存时间 */
  TTL = 'ttl'
}

/**
 * 缓存存储接口
 */
export interface CacheStorage {
  /** 存储名称 */
  name: string
  /** 获取缓存项 */
  get<T>(key: string): Promise<CacheItem<T> | null>
  /** 设置缓存项 */
  set<T>(key: string, item: CacheItem<T>): Promise<void>
  /** 删除缓存项 */
  delete(key: string): Promise<boolean>
  /** 清空缓存 */
  clear(): Promise<void>
  /** 获取所有键 */
  keys(): Promise<string[]>
  /** 获取缓存大小 */
  size(): Promise<number>
}

/**
 * 内存缓存存储
 */
export class MemoryCacheStorage implements CacheStorage {
  public readonly name = 'memory'
  private cache = new Map<string, CacheItem>()

  public async get<T>(key: string): Promise<CacheItem<T> | null> {
    return this.cache.get(key) as CacheItem<T> || null
  }

  public async set<T>(key: string, item: CacheItem<T>): Promise<void> {
    this.cache.set(key, item)
  }

  public async delete(key: string): Promise<boolean> {
    return this.cache.delete(key)
  }

  public async clear(): Promise<void> {
    this.cache.clear()
  }

  public async keys(): Promise<string[]> {
    return Array.from(this.cache.keys())
  }

  public async size(): Promise<number> {
    return this.cache.size
  }
}

/**
 * LocalStorage 缓存存储
 */
export class LocalStorageCacheStorage implements CacheStorage {
  public readonly name = 'localStorage'
  private prefix: string

  constructor(prefix = 'micro-core-cache:') {
    this.prefix = prefix
  }

  public async get<T>(key: string): Promise<CacheItem<T> | null> {
    try {
      const item = localStorage.getItem(this.prefix + key)
      return item ? JSON.parse(item) : null
    } catch {
      return null
    }
  }

  public async set<T>(key: string, item: CacheItem<T>): Promise<void> {
    try {
      localStorage.setItem(this.prefix + key, JSON.stringify(item))
    } catch (error) {
      logger.warn('[LocalStorageCacheStorage] 设置缓存失败:', error)
    }
  }

  public async delete(key: string): Promise<boolean> {
    try {
      localStorage.removeItem(this.prefix + key)
      return true
    } catch {
      return false
    }
  }

  public async clear(): Promise<void> {
    const keys = await this.keys()
    for (const key of keys) {
      await this.delete(key.replace(this.prefix, ''))
    }
  }

  public async keys(): Promise<string[]> {
    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith(this.prefix)) {
        keys.push(key)
      }
    }
    return keys
  }

  public async size(): Promise<number> {
    return (await this.keys()).length
  }
}

/**
 * 缓存插件选项
 */
export interface CachePluginOptions {
  /** 缓存策略 */
  strategy?: CacheStrategy
  /** 最大缓存项数量 */
  maxItems?: number
  /** 最大缓存大小（字节） */
  maxSize?: number
  /** 默认过期时间（毫秒） */
  defaultTTL?: number
  /** 缓存存储 */
  storage?: CacheStorage
  /** 是否启用统计 */
  enableStats?: boolean
  /** 清理间隔（毫秒） */
  cleanupInterval?: number
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 命中次数 */
  hits: number
  /** 未命中次数 */
  misses: number
  /** 命中率 */
  hitRate: number
  /** 缓存项数量 */
  itemCount: number
  /** 缓存大小 */
  size: number
}

/**
 * 缓存插件
 * 提供多种缓存策略和存储方式
 */
export class CachePlugin implements PluginBase {
  public readonly name = 'cache'
  public readonly version = '0.1.0'

  private core?: MicroCore
  private options: Required<CachePluginOptions>
  private storage: CacheStorage
  private stats: CacheStats
  private cleanupTimer?: NodeJS.Timeout

  constructor(options: CachePluginOptions = {}) {
    this.options = {
      strategy: CacheStrategy.LRU,
      maxItems: 1000,
      maxSize: 50 * 1024 * 1024, // 50MB
      defaultTTL: 5 * 60 * 1000, // 5分钟
      storage: new MemoryCacheStorage(),
      enableStats: true,
      cleanupInterval: 60 * 1000, // 1分钟
      ...options
    }

    this.storage = this.options.storage
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      itemCount: 0,
      size: 0
    }
  }

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.startCleanupTimer()

    logger.info(`[CachePlugin] 缓存插件已安装，策略: ${this.options.strategy}`)
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.stopCleanupTimer()
    this.core = undefined

    logger.info('[CachePlugin] 缓存插件已卸载')
  }

  /**
   * 获取缓存
   */
  public async get<T>(key: string): Promise<T | null> {
    const item = await this.storage.get<T>(key)
    
    if (!item) {
      this.updateStats('miss')
      return null
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      await this.storage.delete(key)
      this.updateStats('miss')
      return null
    }

    // 更新访问信息
    item.accessCount++
    item.lastAccessTime = Date.now()
    await this.storage.set(key, item)

    this.updateStats('hit')
    return item.value
  }

  /**
   * 设置缓存
   */
  public async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const now = Date.now()
    const expireTime = ttl ? now + ttl : (this.options.defaultTTL ? now + this.options.defaultTTL : undefined)
    
    const item: CacheItem<T> = {
      value,
      expireTime,
      createTime: now,
      accessCount: 0,
      lastAccessTime: now,
      size: this.calculateSize(value)
    }

    // 检查是否需要清理空间
    await this.ensureSpace(item.size || 0)

    await this.storage.set(key, item)
    await this.updateCacheStats()

    logger.debug(`[CachePlugin] 设置缓存: ${key}`)
  }

  /**
   * 删除缓存
   */
  public async delete(key: string): Promise<boolean> {
    const result = await this.storage.delete(key)
    if (result) {
      await this.updateCacheStats()
    }
    return result
  }

  /**
   * 清空缓存
   */
  public async clear(): Promise<void> {
    await this.storage.clear()
    this.stats.itemCount = 0
    this.stats.size = 0
    logger.info('[CachePlugin] 缓存已清空')
  }

  /**
   * 检查缓存是否存在
   */
  public async has(key: string): Promise<boolean> {
    const item = await this.storage.get(key)
    return item !== null && !this.isExpired(item)
  }

  /**
   * 获取所有缓存键
   */
  public async keys(): Promise<string[]> {
    return await this.storage.keys()
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * 清理过期缓存
   */
  public async cleanup(): Promise<number> {
    const keys = await this.storage.keys()
    let cleanedCount = 0

    for (const key of keys) {
      const item = await this.storage.get(key)
      if (item && this.isExpired(item)) {
        await this.storage.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      await this.updateCacheStats()
      logger.debug(`[CachePlugin] 清理过期缓存: ${cleanedCount} 项`)
    }

    return cleanedCount
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return item.expireTime ? Date.now() > item.expireTime : false
  }

  /**
   * 确保有足够的缓存空间
   */
  private async ensureSpace(requiredSize: number): Promise<void> {
    const currentSize = await this.storage.size()
    
    // 检查项数限制
    if (currentSize >= this.options.maxItems) {
      await this.evictItems(Math.ceil(this.options.maxItems * 0.1)) // 清理10%
    }

    // 检查大小限制
    if (this.stats.size + requiredSize > this.options.maxSize) {
      await this.evictBySize(requiredSize)
    }
  }

  /**
   * 根据策略清理缓存项
   */
  private async evictItems(count: number): Promise<void> {
    const keys = await this.storage.keys()
    const items: Array<{ key: string; item: CacheItem }> = []

    // 获取所有缓存项
    for (const key of keys) {
      const item = await this.storage.get(key)
      if (item) {
        items.push({ key, item })
      }
    }

    // 根据策略排序
    items.sort((a, b) => {
      switch (this.options.strategy) {
        case CacheStrategy.LRU:
          return a.item.lastAccessTime - b.item.lastAccessTime
        case CacheStrategy.LFU:
          return a.item.accessCount - b.item.accessCount
        case CacheStrategy.FIFO:
          return a.item.createTime - b.item.createTime
        case CacheStrategy.TTL:
          return (a.item.expireTime || Infinity) - (b.item.expireTime || Infinity)
        default:
          return 0
      }
    })

    // 删除指定数量的项
    for (let i = 0; i < Math.min(count, items.length); i++) {
      await this.storage.delete(items[i]!.key)
    }
  }

  /**
   * 根据大小清理缓存
   */
  private async evictBySize(requiredSize: number): Promise<void> {
    let freedSize = 0
    const keys = await this.storage.keys()

    for (const key of keys) {
      if (freedSize >= requiredSize) {
        break
      }

      const item = await this.storage.get(key)
      if (item) {
        freedSize += item.size || 0
        await this.storage.delete(key)
      }
    }
  }

  /**
   * 计算值的大小
   */
  private calculateSize(value: any): number {
    try {
      return JSON.stringify(value).length * 2 // 粗略估算（UTF-16）
    } catch {
      return 0
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(type: 'hit' | 'miss'): void {
    if (!this.options.enableStats) {
      return
    }

    if (type === 'hit') {
      this.stats.hits++
    } else {
      this.stats.misses++
    }

    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0
  }

  /**
   * 更新缓存统计信息
   */
  private async updateCacheStats(): Promise<void> {
    if (!this.options.enableStats) {
      return
    }

    this.stats.itemCount = await this.storage.size()
    
    // 计算总大小
    let totalSize = 0
    const keys = await this.storage.keys()
    for (const key of keys) {
      const item = await this.storage.get(key)
      if (item) {
        totalSize += item.size || 0
      }
    }
    this.stats.size = totalSize
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.options.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }
}

/**
 * 创建缓存插件实例
 */
export function createCachePlugin(options?: CachePluginOptions): CachePlugin {
  return new CachePlugin(options)
}

export default CachePlugin