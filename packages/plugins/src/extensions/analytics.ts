import type { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import { logger } from '@micro-core/shared'

/**
 * 分析事件接口
 */
export interface AnalyticsEvent {
  /** 事件名称 */
  name: string
  /** 事件属性 */
  properties?: Record<string, any>
  /** 事件时间戳 */
  timestamp?: number
  /** 用户ID */
  userId?: string
  /** 会话ID */
  sessionId?: string
}

/**
 * 分析提供者接口
 */
export interface AnalyticsProvider {
  /** 提供者名称 */
  name: string
  /** 初始化 */
  initialize(config: any): Promise<void>
  /** 跟踪事件 */
  track(event: AnalyticsEvent): Promise<void>
  /** 设置用户属性 */
  setUser(userId: string, properties?: Record<string, any>): Promise<void>
  /** 清理 */
  cleanup(): Promise<void>
}

/**
 * 分析插件选项
 */
export interface AnalyticsPluginOptions {
  /** 是否启用 */
  enabled?: boolean
  /** 分析提供者列表 */
  providers?: AnalyticsProvider[]
  /** 是否自动跟踪页面浏览 */
  autoTrackPageViews?: boolean
  /** 是否自动跟踪错误 */
  autoTrackErrors?: boolean
  /** 是否自动跟踪性能 */
  autoTrackPerformance?: boolean
  /** 采样率 (0-1) */
  sampleRate?: number
  /** 批量发送配置 */
  batch?: {
    enabled: boolean
    size: number
    timeout: number
  }
  /** 调试模式 */
  debug?: boolean
}

/**
 * 内置 Console 分析提供者
 */
export class ConsoleAnalyticsProvider implements AnalyticsProvider {
  public readonly name = 'console'

  public async initialize(): Promise<void> {
    logger.info('[ConsoleAnalyticsProvider] 初始化完成')
  }

  public async track(event: AnalyticsEvent): Promise<void> {
    console.log('[Analytics]', event)
  }

  public async setUser(userId: string, properties?: Record<string, any>): Promise<void> {
    console.log('[Analytics] User:', { userId, properties })
  }

  public async cleanup(): Promise<void> {
    logger.info('[ConsoleAnalyticsProvider] 清理完成')
  }
}

/**
 * 分析插件
 * 提供用户行为分析和数据收集功能
 */
export class AnalyticsPlugin implements PluginBase {
  public readonly name = 'analytics'
  public readonly version = '0.1.0'

  private core?: MicroCore
  private options: Required<AnalyticsPluginOptions>
  private providers = new Map<string, AnalyticsProvider>()
  private eventQueue: AnalyticsEvent[] = []
  private batchTimer?: NodeJS.Timeout
  private sessionId: string
  private userId?: string

  constructor(options: AnalyticsPluginOptions = {}) {
    this.options = {
      enabled: true,
      providers: [new ConsoleAnalyticsProvider()],
      autoTrackPageViews: true,
      autoTrackErrors: true,
      autoTrackPerformance: false,
      sampleRate: 1.0,
      batch: {
        enabled: false,
        size: 10,
        timeout: 5000
      },
      debug: false,
      ...options
    }

    this.sessionId = this.generateSessionId()
  }

  /**
   * 安装插件
   */
  public async install(core: MicroCore): Promise<void> {
    if (!this.options.enabled) {
      return
    }

    this.core = core

    // 初始化分析提供者
    await this.initializeProviders()

    // 设置自动跟踪
    this.setupAutoTracking()

    // 启动批量发送
    if (this.options.batch.enabled) {
      this.startBatchTimer()
    }

    logger.info(`[AnalyticsPlugin] 分析插件已安装，提供者数量: ${this.providers.size}`)
  }

  /**
   * 卸载插件
   */
  public async uninstall(): Promise<void> {
    // 发送剩余事件
    await this.flush()

    // 清理批量定时器
    if (this.batchTimer) {
      clearTimeout(this.batchTimer)
      this.batchTimer = undefined
    }

    // 清理提供者
    for (const provider of this.providers.values()) {
      try {
        await provider.cleanup()
      } catch (error) {
        logger.error(`[AnalyticsPlugin] 清理提供者失败: ${provider.name}`, error)
      }
    }

    this.providers.clear()
    this.eventQueue = []
    this.core = undefined

    logger.info('[AnalyticsPlugin] 分析插件已卸载')
  }

  /**
   * 注册分析提供者
   */
  public async registerProvider(provider: AnalyticsProvider): Promise<void> {
    try {
      await provider.initialize({})
      this.providers.set(provider.name, provider)
      logger.debug(`[AnalyticsPlugin] 注册提供者: ${provider.name}`)
    } catch (error) {
      logger.error(`[AnalyticsPlugin] 注册提供者失败: ${provider.name}`, error)
    }
  }

  /**
   * 跟踪事件
   */
  public async track(name: string, properties?: Record<string, any>): Promise<void> {
    if (!this.options.enabled || !this.shouldSample()) {
      return
    }

    const event: AnalyticsEvent = {
      name,
      properties,
      timestamp: Date.now(),
      userId: this.userId,
      sessionId: this.sessionId
    }

    if (this.options.debug) {
      logger.debug('[AnalyticsPlugin] 跟踪事件:', event)
    }

    if (this.options.batch.enabled) {
      this.eventQueue.push(event)
      
      if (this.eventQueue.length >= this.options.batch.size) {
        await this.flush()
      }
    } else {
      await this.sendEvent(event)
    }
  }

  /**
   * 设置用户
   */
  public async setUser(userId: string, properties?: Record<string, any>): Promise<void> {
    this.userId = userId

    for (const provider of this.providers.values()) {
      try {
        await provider.setUser(userId, properties)
      } catch (error) {
        logger.error(`[AnalyticsPlugin] 设置用户失败: ${provider.name}`, error)
      }
    }

    logger.debug(`[AnalyticsPlugin] 设置用户: ${userId}`)
  }

  /**
   * 跟踪页面浏览
   */
  public async trackPageView(path: string, title?: string): Promise<void> {
    await this.track('page_view', {
      path,
      title,
      referrer: document.referrer,
      url: window.location.href
    })
  }

  /**
   * 跟踪错误
   */
  public async trackError(error: Error, context?: Record<string, any>): Promise<void> {
    await this.track('error', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...context
    })
  }

  /**
   * 跟踪性能指标
   */
  public async trackPerformance(metrics: Record<string, number>): Promise<void> {
    await this.track('performance', metrics)
  }

  /**
   * 立即发送所有排队的事件
   */
  public async flush(): Promise<void> {
    if (this.eventQueue.length === 0) {
      return
    }

    const events = [...this.eventQueue]
    this.eventQueue = []

    for (const event of events) {
      await this.sendEvent(event)
    }
  }

  /**
   * 初始化分析提供者
   */
  private async initializeProviders(): Promise<void> {
    for (const provider of this.options.providers) {
      await this.registerProvider(provider)
    }
  }

  /**
   * 设置自动跟踪
   */
  private setupAutoTracking(): void {
    if (this.options.autoTrackPageViews) {
      this.setupPageViewTracking()
    }

    if (this.options.autoTrackErrors) {
      this.setupErrorTracking()
    }

    if (this.options.autoTrackPerformance) {
      this.setupPerformanceTracking()
    }
  }

  /**
   * 设置页面浏览跟踪
   */
  private setupPageViewTracking(): void {
    // 跟踪初始页面加载
    if (typeof window !== 'undefined') {
      this.trackPageView(window.location.pathname, document.title)

      // 监听路由变化（如果有路由系统）
      this.core?.on?.('route:changed', (event: any) => {
        this.trackPageView(event.path, event.title)
      })
    }
  }

  /**
   * 设置错误跟踪
   */
  private setupErrorTracking(): void {
    if (typeof window !== 'undefined') {
      // 监听全局错误
      window.addEventListener('error', (event) => {
        this.trackError(new Error(event.message), {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        })
      })

      // 监听未处理的 Promise 拒绝
      window.addEventListener('unhandledrejection', (event) => {
        this.trackError(new Error(event.reason), {
          type: 'unhandled_promise_rejection'
        })
      })
    }

    // 监听微前端错误
    this.core?.on?.('error', (error: Error) => {
      this.trackError(error, { source: 'micro-core' })
    })
  }

  /**
   * 设置性能跟踪
   */
  private setupPerformanceTracking(): void {
    if (typeof window !== 'undefined' && 'performance' in window) {
      // 监听页面加载性能
      window.addEventListener('load', () => {
        setTimeout(() => {
          const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
          if (navigation) {
            this.trackPerformance({
              dns_lookup: navigation.domainLookupEnd - navigation.domainLookupStart,
              tcp_connect: navigation.connectEnd - navigation.connectStart,
              request_response: navigation.responseEnd - navigation.requestStart,
              dom_parse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
              page_load: navigation.loadEventEnd - navigation.navigationStart
            })
          }
        }, 0)
      })

      // 监听资源加载性能
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resource = entry as PerformanceResourceTiming
            this.trackPerformance({
              resource_load_time: resource.responseEnd - resource.startTime,
              resource_name: resource.name,
              resource_type: resource.initiatorType
            })
          }
        }
      })

      observer.observe({ entryTypes: ['resource'] })
    }
  }

  /**
   * 发送单个事件
   */
  private async sendEvent(event: AnalyticsEvent): Promise<void> {
    for (const provider of this.providers.values()) {
      try {
        await provider.track(event)
      } catch (error) {
        logger.error(`[AnalyticsPlugin] 发送事件失败: ${provider.name}`, error)
      }
    }
  }

  /**
   * 启动批量发送定时器
   */
  private startBatchTimer(): void {
    this.batchTimer = setTimeout(async () => {
      await this.flush()
      this.startBatchTimer()
    }, this.options.batch.timeout)
  }

  /**
   * 判断是否应该采样
   */
  private shouldSample(): boolean {
    return Math.random() < this.options.sampleRate
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

/**
 * 创建分析插件实例
 */
export function createAnalyticsPlugin(options?: AnalyticsPluginOptions): AnalyticsPlugin {
  return new AnalyticsPlugin(options)
}

export default AnalyticsPlugin
