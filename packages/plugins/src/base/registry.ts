import type { Plugin, PluginConfig, PluginMetadata } from '../types/plugin'
import { MicroCoreError } from '@micro-core/shared'

/**
 * 插件注册表
 * 管理插件的注册、发现和元数据
 */
export class PluginRegistry {
  private plugins = new Map<string, Plugin>()
  private metadata = new Map<string, PluginMetadata>()
  private dependencies = new Map<string, string[]>()

  /**
   * 注册插件
   */
  register(plugin: Plugin, config?: PluginConfig): void {
    const { name, version } = plugin

    // 验证插件名称
    if (!name || typeof name !== 'string') {
      throw new MicroCoreError(
        'Plugin name is required and must be a string',
        'PLUGIN_INVALID_NAME'
      )
    }

    // 检查插件是否已注册
    if (this.plugins.has(name)) {
      throw new MicroCoreError(
        `Plugin "${name}" is already registered`,
        'PLUGIN_ALREADY_REGISTERED'
      )
    }

    // 验证插件版本
    if (!version || typeof version !== 'string') {
      throw new MicroCoreError(
        'Plugin version is required and must be a string',
        'PLUGIN_INVALID_VERSION'
      )
    }

    // 注册插件
    this.plugins.set(name, plugin)

    // 存储元数据
    const metadata: PluginMetadata = {
      name,
      version,
      description: config?.description || '',
      author: config?.author || '',
      dependencies: config?.dependencies || [],
      peerDependencies: config?.peerDependencies || [],
      registeredAt: new Date(),
      enabled: config?.enabled !== false
    }

    this.metadata.set(name, metadata)

    // 存储依赖关系
    if (config?.dependencies) {
      this.dependencies.set(name, config.dependencies)
    }
  }

  /**
   * 取消注册插件
   */
  unregister(name: string): boolean {
    if (!this.plugins.has(name)) {
      return false
    }

    this.plugins.delete(name)
    this.metadata.delete(name)
    this.dependencies.delete(name)

    return true
  }

  /**
   * 获取插件
   */
  get(name: string): Plugin | undefined {
    return this.plugins.get(name)
  }

  /**
   * 检查插件是否存在
   */
  has(name: string): boolean {
    return this.plugins.has(name)
  }

  /**
   * 获取所有插件
   */
  getAll(): Plugin[] {
    return Array.from(this.plugins.values())
  }

  /**
   * 获取插件元数据
   */
  getMetadata(name: string): PluginMetadata | undefined {
    return this.metadata.get(name)
  }

  /**
   * 获取所有插件元数据
   */
  getAllMetadata(): PluginMetadata[] {
    return Array.from(this.metadata.values())
  }

  /**
   * 获取插件依赖
   */
  getDependencies(name: string): string[] {
    return this.dependencies.get(name) || []
  }

  /**
   * 解析插件依赖顺序
   */
  resolveDependencies(pluginNames: string[]): string[] {
    const resolved: string[] = []
    const visiting = new Set<string>()
    const visited = new Set<string>()

    const visit = (name: string): void => {
      if (visited.has(name)) {
        return
      }

      if (visiting.has(name)) {
        throw new MicroCoreError(
          `Circular dependency detected: ${name}`,
          'PLUGIN_CIRCULAR_DEPENDENCY'
        )
      }

      visiting.add(name)

      const dependencies = this.getDependencies(name)
      for (const dep of dependencies) {
        if (!this.has(dep)) {
          throw new MicroCoreError(
            `Plugin dependency "${dep}" not found for plugin "${name}"`,
            'PLUGIN_DEPENDENCY_NOT_FOUND'
          )
        }
        visit(dep)
      }

      visiting.delete(name)
      visited.add(name)
      resolved.push(name)
    }

    for (const name of pluginNames) {
      visit(name)
    }

    return resolved
  }

  /**
   * 启用插件
   */
  enable(name: string): boolean {
    const metadata = this.metadata.get(name)
    if (!metadata) {
      return false
    }

    metadata.enabled = true
    return true
  }

  /**
   * 禁用插件
   */
  disable(name: string): boolean {
    const metadata = this.metadata.get(name)
    if (!metadata) {
      return false
    }

    metadata.enabled = false
    return true
  }

  /**
   * 检查插件是否启用
   */
  isEnabled(name: string): boolean {
    const metadata = this.metadata.get(name)
    return metadata?.enabled || false
  }

  /**
   * 获取启用的插件
   */
  getEnabled(): Plugin[] {
    return Array.from(this.plugins.entries())
      .filter(([name]) => this.isEnabled(name))
      .map(([, plugin]) => plugin)
  }

  /**
   * 清空注册表
   */
  clear(): void {
    this.plugins.clear()
    this.metadata.clear()
    this.dependencies.clear()
  }

  /**
   * 获取插件数量
   */
  size(): number {
    return this.plugins.size
  }

  /**
   * 搜索插件
   */
  search(query: string): Plugin[] {
    const lowerQuery = query.toLowerCase()
    return Array.from(this.plugins.entries())
      .filter(([name, plugin]) => {
        const metadata = this.metadata.get(name)
        return (
          name.toLowerCase().includes(lowerQuery) ||
          (metadata?.description?.toLowerCase().includes(lowerQuery)) ||
          (metadata?.author?.toLowerCase().includes(lowerQuery))
        )
      })
      .map(([, plugin]) => plugin)
  }
}