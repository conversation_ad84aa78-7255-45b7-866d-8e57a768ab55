import type { Plugin, PluginLoadOptions, PluginLoadResult } from '../types/plugin'
import { MicroCoreError, logger } from '@micro-core/shared'
import { PluginRegistry } from './registry'

/**
 * 插件加载器
 * 负责插件的动态加载、卸载和生命周期管理
 */
export class PluginLoader {
  private registry: PluginRegistry
  private loadedPlugins = new Map<string, Plugin>()
  private loadingPromises = new Map<string, Promise<Plugin>>()

  constructor(registry: PluginRegistry) {
    this.registry = registry
  }

  /**
   * 加载插件
   */
  async load(name: string, options?: PluginLoadOptions): Promise<Plugin> {
    // 检查是否正在加载
    if (this.loadingPromises.has(name)) {
      return this.loadingPromises.get(name)!
    }

    // 检查是否已加载
    if (this.loadedPlugins.has(name)) {
      const plugin = this.loadedPlugins.get(name)!
      if (options?.reload) {
        await this.unload(name)
      } else {
        return plugin
      }
    }

    // 创建加载Promise
    const loadPromise = this.doLoad(name, options)
    this.loadingPromises.set(name, loadPromise)

    try {
      const plugin = await loadPromise
      this.loadedPlugins.set(name, plugin)
      return plugin
    } finally {
      this.loadingPromises.delete(name)
    }
  }

  /**
   * 执行插件加载
   */
  private async doLoad(name: string, options?: PluginLoadOptions): Promise<Plugin> {
    logger.debug(`Loading plugin: ${name}`)

    // 从注册表获取插件
    let plugin = this.registry.get(name)

    if (!plugin) {
      // 尝试动态导入
      if (options?.url) {
        plugin = await this.loadFromUrl(options.url)
      } else if (options?.module) {
        plugin = await this.loadFromModule(options.module)
      } else {
        throw new MicroCoreError(
          `Plugin "${name}" not found in registry and no load options provided`,
          'PLUGIN_NOT_FOUND'
        )
      }

      // 注册动态加载的插件
      this.registry.register(plugin, options?.config)
    }

    // 检查插件是否启用
    if (!this.registry.isEnabled(name)) {
      throw new MicroCoreError(
        `Plugin "${name}" is disabled`,
        'PLUGIN_DISABLED'
      )
    }

    // 加载依赖
    await this.loadDependencies(name)

    // 验证插件
    this.validatePlugin(plugin)

    logger.info(`Plugin loaded successfully: ${name}`)
    return plugin
  }

  /**
   * 从URL加载插件
   */
  private async loadFromUrl(url: string): Promise<Plugin> {
    try {
      const module = await import(url)
      return this.extractPlugin(module)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to load plugin from URL: ${url}`,
        'PLUGIN_LOAD_ERROR',
        { url, error }
      )
    }
  }

  /**
   * 从模块加载插件
   */
  private async loadFromModule(moduleName: string): Promise<Plugin> {
    try {
      const module = await import(moduleName)
      return this.extractPlugin(module)
    } catch (error) {
      throw new MicroCoreError(
        `Failed to load plugin module: ${moduleName}`,
        'PLUGIN_LOAD_ERROR',
        { moduleName, error }
      )
    }
  }

  /**
   * 从模块中提取插件
   */
  private extractPlugin(module: any): Plugin {
    // 尝试不同的导出方式
    const plugin = module.default || module.plugin || module

    if (!plugin || typeof plugin !== 'object') {
      throw new MicroCoreError(
        'Invalid plugin module: must export a plugin object',
        'PLUGIN_INVALID_MODULE'
      )
    }

    return plugin
  }

  /**
   * 加载插件依赖
   */
  private async loadDependencies(name: string): Promise<void> {
    const dependencies = this.registry.getDependencies(name)
    
    if (dependencies.length === 0) {
      return
    }

    // 解析依赖顺序
    const resolvedOrder = this.registry.resolveDependencies(dependencies)

    // 按顺序加载依赖
    for (const depName of resolvedOrder) {
      if (!this.loadedPlugins.has(depName)) {
        await this.load(depName)
      }
    }
  }

  /**
   * 验证插件
   */
  private validatePlugin(plugin: Plugin): void {
    if (!plugin.name || typeof plugin.name !== 'string') {
      throw new MicroCoreError(
        'Plugin must have a valid name',
        'PLUGIN_INVALID_NAME'
      )
    }

    if (!plugin.version || typeof plugin.version !== 'string') {
      throw new MicroCoreError(
        'Plugin must have a valid version',
        'PLUGIN_INVALID_VERSION'
      )
    }

    if (typeof plugin.install !== 'function') {
      throw new MicroCoreError(
        'Plugin must have an install method',
        'PLUGIN_INVALID_INSTALL'
      )
    }

    if (typeof plugin.uninstall !== 'function') {
      throw new MicroCoreError(
        'Plugin must have an uninstall method',
        'PLUGIN_INVALID_UNINSTALL'
      )
    }
  }

  /**
   * 卸载插件
   */
  async unload(name: string): Promise<boolean> {
    const plugin = this.loadedPlugins.get(name)
    if (!plugin) {
      return false
    }

    try {
      logger.debug(`Unloading plugin: ${name}`)

      // 调用插件的卸载方法
      if (typeof plugin.uninstall === 'function') {
        await plugin.uninstall()
      }

      // 从已加载列表中移除
      this.loadedPlugins.delete(name)

      logger.info(`Plugin unloaded successfully: ${name}`)
      return true
    } catch (error) {
      logger.error(`Failed to unload plugin: ${name}`, error)
      throw new MicroCoreError(
        `Failed to unload plugin: ${name}`,
        'PLUGIN_UNLOAD_ERROR',
        { name, error }
      )
    }
  }

  /**
   * 重新加载插件
   */
  async reload(name: string, options?: PluginLoadOptions): Promise<Plugin> {
    await this.unload(name)
    return this.load(name, { ...options, reload: true })
  }

  /**
   * 批量加载插件
   */
  async loadBatch(names: string[], options?: PluginLoadOptions): Promise<PluginLoadResult[]> {
    const results: PluginLoadResult[] = []

    // 解析依赖顺序
    const resolvedOrder = this.registry.resolveDependencies(names)

    for (const name of resolvedOrder) {
      try {
        const plugin = await this.load(name, options)
        results.push({
          name,
          success: true,
          plugin
        })
      } catch (error) {
        results.push({
          name,
          success: false,
          error: error as Error
        })

        // 如果不是可选加载，则停止后续加载
        if (!options?.optional) {
          break
        }
      }
    }

    return results
  }

  /**
   * 批量卸载插件
   */
  async unloadBatch(names: string[]): Promise<boolean[]> {
    const results: boolean[] = []

    // 反向卸载（先卸载依赖者）
    const reverseOrder = [...names].reverse()

    for (const name of reverseOrder) {
      try {
        const result = await this.unload(name)
        results.push(result)
      } catch (error) {
        logger.error(`Failed to unload plugin in batch: ${name}`, error)
        results.push(false)
      }
    }

    return results.reverse()
  }

  /**
   * 获取已加载的插件
   */
  getLoaded(): Plugin[] {
    return Array.from(this.loadedPlugins.values())
  }

  /**
   * 检查插件是否已加载
   */
  isLoaded(name: string): boolean {
    return this.loadedPlugins.has(name)
  }

  /**
   * 获取正在加载的插件
   */
  getLoading(): string[] {
    return Array.from(this.loadingPromises.keys())
  }

  /**
   * 检查插件是否正在加载
   */
  isLoading(name: string): boolean {
    return this.loadingPromises.has(name)
  }

  /**
   * 清空已加载的插件
   */
  async clear(): Promise<void> {
    const names = Array.from(this.loadedPlugins.keys())
    await this.unloadBatch(names)
  }

  /**
   * 获取加载统计信息
   */
  getStats() {
    return {
      total: this.registry.size(),
      loaded: this.loadedPlugins.size,
      loading: this.loadingPromises.size,
      enabled: this.registry.getEnabled().length
    }
  }
}