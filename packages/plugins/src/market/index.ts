/**
 * 插件市场模块
 * 提供插件的注册、发现、安装、验证等功能
 */

// 导出核心类
export { PluginRegistry, type PluginMetadata, type PluginPackage } from './registry'
export { PluginInstaller, type InstallOptions, type InstallResult, type UninstallResult } from './installer'
export { PluginValidator, type ValidationRule, type ValidationResult, type SecurityCheckResult } from './validator'

// 导出默认实例
export { pluginRegistry } from './registry'
export { pluginInstaller } from './installer'
export { pluginValidator } from './validator'

// 导出便捷函数
import { pluginRegistry } from './registry'
import { pluginInstaller } from './installer'
import { pluginValidator } from './validator'
import type { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'

/**
 * 插件市场管理器
 * 提供统一的插件市场操作接口
 */
export class PluginMarket {
  private static instance: PluginMarket
  private core?: MicroCore

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): PluginMarket {
    if (!PluginMarket.instance) {
      PluginMarket.instance = new PluginMarket()
    }
    return PluginMarket.instance
  }

  /**
   * 初始化插件市场
   */
  public initialize(core: MicroCore): void {
    this.core = core
    pluginInstaller.setCore(core)
  }

  /**
   * 注册插件
   */
  public register(
    name: string,
    plugin: new (...args: any[]) => PluginBase,
    metadata: Omit<import('./registry').PluginMetadata, 'name'>
  ): boolean {
    return pluginRegistry.register(name, plugin, {
      ...metadata,
      name
    })
  }

  /**
   * 安装插件
   */
  public async install(
    name: string,
    version?: string,
    options?: import('./installer').InstallOptions
  ): Promise<import('./installer').InstallResult> {
    return pluginInstaller.install(name, version, options)
  }

  /**
   * 卸载插件
   */
  public async uninstall(
    name: string,
    version?: string
  ): Promise<import('./installer').UninstallResult> {
    return pluginInstaller.uninstall(name, version)
  }

  /**
   * 验证插件
   */
  public async validate(
    name: string,
    version?: string
  ): Promise<import('./validator').ValidationResult | null> {
    const pluginPackage = pluginRegistry.get(name, version)
    if (!pluginPackage) {
      return null
    }
    return pluginValidator.validate(pluginPackage)
  }

  /**
   * 搜索插件
   */
  public search(query: string): Array<{
    name: string
    versions: string[]
    metadata: import('./registry').PluginMetadata
  }> {
    return pluginRegistry.search(query)
  }

  /**
   * 获取已安装插件列表
   */
  public getInstalledPlugins(): Array<{
    name: string
    version: string
    instance: PluginBase
  }> {
    return pluginInstaller.getInstalledPlugins()
  }

  /**
   * 获取可用插件列表
   */
  public getAvailablePlugins(): Array<{
    name: string
    versions: string[]
    metadata: import('./registry').PluginMetadata
  }> {
    return pluginRegistry.list()
  }

  /**
   * 检查插件是否已安装
   */
  public isInstalled(name: string, version?: string): boolean {
    return pluginInstaller.isInstalled(name, version)
  }

  /**
   * 获取插件实例
   */
  public getPluginInstance(name: string, version?: string): PluginBase | undefined {
    return pluginInstaller.getPluginInstance(name, version)
  }

  /**
   * 批量安装插件
   */
  public async installBatch(
    plugins: Array<{
      name: string
      version?: string
      options?: import('./installer').InstallOptions
    }>
  ): Promise<import('./installer').InstallResult[]> {
    return pluginInstaller.installBatch(plugins)
  }

  /**
   * 升级插件
   */
  public async upgrade(
    name: string,
    targetVersion?: string
  ): Promise<import('./installer').InstallResult> {
    return pluginInstaller.upgrade(name, targetVersion)
  }

  /**
   * 安全检查
   */
  public async securityCheck(
    name: string,
    version?: string
  ): Promise<import('./validator').SecurityCheckResult | null> {
    const pluginPackage = pluginRegistry.get(name, version)
    if (!pluginPackage) {
      return null
    }
    return pluginValidator.securityCheck(pluginPackage)
  }

  /**
   * 获取插件统计信息
   */
  public getStatistics(): {
    totalPlugins: number
    installedPlugins: number
    availablePlugins: number
    categories: Record<string, number>
  } {
    const available = this.getAvailablePlugins()
    const installed = this.getInstalledPlugins()
    
    const categories: Record<string, number> = {}
    available.forEach(plugin => {
      const category = plugin.metadata.category || 'uncategorized'
      categories[category] = (categories[category] || 0) + 1
    })

    return {
      totalPlugins: available.length,
      installedPlugins: installed.length,
      availablePlugins: available.length,
      categories
    }
  }

  /**
   * 清理缓存
   */
  public clearCache(): void {
    pluginRegistry.clear()
    pluginInstaller.clearQueue()
  }
}

/**
 * 默认插件市场实例
 */
export const pluginMarket = PluginMarket.getInstance()

/**
 * 便捷函数：注册插件
 */
export function registerPlugin(
  name: string,
  plugin: new (...args: any[]) => PluginBase,
  metadata: Omit<import('./registry').PluginMetadata, 'name'>
): boolean {
  return pluginMarket.register(name, plugin, metadata)
}

/**
 * 便捷函数：安装插件
 */
export async function installPlugin(
  name: string,
  version?: string,
  options?: import('./installer').InstallOptions
): Promise<import('./installer').InstallResult> {
  return pluginMarket.install(name, version, options)
}

/**
 * 便捷函数：卸载插件
 */
export async function uninstallPlugin(
  name: string,
  version?: string
): Promise<import('./installer').UninstallResult> {
  return pluginMarket.uninstall(name, version)
}

/**
 * 便捷函数：搜索插件
 */
export function searchPlugins(query: string): Array<{
  name: string
  versions: string[]
  metadata: import('./registry').PluginMetadata
}> {
  return pluginMarket.search(query)
}

/**
 * 便捷函数：验证插件
 */
export async function validatePlugin(
  name: string,
  version?: string
): Promise<import('./validator').ValidationResult | null> {
  return pluginMarket.validate(name, version)
}

export default PluginMarket