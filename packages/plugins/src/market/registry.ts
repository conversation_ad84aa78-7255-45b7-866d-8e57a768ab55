import type { PluginBase } from '../base/plugin-base'
import { logger } from '@micro-core/shared'

/**
 * 插件元数据接口
 */
export interface PluginMetadata {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description: string
  /** 插件作者 */
  author: string
  /** 插件主页 */
  homepage?: string
  /** 插件仓库 */
  repository?: string
  /** 插件关键词 */
  keywords?: string[]
  /** 插件许可证 */
  license?: string
  /** 插件依赖 */
  dependencies?: Record<string, string>
  /** 插件入口文件 */
  main: string
  /** 插件类型 */
  type: 'core' | 'extension' | 'adapter' | 'builder'
  /** 插件分类 */
  category?: string
  /** 插件标签 */
  tags?: string[]
  /** 最小核心版本 */
  minCoreVersion?: string
  /** 最大核心版本 */
  maxCoreVersion?: string
  /** 插件图标 */
  icon?: string
  /** 插件截图 */
  screenshots?: string[]
  /** 插件文档 */
  documentation?: string
  /** 发布时间 */
  publishedAt?: string
  /** 更新时间 */
  updatedAt?: string
  /** 下载次数 */
  downloads?: number
  /** 评分 */
  rating?: number
  /** 评价数量 */
  reviewCount?: number
}

/**
 * 插件包接口
 */
export interface PluginPackage {
  /** 插件元数据 */
  metadata: PluginMetadata
  /** 插件构造函数 */
  plugin: new (...args: any[]) => PluginBase
  /** 插件源码 */
  source?: string
  /** 插件配置 */
  config?: Record<string, any>
}

/**
 * 搜索选项接口
 */
export interface SearchOptions {
  /** 搜索关键词 */
  query?: string
  /** 插件类型 */
  type?: string
  /** 插件分类 */
  category?: string
  /** 插件标签 */
  tags?: string[]
  /** 作者 */
  author?: string
  /** 排序方式 */
  sortBy?: 'name' | 'downloads' | 'rating' | 'publishedAt' | 'updatedAt'
  /** 排序顺序 */
  sortOrder?: 'asc' | 'desc'
  /** 分页大小 */
  limit?: number
  /** 分页偏移 */
  offset?: number
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  /** 插件列表 */
  plugins: PluginMetadata[]
  /** 总数量 */
  total: number
  /** 当前页 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 总页数 */
  totalPages: number
}

/**
 * 插件注册表
 * 管理插件的注册、搜索和获取
 */
export class PluginRegistry {
  private plugins = new Map<string, PluginPackage>()
  private remoteRegistries: string[] = []

  /**
   * 注册插件
   */
  public register(pluginPackage: PluginPackage): void {
    const { name, version } = pluginPackage.metadata
    const key = `${name}@${version}`
    
    if (this.plugins.has(key)) {
      logger.warn(`[PluginRegistry] 插件已存在: ${key}`)
      return
    }

    this.plugins.set(key, pluginPackage)
    logger.info(`[PluginRegistry] 注册插件: ${key}`)
  }

  /**
   * 取消注册插件
   */
  public unregister(name: string, version?: string): boolean {
    if (version) {
      const key = `${name}@${version}`
      const result = this.plugins.delete(key)
      if (result) {
        logger.info(`[PluginRegistry] 取消注册插件: ${key}`)
      }
      return result
    } else {
      // 删除所有版本
      let deleted = false
      for (const key of this.plugins.keys()) {
        if (key.startsWith(`${name}@`)) {
          this.plugins.delete(key)
          deleted = true
        }
      }
      if (deleted) {
        logger.info(`[PluginRegistry] 取消注册插件所有版本: ${name}`)
      }
      return deleted
    }
  }

  /**
   * 获取插件
   */
  public get(name: string, version?: string): PluginPackage | undefined {
    if (version) {
      return this.plugins.get(`${name}@${version}`)
    } else {
      // 获取最新版本
      const versions = this.getVersions(name)
      if (versions.length === 0) {
        return undefined
      }
      
      const latestVersion = this.getLatestVersion(versions)
      return this.plugins.get(`${name}@${latestVersion}`)
    }
  }

  /**
   * 检查插件是否存在
   */
  public has(name: string, version?: string): boolean {
    return this.get(name, version) !== undefined
  }

  /**
   * 获取插件的所有版本
   */
  public getVersions(name: string): string[] {
    const versions: string[] = []
    
    for (const key of this.plugins.keys()) {
      if (key.startsWith(`${name}@`)) {
        const version = key.split('@')[1]
        if (version) {
          versions.push(version)
        }
      }
    }

    return versions.sort(this.compareVersions)
  }

  /**
   * 获取所有插件名称
   */
  public getPluginNames(): string[] {
    const names = new Set<string>()
    
    for (const key of this.plugins.keys()) {
      const name = key.split('@')[0]
      if (name) {
        names.add(name)
      }
    }

    return Array.from(names).sort()
  }

  /**
   * 搜索插件
   */
  public search(options: SearchOptions = {}): SearchResult {
    const {
      query,
      type,
      category,
      tags,
      author,
      sortBy = 'name',
      sortOrder = 'asc',
      limit = 20,
      offset = 0
    } = options

    let results = Array.from(this.plugins.values())

    // 过滤条件
    if (query) {
      const lowerQuery = query.toLowerCase()
      results = results.filter(pkg => 
        pkg.metadata.name.toLowerCase().includes(lowerQuery) ||
        pkg.metadata.description.toLowerCase().includes(lowerQuery) ||
        pkg.metadata.keywords?.some(keyword => 
          keyword.toLowerCase().includes(lowerQuery)
        )
      )
    }

    if (type) {
      results = results.filter(pkg => pkg.metadata.type === type)
    }

    if (category) {
      results = results.filter(pkg => pkg.metadata.category === category)
    }

    if (tags && tags.length > 0) {
      results = results.filter(pkg => 
        pkg.metadata.tags?.some(tag => tags.includes(tag))
      )
    }

    if (author) {
      results = results.filter(pkg => 
        pkg.metadata.author.toLowerCase().includes(author.toLowerCase())
      )
    }

    // 排序
    results.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'name':
          comparison = a.metadata.name.localeCompare(b.metadata.name)
          break
        case 'downloads':
          comparison = (a.metadata.downloads || 0) - (b.metadata.downloads || 0)
          break
        case 'rating':
          comparison = (a.metadata.rating || 0) - (b.metadata.rating || 0)
          break
        case 'publishedAt':
          comparison = new Date(a.metadata.publishedAt || 0).getTime() - 
                      new Date(b.metadata.publishedAt || 0).getTime()
          break
        case 'updatedAt':
          comparison = new Date(a.metadata.updatedAt || 0).getTime() - 
                      new Date(b.metadata.updatedAt || 0).getTime()
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    // 分页
    const total = results.length
    const paginatedResults = results.slice(offset, offset + limit)
    const totalPages = Math.ceil(total / limit)
    const page = Math.floor(offset / limit) + 1

    return {
      plugins: paginatedResults.map(pkg => pkg.metadata),
      total,
      page,
      pageSize: limit,
      totalPages
    }
  }

  /**
   * 获取插件统计信息
   */
  public getStats(): {
    totalPlugins: number
    totalVersions: number
    typeDistribution: Record<string, number>
    categoryDistribution: Record<string, number>
  } {
    const typeDistribution: Record<string, number> = {}
    const categoryDistribution: Record<string, number> = {}

    for (const pkg of this.plugins.values()) {
      // 统计类型分布
      const type = pkg.metadata.type
      typeDistribution[type] = (typeDistribution[type] || 0) + 1

      // 统计分类分布
      const category = pkg.metadata.category || 'uncategorized'
      categoryDistribution[category] = (categoryDistribution[category] || 0) + 1
    }

    return {
      totalPlugins: this.getPluginNames().length,
      totalVersions: this.plugins.size,
      typeDistribution,
      categoryDistribution
    }
  }

  /**
   * 添加远程注册表
   */
  public addRemoteRegistry(url: string): void {
    if (!this.remoteRegistries.includes(url)) {
      this.remoteRegistries.push(url)
      logger.info(`[PluginRegistry] 添加远程注册表: ${url}`)
    }
  }

  /**
   * 移除远程注册表
   */
  public removeRemoteRegistry(url: string): boolean {
    const index = this.remoteRegistries.indexOf(url)
    if (index !== -1) {
      this.remoteRegistries.splice(index, 1)
      logger.info(`[PluginRegistry] 移除远程注册表: ${url}`)
      return true
    }
    return false
  }

  /**
   * 从远程注册表搜索插件
   */
  public async searchRemote(options: SearchOptions = {}): Promise<SearchResult> {
    // 这里可以实现从远程注册表搜索插件的逻辑
    // 目前返回空结果
    return {
      plugins: [],
      total: 0,
      page: 1,
      pageSize: options.limit || 20,
      totalPages: 0
    }
  }

  /**
   * 清空注册表
   */
  public clear(): void {
    this.plugins.clear()
    logger.info('[PluginRegistry] 清空插件注册表')
  }

  /**
   * 比较版本号
   */
  private compareVersions(a: string, b: string): number {
    const aParts = a.split('.').map(Number)
    const bParts = b.split('.').map(Number)
    const maxLength = Math.max(aParts.length, bParts.length)

    for (let i = 0; i < maxLength; i++) {
      const aPart = aParts[i] || 0
      const bPart = bParts[i] || 0

      if (aPart < bPart) return -1
      if (aPart > bPart) return 1
    }

    return 0
  }

  /**
   * 获取最新版本
   */
  private getLatestVersion(versions: string[]): string {
    return versions.sort(this.compareVersions).pop() || '0.0.0'
  }
}

/**
 * 默认插件注册表实例
 */
export const pluginRegistry = new PluginRegistry()

export default PluginRegistry