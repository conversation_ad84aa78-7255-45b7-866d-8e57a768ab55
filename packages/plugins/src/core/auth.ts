import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { AuthConfig, AuthProvider, Permission, User, AuthEvent } from '../types/plugin'

/**
 * Auth插件 - 权限管理
 * 提供微前端应用的权限管理和用户认证功能
 */
export class AuthPlugin extends PluginBase {
  public readonly name = 'auth'
  public readonly version = '0.1.0'

  private providers: Map<string, AuthProvider> = new Map()
  private currentUser: User | null = null
  private permissions: Set<string> = new Set()
  private core: MicroCore | null = null
  private config: AuthConfig | null = null

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.registerAuthAPI()
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.providers.clear()
    this.currentUser = null
    this.permissions.clear()
    this.core = null
    this.config = null
  }

  /**
   * 配置权限系统
   */
  public configure(config: AuthConfig): void {
    this.config = config
    
    // 注册默认提供者
    if (config.providers) {
      config.providers.forEach(provider => {
        this.registerProvider(provider.name, provider)
      })
    }
  }

  /**
   * 注册权限提供者
   */
  public registerProvider(name: string, provider: AuthProvider): void {
    this.providers.set(name, provider)
  }

  /**
   * 获取权限提供者
   */
  public getProvider(name: string): AuthProvider | undefined {
    return this.providers.get(name)
  }

  /**
   * 用户登录
   */
  public async login(providerName: string, credentials: any): Promise<boolean> {
    const provider = this.providers.get(providerName)
    if (!provider) {
      throw new Error(`Auth provider not found: ${providerName}`)
    }

    try {
      const result = await provider.login(credentials)
      if (result.success && result.user) {
        this.currentUser = result.user
        this.permissions = new Set(result.permissions || [])
        
        // 触发登录事件
        const event: AuthEvent = {
          type: 'login',
          user: result.user,
          provider: providerName
        }
        this.emit('auth-login', event)
        
        return true
      }
      return false
    } catch (error) {
      console.error('[AuthPlugin] Login error:', error)
      return false
    }
  }

  /**
   * 用户登出
   */
  public async logout(providerName?: string): Promise<boolean> {
    const provider = providerName 
      ? this.providers.get(providerName)
      : this.getCurrentProvider()

    if (!provider) {
      console.warn('[AuthPlugin] No auth provider available for logout')
      return false
    }

    try {
      const result = await provider.logout()
      if (result.success) {
        const user = this.currentUser
        this.currentUser = null
        this.permissions.clear()
        
        // 触发登出事件
        const event: AuthEvent = {
          type: 'logout',
          user,
          provider: providerName || 'unknown'
        }
        this.emit('auth-logout', event)
        
        return true
      }
      return false
    } catch (error) {
      console.error('[AuthPlugin] Logout error:', error)
      return false
    }
  }

  /**
   * 刷新令牌
   */
  public async refreshToken(providerName?: string): Promise<boolean> {
    const provider = providerName 
      ? this.providers.get(providerName)
      : this.getCurrentProvider()

    if (!provider || !provider.refreshToken) {
      return false
    }

    try {
      const result = await provider.refreshToken()
      if (result.success && result.user) {
        this.currentUser = result.user
        this.permissions = new Set(result.permissions || [])
        
        // 触发令牌刷新事件
        const event: AuthEvent = {
          type: 'token-refresh',
          user: result.user,
          provider: providerName || 'unknown'
        }
        this.emit('auth-token-refresh', event)
        
        return true
      }
      return false
    } catch (error) {
      console.error('[AuthPlugin] Token refresh error:', error)
      return false
    }
  }

  /**
   * 检查用户是否已登录
   */
  public isAuthenticated(): boolean {
    return this.currentUser !== null
  }

  /**
   * 获取当前用户
   */
  public getCurrentUser(): User | null {
    return this.currentUser
  }

  /**
   * 检查权限
   */
  public hasPermission(permission: string): boolean {
    return this.permissions.has(permission)
  }

  /**
   * 检查多个权限（AND关系）
   */
  public hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.permissions.has(permission))
  }

  /**
   * 检查多个权限（OR关系）
   */
  public hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.permissions.has(permission))
  }

  /**
   * 添加权限
   */
  public addPermission(permission: string): void {
    this.permissions.add(permission)
  }

  /**
   * 移除权限
   */
  public removePermission(permission: string): void {
    this.permissions.delete(permission)
  }

  /**
   * 获取所有权限
   */
  public getPermissions(): string[] {
    return Array.from(this.permissions)
  }

  /**
   * 检查角色
   */
  public hasRole(role: string): boolean {
    return this.currentUser?.roles?.includes(role) || false
  }

  /**
   * 检查多个角色（AND关系）
   */
  public hasAllRoles(roles: string[]): boolean {
    if (!this.currentUser?.roles) return false
    return roles.every(role => this.currentUser!.roles!.includes(role))
  }

  /**
   * 检查多个角色（OR关系）
   */
  public hasAnyRole(roles: string[]): boolean {
    if (!this.currentUser?.roles) return false
    return roles.some(role => this.currentUser!.roles!.includes(role))
  }

  /**
   * 权限守卫装饰器
   */
  public requirePermission(permission: string) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value
      
      descriptor.value = function (...args: any[]) {
        if (!this.hasPermission(permission)) {
          throw new Error(`Permission denied: ${permission}`)
        }
        return originalMethod.apply(this, args)
      }
      
      return descriptor
    }
  }

  /**
   * 角色守卫装饰器
   */
  public requireRole(role: string) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value
      
      descriptor.value = function (...args: any[]) {
        if (!this.hasRole(role)) {
          throw new Error(`Role required: ${role}`)
        }
        return originalMethod.apply(this, args)
      }
      
      return descriptor
    }
  }

  /**
   * 获取当前权限提供者
   */
  private getCurrentProvider(): AuthProvider | undefined {
    // 简单实现：返回第一个提供者
    return this.providers.values().next().value
  }

  /**
   * 注册权限API到核心
   */
  private registerAuthAPI(): void {
    if (!this.core) return

    // 注册全局权限API
    const authAPI = {
      login: this.login.bind(this),
      logout: this.logout.bind(this),
      refreshToken: this.refreshToken.bind(this),
      isAuthenticated: this.isAuthenticated.bind(this),
      getCurrentUser: this.getCurrentUser.bind(this),
      hasPermission: this.hasPermission.bind(this),
      hasAllPermissions: this.hasAllPermissions.bind(this),
      hasAnyPermission: this.hasAnyPermission.bind(this),
      hasRole: this.hasRole.bind(this),
      hasAllRoles: this.hasAllRoles.bind(this),
      hasAnyRole: this.hasAnyRole.bind(this),
      getPermissions: this.getPermissions.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_AUTH__ = authAPI
    }
  }
}

export default AuthPlugin