import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { DevToolsConfig, DebugInfo, LogLevel } from '../types/plugin'

/**
 * DevTools插件 - 开发工具
 * 提供微前端应用的开发调试和监控功能
 */
export class DevToolsPlugin extends PluginBase {
  public readonly name = 'dev-tools'
  public readonly version = '0.1.0'

  private core: MicroCore | null = null
  private config: DevToolsConfig | null = null
  private debugPanel: HTMLElement | null = null
  private logs: Array<{ level: LogLevel; message: string; timestamp: number; data?: any }> = []
  private isEnabled = false

  /**
   * 渲染性能信息
   */
  private renderPerformance(): string {
    const performance = this.getPerformanceInfo()
    return `
      <div class="micro-core-debug-log info">
        <strong>内存使用</strong>
        <div>已用堆: ${performance.memoryUsage?.usedJSHeapSize || 'N/A'}</div>
        <div>总堆大小: ${performance.memoryUsage?.totalJSHeapSize || 'N/A'}</div>
        <div>堆限制: ${performance.memoryUsage?.jsHeapSizeLimit || 'N/A'}</div>
      </div>
      <div class="micro-core-debug-log info">
        <strong>加载时间</strong>
        <div>页面加载: ${performance.loadTime || 'N/A'}ms</div>
        <div>DOM就绪: ${performance.domReadyTime || 'N/A'}ms</div>
      </div>
    `
  }

  /**
   * 获取应用信息
   */
  private getApplicationsInfo(): Array<{
    name: string
    status: string
    entry: string
    container: string
  }> {
    if (!this.core) return []

    // 这里需要从核心获取应用信息
    // 暂时返回模拟数据
    return [
      {
        name: 'main-app',
        status: 'mounted',
        entry: 'http://localhost:3000',
        container: '#app'
      }
    ]
  }

  /**
   * 获取插件信息
   */
  private getPluginsInfo(): Array<{
    name: string
    version: string
    enabled: boolean
  }> {
    if (!this.core) return []

    // 这里需要从核心获取插件信息
    // 暂时返回模拟数据
    return [
      {
        name: 'router',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'auth',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'dev-tools',
        version: '0.1.0',
        enabled: true
      }
    ]
  }

  /**
   * 获取性能信息
   */
  private getPerformanceInfo(): {
    memoryUsage?: MemoryInfo
    loadTime?: number
    domReadyTime?: number
  } {
    const performance: any = {}

    // 获取内存使用信息
    if ('memory' in window.performance) {
      performance.memoryUsage = (window.performance as any).memory
    }

    // 获取加载时间
    if (window.performance.timing) {
      const timing = window.performance.timing
      performance.loadTime = timing.loadEventEnd - timing.navigationStart
      performance.domReadyTime = timing.domContentLoadedEventEnd - timing.navigationStart
    }

    return performance
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(logEntry: {
    level: LogLevel
    message: string
    timestamp: number
    data?: any
  }): void {
    const time = new Date(logEntry.timestamp).toLocaleTimeString()
    const prefix = `[MicroCore DevTools ${time}]`

    switch (logEntry.level) {
      case 'error':
        console.error(prefix, logEntry.message, logEntry.data)
        break
      case 'warn':
        console.warn(prefix, logEntry.message, logEntry.data)
        break
      case 'debug':
        console.debug(prefix, logEntry.message, logEntry.data)
        break
      default:
        console.log(prefix, logEntry.message, logEntry.data)
        break
    }
  }

  /**
   * 注册开发工具API到核心
   */
  private registerDevToolsAPI(): void {
    if (!this.core) return

    // 注册全局开发工具API
    const devToolsAPI = {
      log: this.log.bind(this),
      getDebugInfo: this.getDebugInfo.bind(this),
      clearLogs: this.clearLogs.bind(this),
      showDebugPanel: this.showDebugPanel.bind(this),
      hideDebugPanel: this.hideDebugPanel.bind(this),
      toggleDebugPanel: this.toggleDebugPanel.bind(this),
      exportDebugData: this.exportDebugData.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_DEV_TOOLS__ = devToolsAPI
    }
  }
}

export default DevToolsPlugin
  public install(core: MicroCore): void {
    this.core = core
    this.isEnabled = process.env.NODE_ENV === 'development'
    
    if (this.isEnabled) {
      this.setupDevTools()
      this.registerDevToolsAPI()
    }
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    if (this.debugPanel) {
      this.debugPanel.remove()
      this.debugPanel = null
    }
    this.logs = []
    this.core = null
    this.config = null
    this.isEnabled = false
  }

  /**
   * 配置开发工具
   */
  public configure(config: DevToolsConfig): void {
    this.config = config
    this.isEnabled = config.enabled !== false && process.env.NODE_ENV === 'development'
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, message: string, data?: any): void {
    if (!this.isEnabled) return

    const logEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    }

    this.logs.push(logEntry)

    // 限制日志数量
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500)
    }

    // 输出到控制台
    this.outputToConsole(logEntry)

    // 更新调试面板
    this.updateDebugPanel()
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): DebugInfo {
    if (!this.core) {
      return {
        applications: [],
        plugins: [],
        performance: {},
        logs: this.logs
      }
    }

    return {
      applications: this.getApplicationsInfo(),
      plugins: this.getPluginsInfo(),
      performance: this.getPerformanceInfo(),
      logs: this.logs
    }
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = []
    this.updateDebugPanel()
  }

  /**
   * 显示调试面板
   */
  public showDebugPanel(): void {
    if (!this.isEnabled || this.debugPanel) return

    this.createDebugPanel()
  }

  /**
   * 隐藏调试面板
   */
  public hideDebugPanel(): void {
    if (this.debugPanel) {
      this.debugPanel.style.display = 'none'
    }
  }

  /**
   * 切换调试面板显示状态
   */
  public toggleDebugPanel(): void {
    if (!this.debugPanel) {
      this.showDebugPanel()
    } else {
      const isVisible = this.debugPanel.style.display !== 'none'
      this.debugPanel.style.display = isVisible ? 'none' : 'block'
    }
  }

  /**
   * 导出调试数据
   */
  public exportDebugData(): string {
    const debugInfo = this.getDebugInfo()
    return JSON.stringify(debugInfo, null, 2)
  }

  /**
   * 设置开发工具
   */
  private setupDevTools(): void {
    // 监听键盘快捷键
    this.setupKeyboardShortcuts()
    
    // 监听核心事件
    this.setupCoreEventListeners()
    
    // 注入调试样式
    this.injectDebugStyles()
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl/Cmd + Shift + D 切换调试面板
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault()
        this.toggleDebugPanel()
      }
    })
  }

  /**
   * 设置核心事件监听器
   */
  private setupCoreEventListeners(): void {
    if (!this.core) return

    // 监听应用生命周期事件
    this.core.on('app-bootstrap', (app) => {
      this.log('info', `Application bootstrapped: ${app.name}`, app)
    })

    this.core.on('app-mount', (app) => {
      this.log('info', `Application mounted: ${app.name}`, app)
    })

    this.core.on('app-unmount', (app) => {
      this.log('info', `Application unmounted: ${app.name}`, app)
    })

    this.core.on('app-error', (error) => {
      this.log('error', `Application error: ${error.message}`, error)
    })
  }

  /**
   * 注入调试样式
   */
  private injectDebugStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .micro-core-debug-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        height: 600px;
        background: #1e1e1e;
        color: #fff;
        border: 1px solid #333;
        border-radius: 8px;
        z-index: 10000;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }
      
      .micro-core-debug-header {
        background: #333;
        padding: 10px;
        border-bottom: 1px solid #555;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .micro-core-debug-content {
        height: calc(100% - 50px);
        overflow-y: auto;
        padding: 10px;
      }
      
      .micro-core-debug-tab {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 5px;
        background: #555;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
      }
      
      .micro-core-debug-tab.active {
        background: #007acc;
      }
      
      .micro-core-debug-tab:hover {
        background: #666;
      }
      
      .micro-core-debug-log {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
        border-left: 3px solid #555;
      }
      
      .micro-core-debug-log.info {
        border-left-color: #007acc;
      }
      
      .micro-core-debug-log.warn {
        border-left-color: #ff9800;
      }
      
      .micro-core-debug-log.error {
        border-left-color: #f44336;
      }
      
      .micro-core-debug-log.debug {
        border-left-color: #4caf50;
      }
      
      .micro-core-debug-timestamp {
        color: #888;
        font-size: 10px;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 创建调试面板
   */
  private createDebugPanel(): void {
    this.debugPanel = document.createElement('div')
    this.debugPanel.className = 'micro-core-debug-panel'
    
    this.debugPanel.innerHTML = `
      <div class="micro-core-debug-header">
        <div>
          <span class="micro-core-debug-tab active" data-tab="logs">Logs</span>
          <span class="micro-core-debug-tab" data-tab="apps">Apps</span>
          <span class="micro-core-debug-tab" data-tab="plugins">Plugins</span>
          <span class="micro-core-debug-tab" data-tab="performance">Performance</span>
        </div>
        <button onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
      <div class="micro-core-debug-content" id="debug-content">
        <!-- 内容将通过JavaScript动态填充 -->
      </div>
    `

    // 添加标签页切换事件
    this.debugPanel.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (target.classList.contains('micro-core-debug-tab')) {
        // 移除所有活动状态
        this.debugPanel!.querySelectorAll('.micro-core-debug-tab').forEach(tab => {
          tab.classList.remove('active')
        })
        
        // 添加当前活动状态
        target.classList.add('active')
        
        // 更新内容
        this.updateDebugPanelContent(target.dataset.tab!)
      }
    })

    document.body.appendChild(this.debugPanel)
    this.updateDebugPanel()
  }

  /**
   * 更新调试面板
   */
  private updateDebugPanel(): void {
    if (!this.debugPanel) return

    const activeTab = this.debugPanel.querySelector('.micro-core-debug-tab.active')
    const tabName = activeTab?.getAttribute('data-tab') || 'logs'
    this.updateDebugPanelContent(tabName)
  }

  /**
   * 更新调试面板内容
   */
  private updateDebugPanelContent(tab: string): void {
    if (!this.debugPanel) return

    const content = this.debugPanel.querySelector('#debug-content')
    if (!content) return

    switch (tab) {
      case 'logs':
        content.innerHTML = this.renderLogs()
        break
      case 'apps':
        content.innerHTML = this.renderApplications()
        break
      case 'plugins':
        content.innerHTML = this.renderPlugins()
        break
      case 'performance':
        content.innerHTML = this.renderPerformance()
        break
    }
  }

  /**
   * 渲染日志
   */
  private renderLogs(): string {
    return this.logs.slice(-50).map(log => {
      const time = new Date(log.timestamp).toLocaleTimeString()
      return `
        <div class="micro-core-debug-log ${log.level}">
          <div class="micro-core-debug-timestamp">${time}</div>
          <div>${log.message}</div>
          ${log.data ? `<pre>${JSON.stringify(log.data, null, 2)}</pre>` : ''}
        </div>
      `
    }).join('')
  }
import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { DevToolsConfig, DebugInfo, LogLevel } from '../types/plugin'

/**
 * DevTools插件 - 开发工具
 * 提供微前端应用的开发调试和监控功能
 */
export class DevToolsPlugin extends PluginBase {
  public readonly name = 'dev-tools'
  public readonly version = '0.1.0'

  private core: MicroCore | null = null
  private config: DevToolsConfig | null = null
  private debugPanel: HTMLElement | null = null
  private logs: Array<{ level: LogLevel; message: string; timestamp: number; data?: any }> = []
  private isEnabled = false

  /**
   * 渲染性能信息
   */
  private renderPerformance(): string {
    const performance = this.getPerformanceInfo()
    return `
      <div class="micro-core-debug-log info">
        <strong>内存使用</strong>
        <div>已用堆: ${performance.memoryUsage?.usedJSHeapSize || 'N/A'}</div>
        <div>总堆大小: ${performance.memoryUsage?.totalJSHeapSize || 'N/A'}</div>
        <div>堆限制: ${performance.memoryUsage?.jsHeapSizeLimit || 'N/A'}</div>
      </div>
      <div class="micro-core-debug-log info">
        <strong>加载时间</strong>
        <div>页面加载: ${performance.loadTime || 'N/A'}ms</div>
        <div>DOM就绪: ${performance.domReadyTime || 'N/A'}ms</div>
      </div>
    `
  }

  /**
   * 获取应用信息
   */
  private getApplicationsInfo(): Array<{
    name: string
    status: string
    entry: string
    container: string
  }> {
    if (!this.core) return []

    // 这里需要从核心获取应用信息
    // 暂时返回模拟数据
    return [
      {
        name: 'main-app',
        status: 'mounted',
        entry: 'http://localhost:3000',
        container: '#app'
      }
    ]
  }

  /**
   * 获取插件信息
   */
  private getPluginsInfo(): Array<{
    name: string
    version: string
    enabled: boolean
  }> {
    if (!this.core) return []

    // 这里需要从核心获取插件信息
    // 暂时返回模拟数据
    return [
      {
        name: 'router',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'auth',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'dev-tools',
        version: '0.1.0',
        enabled: true
      }
    ]
  }

  /**
   * 获取性能信息
   */
  private getPerformanceInfo(): {
    memoryUsage?: MemoryInfo
    loadTime?: number
    domReadyTime?: number
  } {
    const performance: any = {}

    // 获取内存使用信息
    if ('memory' in window.performance) {
      performance.memoryUsage = (window.performance as any).memory
    }

    // 获取加载时间
    if (window.performance.timing) {
      const timing = window.performance.timing
      performance.loadTime = timing.loadEventEnd - timing.navigationStart
      performance.domReadyTime = timing.domContentLoadedEventEnd - timing.navigationStart
    }

    return performance
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(logEntry: {
    level: LogLevel
    message: string
    timestamp: number
    data?: any
  }): void {
    const time = new Date(logEntry.timestamp).toLocaleTimeString()
    const prefix = `[MicroCore DevTools ${time}]`

    switch (logEntry.level) {
      case 'error':
        console.error(prefix, logEntry.message, logEntry.data)
        break
      case 'warn':
        console.warn(prefix, logEntry.message, logEntry.data)
        break
      case 'debug':
        console.debug(prefix, logEntry.message, logEntry.data)
        break
      default:
        console.log(prefix, logEntry.message, logEntry.data)
        break
    }
  }

  /**
   * 注册开发工具API到核心
   */
  private registerDevToolsAPI(): void {
    if (!this.core) return

    // 注册全局开发工具API
    const devToolsAPI = {
      log: this.log.bind(this),
      getDebugInfo: this.getDebugInfo.bind(this),
      clearLogs: this.clearLogs.bind(this),
      showDebugPanel: this.showDebugPanel.bind(this),
      hideDebugPanel: this.hideDebugPanel.bind(this),
      toggleDebugPanel: this.toggleDebugPanel.bind(this),
      exportDebugData: this.exportDebugData.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_DEV_TOOLS__ = devToolsAPI
    }
  }
}

export default DevToolsPlugin
  public install(core: MicroCore): void {
    this.core = core
    this.isEnabled = process.env.NODE_ENV === 'development'
    
    if (this.isEnabled) {
      this.setupDevTools()
      this.registerDevToolsAPI()
    }
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    if (this.debugPanel) {
      this.debugPanel.remove()
      this.debugPanel = null
    }
    this.logs = []
    this.core = null
    this.config = null
    this.isEnabled = false
  }

  /**
   * 配置开发工具
   */
  public configure(config: DevToolsConfig): void {
    this.config = config
    this.isEnabled = config.enabled !== false && process.env.NODE_ENV === 'development'
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, message: string, data?: any): void {
    if (!this.isEnabled) return

    const logEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    }

    this.logs.push(logEntry)

    // 限制日志数量
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500)
    }

    // 输出到控制台
    this.outputToConsole(logEntry)

    // 更新调试面板
    this.updateDebugPanel()
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): DebugInfo {
    if (!this.core) {
      return {
        applications: [],
        plugins: [],
        performance: {},
        logs: this.logs
      }
    }

    return {
      applications: this.getApplicationsInfo(),
      plugins: this.getPluginsInfo(),
      performance: this.getPerformanceInfo(),
      logs: this.logs
    }
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = []
    this.updateDebugPanel()
  }

  /**
   * 显示调试面板
   */
  public showDebugPanel(): void {
    if (!this.isEnabled || this.debugPanel) return

    this.createDebugPanel()
  }

  /**
   * 隐藏调试面板
   */
  public hideDebugPanel(): void {
    if (this.debugPanel) {
      this.debugPanel.style.display = 'none'
    }
  }

  /**
   * 切换调试面板显示状态
   */
  public toggleDebugPanel(): void {
    if (!this.debugPanel) {
      this.showDebugPanel()
    } else {
      const isVisible = this.debugPanel.style.display !== 'none'
      this.debugPanel.style.display = isVisible ? 'none' : 'block'
    }
  }

  /**
   * 导出调试数据
   */
  public exportDebugData(): string {
    const debugInfo = this.getDebugInfo()
    return JSON.stringify(debugInfo, null, 2)
  }

  /**
   * 设置开发工具
   */
  private setupDevTools(): void {
    // 监听键盘快捷键
    this.setupKeyboardShortcuts()
    
    // 监听核心事件
    this.setupCoreEventListeners()
    
    // 注入调试样式
    this.injectDebugStyles()
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl/Cmd + Shift + D 切换调试面板
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault()
        this.toggleDebugPanel()
      }
    })
  }

  /**
   * 设置核心事件监听器
   */
  private setupCoreEventListeners(): void {
    if (!this.core) return

    // 监听应用生命周期事件
    this.core.on('app-bootstrap', (app) => {
      this.log('info', `Application bootstrapped: ${app.name}`, app)
    })

    this.core.on('app-mount', (app) => {
      this.log('info', `Application mounted: ${app.name}`, app)
    })

    this.core.on('app-unmount', (app) => {
      this.log('info', `Application unmounted: ${app.name}`, app)
    })

    this.core.on('app-error', (error) => {
      this.log('error', `Application error: ${error.message}`, error)
    })
  }

  /**
   * 注入调试样式
   */
  private injectDebugStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .micro-core-debug-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        height: 600px;
        background: #1e1e1e;
        color: #fff;
        border: 1px solid #333;
        border-radius: 8px;
        z-index: 10000;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }
      
      .micro-core-debug-header {
        background: #333;
        padding: 10px;
        border-bottom: 1px solid #555;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .micro-core-debug-content {
        height: calc(100% - 50px);
        overflow-y: auto;
        padding: 10px;
      }
      
      .micro-core-debug-tab {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 5px;
        background: #555;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
      }
      
      .micro-core-debug-tab.active {
        background: #007acc;
      }
      
      .micro-core-debug-tab:hover {
        background: #666;
      }
      
      .micro-core-debug-log {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
        border-left: 3px solid #555;
      }
      
      .micro-core-debug-log.info {
        border-left-color: #007acc;
      }
      
      .micro-core-debug-log.warn {
        border-left-color: #ff9800;
      }
      
      .micro-core-debug-log.error {
        border-left-color: #f44336;
      }
      
      .micro-core-debug-log.debug {
        border-left-color: #4caf50;
      }
      
      .micro-core-debug-timestamp {
        color: #888;
        font-size: 10px;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 创建调试面板
   */
  private createDebugPanel(): void {
    this.debugPanel = document.createElement('div')
    this.debugPanel.className = 'micro-core-debug-panel'
    
    this.debugPanel.innerHTML = `
      <div class="micro-core-debug-header">
        <div>
          <span class="micro-core-debug-tab active" data-tab="logs">Logs</span>
          <span class="micro-core-debug-tab" data-tab="apps">Apps</span>
          <span class="micro-core-debug-tab" data-tab="plugins">Plugins</span>
          <span class="micro-core-debug-tab" data-tab="performance">Performance</span>
        </div>
        <button onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
      <div class="micro-core-debug-content" id="debug-content">
        <!-- 内容将通过JavaScript动态填充 -->
      </div>
    `

    // 添加标签页切换事件
    this.debugPanel.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (target.classList.contains('micro-core-debug-tab')) {
        // 移除所有活动状态
        this.debugPanel!.querySelectorAll('.micro-core-debug-tab').forEach(tab => {
          tab.classList.remove('active')
        })
        
        // 添加当前活动状态
        target.classList.add('active')
        
        // 更新内容
        this.updateDebugPanelContent(target.dataset.tab!)
      }
    })

    document.body.appendChild(this.debugPanel)
    this.updateDebugPanel()
  }

  /**
   * 更新调试面板
   */
  private updateDebugPanel(): void {
    if (!this.debugPanel) return

    const activeTab = this.debugPanel.querySelector('.micro-core-debug-tab.active')
    const tabName = activeTab?.getAttribute('data-tab') || 'logs'
    this.updateDebugPanelContent(tabName)
  }

  /**
   * 更新调试面板内容
   */
  private updateDebugPanelContent(tab: string): void {
    if (!this.debugPanel) return

    const content = this.debugPanel.querySelector('#debug-content')
    if (!content) return

    switch (tab) {
      case 'logs':
        content.innerHTML = this.renderLogs()
        break
      case 'apps':
        content.innerHTML = this.renderApplications()
        break
      case 'plugins':
        content.innerHTML = this.renderPlugins()
        break
      case 'performance':
        content.innerHTML = this.renderPerformance()
        break
    }
  }

import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { DevToolsConfig, DebugInfo, LogLevel } from '../types/plugin'

/**
 * DevTools插件 - 开发工具
 * 提供微前端应用的开发调试和监控功能
 */
export class DevToolsPlugin extends PluginBase {
  public readonly name = 'dev-tools'
  public readonly version = '0.1.0'

  private core: MicroCore | null = null
  private config: DevToolsConfig | null = null
  private debugPanel: HTMLElement | null = null
  private logs: Array<{ level: LogLevel; message: string; timestamp: number; data?: any }> = []
  private isEnabled = false

  /**
   * 渲染性能信息
   */
  private renderPerformance(): string {
    const performance = this.getPerformanceInfo()
    return `
      <div class="micro-core-debug-log info">
        <strong>内存使用</strong>
        <div>已用堆: ${performance.memoryUsage?.usedJSHeapSize || 'N/A'}</div>
        <div>总堆大小: ${performance.memoryUsage?.totalJSHeapSize || 'N/A'}</div>
        <div>堆限制: ${performance.memoryUsage?.jsHeapSizeLimit || 'N/A'}</div>
      </div>
      <div class="micro-core-debug-log info">
        <strong>加载时间</strong>
        <div>页面加载: ${performance.loadTime || 'N/A'}ms</div>
        <div>DOM就绪: ${performance.domReadyTime || 'N/A'}ms</div>
      </div>
    `
  }

  /**
   * 获取应用信息
   */
  private getApplicationsInfo(): Array<{
    name: string
    status: string
    entry: string
    container: string
  }> {
    if (!this.core) return []

    // 这里需要从核心获取应用信息
    // 暂时返回模拟数据
    return [
      {
        name: 'main-app',
        status: 'mounted',
        entry: 'http://localhost:3000',
        container: '#app'
      }
    ]
  }

  /**
   * 获取插件信息
   */
  private getPluginsInfo(): Array<{
    name: string
    version: string
    enabled: boolean
  }> {
    if (!this.core) return []

    // 这里需要从核心获取插件信息
    // 暂时返回模拟数据
    return [
      {
        name: 'router',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'auth',
        version: '0.1.0',
        enabled: true
      },
      {
        name: 'dev-tools',
        version: '0.1.0',
        enabled: true
      }
    ]
  }

  /**
   * 获取性能信息
   */
  private getPerformanceInfo(): {
    memoryUsage?: MemoryInfo
    loadTime?: number
    domReadyTime?: number
  } {
    const performance: any = {}

    // 获取内存使用信息
    if ('memory' in window.performance) {
      performance.memoryUsage = (window.performance as any).memory
    }

    // 获取加载时间
    if (window.performance.timing) {
      const timing = window.performance.timing
      performance.loadTime = timing.loadEventEnd - timing.navigationStart
      performance.domReadyTime = timing.domContentLoadedEventEnd - timing.navigationStart
    }

    return performance
  }

  /**
   * 输出到控制台
   */
  private outputToConsole(logEntry: {
    level: LogLevel
    message: string
    timestamp: number
    data?: any
  }): void {
    const time = new Date(logEntry.timestamp).toLocaleTimeString()
    const prefix = `[MicroCore DevTools ${time}]`

    switch (logEntry.level) {
      case 'error':
        console.error(prefix, logEntry.message, logEntry.data)
        break
      case 'warn':
        console.warn(prefix, logEntry.message, logEntry.data)
        break
      case 'debug':
        console.debug(prefix, logEntry.message, logEntry.data)
        break
      default:
        console.log(prefix, logEntry.message, logEntry.data)
        break
    }
  }

  /**
   * 注册开发工具API到核心
   */
  private registerDevToolsAPI(): void {
    if (!this.core) return

    // 注册全局开发工具API
    const devToolsAPI = {
      log: this.log.bind(this),
      getDebugInfo: this.getDebugInfo.bind(this),
      clearLogs: this.clearLogs.bind(this),
      showDebugPanel: this.showDebugPanel.bind(this),
      hideDebugPanel: this.hideDebugPanel.bind(this),
      toggleDebugPanel: this.toggleDebugPanel.bind(this),
      exportDebugData: this.exportDebugData.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_DEV_TOOLS__ = devToolsAPI
    }
  }
}

export default DevToolsPlugin
  public install(core: MicroCore): void {
    this.core = core
    this.isEnabled = process.env.NODE_ENV === 'development'
    
    if (this.isEnabled) {
      this.setupDevTools()
      this.registerDevToolsAPI()
    }
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    if (this.debugPanel) {
      this.debugPanel.remove()
      this.debugPanel = null
    }
    this.logs = []
    this.core = null
    this.config = null
    this.isEnabled = false
  }

  /**
   * 配置开发工具
   */
  public configure(config: DevToolsConfig): void {
    this.config = config
    this.isEnabled = config.enabled !== false && process.env.NODE_ENV === 'development'
  }

  /**
   * 记录日志
   */
  public log(level: LogLevel, message: string, data?: any): void {
    if (!this.isEnabled) return

    const logEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    }

    this.logs.push(logEntry)

    // 限制日志数量
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-500)
    }

    // 输出到控制台
    this.outputToConsole(logEntry)

    // 更新调试面板
    this.updateDebugPanel()
  }

  /**
   * 获取调试信息
   */
  public getDebugInfo(): DebugInfo {
    if (!this.core) {
      return {
        applications: [],
        plugins: [],
        performance: {},
        logs: this.logs
      }
    }

    return {
      applications: this.getApplicationsInfo(),
      plugins: this.getPluginsInfo(),
      performance: this.getPerformanceInfo(),
      logs: this.logs
    }
  }

  /**
   * 清空日志
   */
  public clearLogs(): void {
    this.logs = []
    this.updateDebugPanel()
  }

  /**
   * 显示调试面板
   */
  public showDebugPanel(): void {
    if (!this.isEnabled || this.debugPanel) return

    this.createDebugPanel()
  }

  /**
   * 隐藏调试面板
   */
  public hideDebugPanel(): void {
    if (this.debugPanel) {
      this.debugPanel.style.display = 'none'
    }
  }

  /**
   * 切换调试面板显示状态
   */
  public toggleDebugPanel(): void {
    if (!this.debugPanel) {
      this.showDebugPanel()
    } else {
      const isVisible = this.debugPanel.style.display !== 'none'
      this.debugPanel.style.display = isVisible ? 'none' : 'block'
    }
  }

  /**
   * 导出调试数据
   */
  public exportDebugData(): string {
    const debugInfo = this.getDebugInfo()
    return JSON.stringify(debugInfo, null, 2)
  }

  /**
   * 设置开发工具
   */
  private setupDevTools(): void {
    // 监听键盘快捷键
    this.setupKeyboardShortcuts()
    
    // 监听核心事件
    this.setupCoreEventListeners()
    
    // 注入调试样式
    this.injectDebugStyles()
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl/Cmd + Shift + D 切换调试面板
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'D') {
        event.preventDefault()
        this.toggleDebugPanel()
      }
    })
  }

  /**
   * 设置核心事件监听器
   */
  private setupCoreEventListeners(): void {
    if (!this.core) return

    // 监听应用生命周期事件
    this.core.on('app-bootstrap', (app) => {
      this.log('info', `Application bootstrapped: ${app.name}`, app)
    })

    this.core.on('app-mount', (app) => {
      this.log('info', `Application mounted: ${app.name}`, app)
    })

    this.core.on('app-unmount', (app) => {
      this.log('info', `Application unmounted: ${app.name}`, app)
    })

    this.core.on('app-error', (error) => {
      this.log('error', `Application error: ${error.message}`, error)
    })
  }

  /**
   * 注入调试样式
   */
  private injectDebugStyles(): void {
    const style = document.createElement('style')
    style.textContent = `
      .micro-core-debug-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        height: 600px;
        background: #1e1e1e;
        color: #fff;
        border: 1px solid #333;
        border-radius: 8px;
        z-index: 10000;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      }
      
      .micro-core-debug-header {
        background: #333;
        padding: 10px;
        border-bottom: 1px solid #555;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .micro-core-debug-content {
        height: calc(100% - 50px);
        overflow-y: auto;
        padding: 10px;
      }
      
      .micro-core-debug-tab {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 5px;
        background: #555;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
      }
      
      .micro-core-debug-tab.active {
        background: #007acc;
      }
      
      .micro-core-debug-tab:hover {
        background: #666;
      }
      
      .micro-core-debug-log {
        margin-bottom: 5px;
        padding: 5px;
        border-radius: 3px;
        border-left: 3px solid #555;
      }
      
      .micro-core-debug-log.info {
        border-left-color: #007acc;
      }
      
      .micro-core-debug-log.warn {
        border-left-color: #ff9800;
      }
      
      .micro-core-debug-log.error {
        border-left-color: #f44336;
      }
      
      .micro-core-debug-log.debug {
        border-left-color: #4caf50;
      }
      
      .micro-core-debug-timestamp {
        color: #888;
        font-size: 10px;
      }
    `
    document.head.appendChild(style)
  }

  /**
   * 创建调试面板
   */
  private createDebugPanel(): void {
    this.debugPanel = document.createElement('div')
    this.debugPanel.className = 'micro-core-debug-panel'
    
    this.debugPanel.innerHTML = `
      <div class="micro-core-debug-header">
        <div>
          <span class="micro-core-debug-tab active" data-tab="logs">Logs</span>
          <span class="micro-core-debug-tab" data-tab="apps">Apps</span>
          <span class="micro-core-debug-tab" data-tab="plugins">Plugins</span>
          <span class="micro-core-debug-tab" data-tab="performance">Performance</span>
        </div>
        <button onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
      <div class="micro-core-debug-content" id="debug-content">
        <!-- 内容将通过JavaScript动态填充 -->
      </div>
    `

    // 添加标签页切换事件
    this.debugPanel.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (target.classList.contains('micro-core-debug-tab')) {
        // 移除所有活动状态
        this.debugPanel!.querySelectorAll('.micro-core-debug-tab').forEach(tab => {
          tab.classList.remove('active')
        })
        
        // 添加当前活动状态
        target.classList.add('active')
        
        // 更新内容
        this.updateDebugPanelContent(target.dataset.tab!)
      }
    })

    document.body.appendChild(this.debugPanel)
    this.updateDebugPanel()
  }

  /**
   * 更新调试面板
   */
  private updateDebugPanel(): void {
    if (!this.debugPanel) return

    const activeTab = this.debugPanel.querySelector('.micro-core-debug-tab.active')
    const tabName = activeTab?.getAttribute('data-tab') || 'logs'
    this.updateDebugPanelContent(tabName)
  }

  /**
   * 更新调试面板内容
   */
  private updateDebugPanelContent(tab: string): void {
    if (!this.debugPanel) return

    const content = this.debugPanel.querySelector('#debug-content')
    if (!content) return

    switch (tab) {
      case 'logs':
        content.innerHTML = this.renderLogs()
        break
      case 'apps':
        content.innerHTML = this.renderApplications()
        break
      case 'plugins':
        content.innerHTML = this.renderPlugins()
        break
      case 'performance':
        content.innerHTML = this.renderPerformance()
        break
    }
  }

  /**
   * 渲染日志
   */
  private renderLogs(): string {
    return this.logs.slice(-50).map(log => {
      const time = new Date(log.timestamp).toLocaleTimeString()
      return `
        <div class="micro-core-debug-log ${log.level}">
          <div class="micro-core-debug-timestamp">${time}</div>
          <div>${log.message}</div>
          ${log.data ? `<pre>${JSON.stringify(log.data, null, 2)}</pre>` : ''}
        </div>
      `
    }).join('')
  }

  /**
   * 渲染应用信息
   */
  private renderApplications(): string {
    const apps = this.getApplicationsInfo()
    return apps.map(app => `
      <div class="micro-core-debug-log info">
        <strong>${app.name}</strong> - ${app.status}
        <div>Entry: ${app.entry}</div>
        <div>Container: ${app.container}</div>
      </div>
    `).join('')
  }

  /**
   * 渲染插件信息
   */
  private renderPlugins(): string {
    const plugins = this.getPluginsInfo()
    return plugins.map(plugin => `
      <div class="micro-core-debug-log info">
        <strong>${plugin.name}</strong> v${plugin.version}
        <div>Status: ${plugin.enabled ? 'Enabled' : 'Disabled'}</div>
      </div>
    `).join('')
  }

  /**
   * 渲染性能信息