import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { PerformanceConfig, PerformanceMetrics, PerformanceEvent } from '../types/plugin'

/**
 * Performance插件 - 性能监控
 * 提供微前端应用的性能监控和优化功能
 */
export class PerformancePlugin extends PluginBase {
  public readonly name = 'performance'
  public readonly version = '0.1.0'

  private core: MicroCore | null = null
  private config: PerformanceConfig | null = null
  private metrics: PerformanceMetrics = {
    appLoadTimes: new Map(),
    memoryUsage: [],
    renderTimes: new Map(),
    bundleSizes: new Map()
  }
  private observer: PerformanceObserver | null = null
  private memoryMonitorInterval: number | null = null

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.setupPerformanceMonitoring()
    this.registerPerformanceAPI()
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.stopPerformanceMonitoring()
    this.metrics = {
      appLoadTimes: new Map(),
      memoryUsage: [],
      renderTimes: new Map(),
      bundleSizes: new Map()
    }
    this.core = null
    this.config = null
  }

  /**
   * 配置性能监控
   */
  public configure(config: PerformanceConfig): void {
    this.config = config
    
    // 重新设置监控
    this.stopPerformanceMonitoring()
    this.setupPerformanceMonitoring()
  }

  /**
   * 记录应用加载时间
   */
  public recordAppLoadTime(appName: string, startTime: number, endTime: number): void {
    const loadTime = endTime - startTime
    this.metrics.appLoadTimes.set(appName, loadTime)
    
    // 触发性能事件
    const event: PerformanceEvent = {
      type: 'app-load-time',
      appName,
      value: loadTime,
      timestamp: Date.now()
    }
    this.emit('performance-metric', event)
    
    // 检查性能阈值
    this.checkPerformanceThreshold('loadTime', loadTime, appName)
  }

  /**
   * 记录渲染时间
   */
  public recordRenderTime(appName: string, renderTime: number): void {
    this.metrics.renderTimes.set(appName, renderTime)
    
    // 触发性能事件
    const event: PerformanceEvent = {
      type: 'render-time',
      appName,
      value: renderTime,
      timestamp: Date.now()
    }
    this.emit('performance-metric', event)
    
    // 检查性能阈值
    this.checkPerformanceThreshold('renderTime', renderTime, appName)
  }

  /**
   * 记录包大小
   */
  public recordBundleSize(appName: string, size: number): void {
    this.metrics.bundleSizes.set(appName, size)
    
    // 触发性能事件
    const event: PerformanceEvent = {
      type: 'bundle-size',
      appName,
      value: size,
      timestamp: Date.now()
    }
    this.emit('performance-metric', event)
    
    // 检查性能阈值
    this.checkPerformanceThreshold('bundleSize', size, appName)
  }

  /**
   * 获取性能指标
   */
  public getMetrics(): PerformanceMetrics {
    return {
      appLoadTimes: new Map(this.metrics.appLoadTimes),
      memoryUsage: [...this.metrics.memoryUsage],
      renderTimes: new Map(this.metrics.renderTimes),
      bundleSizes: new Map(this.metrics.bundleSizes)
    }
  }

  /**
   * 获取应用加载时间
   */
  public getAppLoadTime(appName: string): number | undefined {
    return this.metrics.appLoadTimes.get(appName)
  }

  /**
   * 获取平均加载时间
   */
  public getAverageLoadTime(): number {
    const loadTimes = Array.from(this.metrics.appLoadTimes.values())
    if (loadTimes.length === 0) return 0
    
    const total = loadTimes.reduce((sum, time) => sum + time, 0)
    return total / loadTimes.length
  }

  /**
   * 获取内存使用情况
   */
  public getMemoryUsage(): Array<{ timestamp: number; usage: MemoryInfo }> {
    return [...this.metrics.memoryUsage]
  }

  /**
   * 获取最新内存使用
   */
  public getCurrentMemoryUsage(): MemoryInfo | null {
    if ('memory' in performance) {
      return (performance as any).memory
    }
    return null
  }

  /**
   * 清空性能指标
   */
  public clearMetrics(): void {
    this.metrics = {
      appLoadTimes: new Map(),
      memoryUsage: [],
      renderTimes: new Map(),
      bundleSizes: new Map()
    }
  }

  /**
   * 生成性能报告
   */
  public generateReport(): string {
    const metrics = this.getMetrics()
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalApps: metrics.appLoadTimes.size,
        averageLoadTime: this.getAverageLoadTime(),
        totalBundleSize: Array.from(metrics.bundleSizes.values()).reduce((sum, size) => sum + size, 0),
        memorySnapshots: metrics.memoryUsage.length
      },
      details: {
        appLoadTimes: Object.fromEntries(metrics.appLoadTimes),
        renderTimes: Object.fromEntries(metrics.renderTimes),
        bundleSizes: Object.fromEntries(metrics.bundleSizes),
        memoryUsage: metrics.memoryUsage.slice(-10) // 最近10个内存快照
      }
    }
    
    return JSON.stringify(report, null, 2)
  }

  /**
   * 设置性能监控
   */
  private setupPerformanceMonitoring(): void {
    // 设置Performance Observer
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.handlePerformanceEntry(entry)
        }
      })
      
      // 监控导航、资源加载、用户交互等
      try {
        this.observer.observe({ entryTypes: ['navigation', 'resource', 'measure', 'mark'] })
      } catch (error) {
        console.warn('[PerformancePlugin] PerformanceObserver setup failed:', error)
      }
    }
    
    // 设置内存监控
    this.setupMemoryMonitoring()
    
    // 监听应用生命周期事件
    this.setupAppLifecycleMonitoring()
  }

  /**
   * 停止性能监控
   */
  private stopPerformanceMonitoring(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    
    if (this.memoryMonitorInterval) {
      clearInterval(this.memoryMonitorInterval)
      this.memoryMonitorInterval = null
    }
  }

  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring(): void {
    const interval = this.config?.memoryMonitorInterval || 30000 // 默认30秒
    
    this.memoryMonitorInterval = window.setInterval(() => {
      const memoryInfo = this.getCurrentMemoryUsage()
      if (memoryInfo) {
        this.metrics.memoryUsage.push({
          timestamp: Date.now(),
          usage: memoryInfo
        })
        
        // 限制内存快照数量
        if (this.metrics.memoryUsage.length > 100) {
          this.metrics.memoryUsage = this.metrics.memoryUsage.slice(-50)
        }
        
        // 检查内存使用阈值
        this.checkMemoryThreshold(memoryInfo)
      }
    }, interval)
  }

  /**
   * 设置应用生命周期监控
   */
  private setupAppLifecycleMonitoring(): void {
    if (!this.core) return

    // 监听应用启动
    this.core.on('app-bootstrap-start', (app) => {
      performance.mark(`app-bootstrap-start-${app.name}`)
    })

    this.core.on('app-bootstrap-end', (app) => {
      performance.mark(`app-bootstrap-end-${app.name}`)
      performance.measure(
        `app-bootstrap-${app.name}`,
        `app-bootstrap-start-${app.name}`,
        `app-bootstrap-end-${app.name}`
      )
    })

    // 监听应用挂载
    this.core.on('app-mount-start', (app) => {
      performance.mark(`app-mount-start-${app.name}`)
    })

    this.core.on('app-mount-end', (app) => {
      performance.mark(`app-mount-end-${app.name}`)
      performance.measure(
        `app-mount-${app.name}`,
        `app-mount-start-${app.name}`,
        `app-mount-end-${app.name}`
      )
    })
  }

  /**
   * 处理性能条目
   */
  private handlePerformanceEntry(entry: PerformanceEntry): void {
    switch (entry.entryType) {
      case 'navigation':
        this.handleNavigationEntry(entry as PerformanceNavigationTiming)
        break
      case 'resource':
        this.handleResourceEntry(entry as PerformanceResourceTiming)
        break
      case 'measure':
        this.handleMeasureEntry(entry)
        break
      case 'mark':
        this.handleMarkEntry(entry)
        break
    }
  }

  /**
   * 处理导航性能条目
   */
  private handleNavigationEntry(entry: PerformanceNavigationTiming): void {
    const loadTime = entry.loadEventEnd - entry.navigationStart
    
    // 触发页面加载性能事件
    const event: PerformanceEvent = {
      type: 'page-load-time',
      value: loadTime,
      timestamp: Date.now()
    }
    this.emit('performance-metric', event)
  }

  /**
   * 处理资源性能条目
   */
  private handleResourceEntry(entry: PerformanceResourceTiming): void {
    const loadTime = entry.responseEnd - entry.startTime
    
    // 如果是应用资源，记录加载时间
    if (entry.name.includes('micro-app') || entry.name.includes('sub-app')) {
      const event: PerformanceEvent = {
        type: 'resource-load-time',
        value: loadTime,
        timestamp: Date.now(),
        details: {
          name: entry.name,
          size: entry.transferSize
        }
      }
      this.emit('performance-metric', event)
    }
  }

  /**
   * 处理测量性能条目
   */
  private handleMeasureEntry(entry: PerformanceEntry): void {
    // 处理应用相关的测量
    if (entry.name.startsWith('app-')) {
      const appName = entry.name.split('-').slice(2).join('-')
      
      if (entry.name.includes('bootstrap')) {
        this.recordAppLoadTime(appName, 0, entry.duration)
      } else if (entry.name.includes('mount')) {
        this.recordRenderTime(appName, entry.duration)
      }
    }
  }

  /**
   * 处理标记性能条目
   */
  private handleMarkEntry(entry: PerformanceEntry): void {
    // 可以在这里处理自定义标记
  }

  /**
   * 检查性能阈值
   */
  private checkPerformanceThreshold(metric: string, value: number, appName?: string): void {
    const thresholds = this.config?.thresholds
    if (!thresholds) return

    let threshold: number | undefined
    let warningMessage: string

    switch (metric) {
      case 'loadTime':
        threshold = thresholds.loadTime
        warningMessage = `应用 ${appName} 加载时间过长: ${value}ms`
        break
      case 'renderTime':
        threshold = thresholds.renderTime
        warningMessage = `应用 ${appName} 渲染时间过长: ${value}ms`
        break
      case 'bundleSize':
        threshold = thresholds.bundleSize
        warningMessage = `应用 ${appName} 包大小过大: ${value}bytes`
        break
      default:
        return
    }

    if (threshold && value > threshold) {
      console.warn(`[PerformancePlugin] ${warningMessage}`)
      
      // 触发性能警告事件
      const event: PerformanceEvent = {
        type: 'performance-warning',
        appName,
        value,
        timestamp: Date.now(),
        details: { metric, threshold }
      }
      this.emit('performance-warning', event)
    }
  }

  /**
   * 检查内存阈值
   */
  private checkMemoryThreshold(memoryInfo: MemoryInfo): void {
    const threshold = this.config?.thresholds?.memoryUsage
    if (!threshold) return

    const usagePercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100

    if (usagePercent > threshold) {
      console.warn(`[PerformancePlugin] 内存使用率过高: ${usagePercent.toFixed(2)}%`)
      
      // 触发内存警告事件
      const event: PerformanceEvent = {
        type: 'memory-warning',
        value: usagePercent,
        timestamp: Date.now(),
        details: { memoryInfo, threshold }
      }
      this.emit('memory-warning', event)
    }
  }

  /**
   * 注册性能API到核心
   */
  private registerPerformanceAPI(): void {
    if (!this.core) return

    // 注册全局性能API
    const performanceAPI = {
      recordAppLoadTime: this.recordAppLoadTime.bind(this),
      recordRenderTime: this.recordRenderTime.bind(this),
      recordBundleSize: this.recordBundleSize.bind(this),
      getMetrics: this.getMetrics.bind(this),
      getAppLoadTime: this.getAppLoadTime.bind(this),
      getAverageLoadTime: this.getAverageLoadTime.bind(this),
      getMemoryUsage: this.getMemoryUsage.bind(this),
      getCurrentMemoryUsage: this.getCurrentMemoryUsage.bind(this),
      clearMetrics: this.clearMetrics.bind(this),
      generateReport: this.generateReport.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_PERFORMANCE__ = performanceAPI
    }
  }
}

export default PerformancePlugin