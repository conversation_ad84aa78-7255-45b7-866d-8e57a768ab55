import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import { logger, MicroCoreError } from '@micro-core/shared'

/**
 * 通信插件配置
 */
export interface CommunicationPluginConfig {
  enableGlobalBus?: boolean
  enablePostMessage?: boolean
  enableBroadcastChannel?: boolean
  enableSharedWorker?: boolean
  namespace?: string
  timeout?: number
}

/**
 * 消息类型
 */
export interface Message {
  id: string
  type: string
  data: any
  from: string
  to?: string
  timestamp: number
  timeout?: number
}

/**
 * 消息处理器
 */
export type MessageHandler = (message: Message) => void | Promise<void>

/**
 * 通信插件
 * 提供微前端应用间的通信功能
 */
export class CommunicationPlugin extends PluginBase {
  name = 'communication'
  version = '0.1.0'

  private core!: MicroCore
  private config: CommunicationPluginConfig = {}
  private handlers = new Map<string, Set<MessageHandler>>()
  private broadcastChannel?: BroadcastChannel
  private sharedWorker?: SharedWorker
  private messageId = 0

  /**
   * 安装插件
   */
  async install(core: MicroCore, config?: CommunicationPluginConfig): Promise<void> {
    this.core = core
    this.config = {
      enableGlobalBus: true,
      enablePostMessage: true,
      enableBroadcastChannel: true,
      enableSharedWorker: false,
      namespace: 'micro-core',
      timeout: 5000,
      ...config
    }

    // 初始化通信通道
    await this.initCommunication()

    // 注册核心API
    this.registerAPI()

    logger.info('通信插件安装成功')
  }

  /**
   * 卸载插件
   */
  async uninstall(): Promise<void> {
    // 清理通信通道
    this.cleanupCommunication()

    // 清空处理器
    this.handlers.clear()

    logger.info('通信插件卸载成功')
  }

  /**
   * 初始化通信通道
   */
  private async initCommunication(): Promise<void> {
    // 初始化BroadcastChannel
    if (this.config.enableBroadcastChannel && 'BroadcastChannel' in window) {
      try {
        this.broadcastChannel = new BroadcastChannel(this.config.namespace!)
        this.broadcastChannel.addEventListener('message', this.handleBroadcastMessage)
        logger.debug('BroadcastChannel初始化成功')
      } catch (error) {
        logger.warn('BroadcastChannel初始化失败:', error)
      }
    }

    // 初始化SharedWorker
    if (this.config.enableSharedWorker && 'SharedWorker' in window) {
      try {
        // 这里需要一个SharedWorker脚本，暂时跳过实现
        logger.debug('SharedWorker支持已启用')
      } catch (error) {
        logger.warn('SharedWorker初始化失败:', error)
      }
    }

    // 初始化PostMessage监听
    if (this.config.enablePostMessage) {
      window.addEventListener('message', this.handlePostMessage)
      logger.debug('PostMessage监听器初始化成功')
    }
  }

  /**
   * 清理通信通道
   */
  private cleanupCommunication(): void {
    // 清理BroadcastChannel
    if (this.broadcastChannel) {
      this.broadcastChannel.removeEventListener('message', this.handleBroadcastMessage)
      this.broadcastChannel.close()
      this.broadcastChannel = undefined
    }

    // 清理PostMessage监听
    if (this.config.enablePostMessage) {
      window.removeEventListener('message', this.handlePostMessage)
    }

    // 清理SharedWorker
    if (this.sharedWorker) {
      this.sharedWorker.port.close()
      this.sharedWorker = undefined
    }
  }

  /**
   * 处理BroadcastChannel消息
   */
  private handleBroadcastMessage = (event: MessageEvent): void => {
    try {
      const message = event.data as Message
      this.processMessage(message)
    } catch (error) {
      logger.error('处理BroadcastChannel消息失败:', error)
    }
  }

  /**
   * 处理PostMessage消息
   */
  private handlePostMessage = (event: MessageEvent): void => {
    try {
      // 验证消息来源和格式
      if (!this.isValidMessage(event.data)) {
        return
      }

      const message = event.data as Message
      this.processMessage(message)
    } catch (error) {
      logger.error('处理PostMessage消息失败:', error)
    }
  }

  /**
   * 验证消息格式
   */
  private isValidMessage(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.id === 'string' &&
      typeof data.type === 'string' &&
      typeof data.from === 'string' &&
      typeof data.timestamp === 'number'
    )
  }

  /**
   * 处理消息
   */
  private processMessage(message: Message): void {
    // 检查消息是否过期
    if (message.timeout && Date.now() - message.timestamp > message.timeout) {
      logger.warn('消息已过期:', message)
      return
    }

    // 检查消息是否是发给当前应用的
    if (message.to && message.to !== this.getAppName()) {
      return
    }

    // 获取消息处理器
    const handlers = this.handlers.get(message.type)
    if (!handlers || handlers.size === 0) {
      logger.debug(`没有找到消息类型 "${message.type}" 的处理器`)
      return
    }

    // 执行所有处理器
    handlers.forEach(async (handler) => {
      try {
        await handler(message)
      } catch (error) {
        logger.error(`消息处理器执行失败 (类型: ${message.type}):`, error)
      }
    })
  }

  /**
   * 获取当前应用名称
   */
  private getAppName(): string {
    // 从核心实例获取应用名称，如果没有则使用默认值
    return (this.core as any).appName || 'main'
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `${this.getAppName()}-${Date.now()}-${++this.messageId}`
  }

  /**
   * 注册API
   */
  private registerAPI(): void {
    // 将通信API注册到核心实例
    Object.assign(this.core, {
      communication: {
        send: this.send.bind(this),
        broadcast: this.broadcast.bind(this),
        subscribe: this.subscribe.bind(this),
        unsubscribe: this.unsubscribe.bind(this),
        request: this.request.bind(this),
        reply: this.reply.bind(this)
      }
    })
  }

  /**
   * 发送消息到指定应用
   */
  send(to: string, type: string, data: any, timeout?: number): void {
    const message: Message = {
      id: this.generateMessageId(),
      type,
      data,
      from: this.getAppName(),
      to,
      timestamp: Date.now(),
      timeout: timeout || this.config.timeout
    }

    this.sendMessage(message)
  }

  /**
   * 广播消息到所有应用
   */
  broadcast(type: string, data: any, timeout?: number): void {
    const message: Message = {
      id: this.generateMessageId(),
      type,
      data,
      from: this.getAppName(),
      timestamp: Date.now(),
      timeout: timeout || this.config.timeout
    }

    this.sendMessage(message)
  }

  /**
   * 发送消息
   */
  private sendMessage(message: Message): void {
    try {
      // 通过BroadcastChannel发送
      if (this.broadcastChannel) {
        this.broadcastChannel.postMessage(message)
      }

      // 通过PostMessage发送到所有iframe
      if (this.config.enablePostMessage) {
        const iframes = document.querySelectorAll('iframe')
        iframes.forEach(iframe => {
          if (iframe.contentWindow) {
            iframe.contentWindow.postMessage(message, '*')
          }
        })

        // 发送到父窗口
        if (window.parent !== window) {
          window.parent.postMessage(message, '*')
        }
      }

      logger.debug('消息发送成功:', message)
    } catch (error) {
      logger.error('消息发送失败:', error)
      throw new MicroCoreError(
        '消息发送失败',
        'COMMUNICATION_SEND_ERROR',
        { message, error }
      )
    }
  }

  /**
   * 订阅消息类型
   */
  subscribe(type: string, handler: MessageHandler): () => void {
    if (!this.handlers.has(type)) {
      this.handlers.set(type, new Set())
    }

    const handlers = this.handlers.get(type)!
    handlers.add(handler)

    logger.debug(`订阅消息类型: ${type}`)

    // 返回取消订阅的函数
    return () => {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.handlers.delete(type)
      }
      logger.debug(`取消订阅消息类型: ${type}`)
    }
  }

  /**
   * 取消订阅消息类型
   */
  unsubscribe(type: string, handler?: MessageHandler): void {
    const handlers = this.handlers.get(type)
    if (!handlers) {
      return
    }

    if (handler) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.handlers.delete(type)
      }
    } else {
      // 取消所有该类型的订阅
      this.handlers.delete(type)
    }

    logger.debug(`取消订阅消息类型: ${type}`)
  }

  /**
   * 发送请求并等待响应
   */
  async request(to: string, type: string, data: any, timeout?: number): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestId = this.generateMessageId()
      const responseType = `${type}:response:${requestId}`
      
      // 设置响应超时
      const timeoutMs = timeout || this.config.timeout!
      const timer = setTimeout(() => {
        this.unsubscribe(responseType)
        reject(new MicroCoreError(
          '请求超时',
          'COMMUNICATION_REQUEST_TIMEOUT',
          { to, type, data, timeout: timeoutMs }
        ))
      }, timeoutMs)

      // 订阅响应
      const unsubscribe = this.subscribe(responseType, (message) => {
        clearTimeout(timer)
        unsubscribe()
        
        if (message.data && message.data.error) {
          reject(new MicroCoreError(
            message.data.error.message || '请求处理失败',
            message.data.error.code || 'COMMUNICATION_REQUEST_ERROR',
            message.data.error.context
          ))
        } else {
          resolve(message.data)
        }
      })

      // 发送请求
      this.send(to, type, {
        ...data,
        requestId,
        responseType
      }, timeoutMs)
    })
  }

  /**
   * 回复请求
   */
  reply(message: Message, data: any, error?: Error): void {
    if (!message.data || !message.data.requestId || !message.data.responseType) {
      logger.warn('无法回复消息：缺少请求信息', message)
      return
    }

    const responseData = error ? {
      error: {
        message: error.message,
        code: (error as any).code || 'UNKNOWN_ERROR',
        context: (error as any).context
      }
    } : data

    this.send(
      message.from,
      message.data.responseType,
      responseData
    )
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      subscribedTypes: Array.from(this.handlers.keys()),
      totalHandlers: Array.from(this.handlers.values()).reduce((sum, handlers) => sum + handlers.size, 0),
      channels: {
        broadcastChannel: !!this.broadcastChannel,
        postMessage: this.config.enablePostMessage,
        sharedWorker: !!this.sharedWorker
      }
    }
  }
}