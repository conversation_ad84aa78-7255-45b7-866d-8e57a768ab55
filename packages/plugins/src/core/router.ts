import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { RouterConfig, RouteChangeEvent, NavigationGuard } from '../types/plugin'

/**
 * Router插件 - 路由管理
 * 提供微前端应用的路由管理和导航功能
 */
export class RouterPlugin extends PluginBase {
  public readonly name = 'router'
  public readonly version = '0.1.0'

  private routes: Map<string, RouterConfig> = new Map()
  private guards: NavigationGuard[] = []
  private currentRoute: string | null = null
  private core: MicroCore | null = null

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.setupRouteListener()
    this.registerRouterAPI()
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.removeRouteListener()
    this.routes.clear()
    this.guards = []
    this.currentRoute = null
    this.core = null
  }

  /**
   * 注册路由
   */
  public registerRoute(path: string, config: RouterConfig): void {
    this.routes.set(path, config)
  }

  /**
   * 取消注册路由
   */
  public unregisterRoute(path: string): void {
    this.routes.delete(path)
  }

  /**
   * 导航到指定路由
   */
  public async navigate(path: string, state?: any): Promise<boolean> {
    const route = this.routes.get(path)
    if (!route) {
      console.warn(`[RouterPlugin] Route not found: ${path}`)
      return false
    }

    // 执行导航守卫
    const canNavigate = await this.executeGuards(path, this.currentRoute)
    if (!canNavigate) {
      return false
    }

    // 更新当前路由
    const previousRoute = this.currentRoute
    this.currentRoute = path

    // 触发路由变化事件
    const event: RouteChangeEvent = {
      type: 'route-change',
      from: previousRoute,
      to: path,
      state
    }

    this.emit('route-change', event)

    // 更新浏览器历史
    if (route.mode === 'history') {
      window.history.pushState(state, '', path)
    } else {
      window.location.hash = path
    }

    return true
  }

  /**
   * 返回上一页
   */
  public goBack(): void {
    window.history.back()
  }

  /**
   * 前进到下一页
   */
  public goForward(): void {
    window.history.forward()
  }

  /**
   * 替换当前路由
   */
  public async replace(path: string, state?: any): Promise<boolean> {
    const route = this.routes.get(path)
    if (!route) {
      console.warn(`[RouterPlugin] Route not found: ${path}`)
      return false
    }

    // 执行导航守卫
    const canNavigate = await this.executeGuards(path, this.currentRoute)
    if (!canNavigate) {
      return false
    }

    // 更新当前路由
    const previousRoute = this.currentRoute
    this.currentRoute = path

    // 触发路由变化事件
    const event: RouteChangeEvent = {
      type: 'route-replace',
      from: previousRoute,
      to: path,
      state
    }

    this.emit('route-replace', event)

    // 替换浏览器历史
    if (route.mode === 'history') {
      window.history.replaceState(state, '', path)
    } else {
      window.location.hash = path
    }

    return true
  }

  /**
   * 添加导航守卫
   */
  public addGuard(guard: NavigationGuard): void {
    this.guards.push(guard)
  }

  /**
   * 移除导航守卫
   */
  public removeGuard(guard: NavigationGuard): void {
    const index = this.guards.indexOf(guard)
    if (index > -1) {
      this.guards.splice(index, 1)
    }
  }

  /**
   * 获取当前路由
   */
  public getCurrentRoute(): string | null {
    return this.currentRoute
  }

  /**
   * 获取所有路由
   */
  public getRoutes(): RouterConfig[] {
    return Array.from(this.routes.values())
  }

  /**
   * 设置路由监听器
   */
  private setupRouteListener(): void {
    // 监听浏览器前进后退
    window.addEventListener('popstate', this.handlePopState.bind(this))
    
    // 监听hash变化
    window.addEventListener('hashchange', this.handleHashChange.bind(this))
  }

  /**
   * 移除路由监听器
   */
  private removeRouteListener(): void {
    window.removeEventListener('popstate', this.handlePopState.bind(this))
    window.removeEventListener('hashchange', this.handleHashChange.bind(this))
  }

  /**
   * 处理浏览器前进后退
   */
  private async handlePopState(event: PopStateEvent): Promise<void> {
    const path = window.location.pathname
    await this.handleRouteChange(path, event.state)
  }

  /**
   * 处理hash变化
   */
  private async handleHashChange(event: HashChangeEvent): Promise<void> {
    const hash = window.location.hash.slice(1)
    await this.handleRouteChange(hash)
  }

  /**
   * 处理路由变化
   */
  private async handleRouteChange(path: string, state?: any): Promise<void> {
    const route = this.routes.get(path)
    if (!route) {
      return
    }

    // 执行导航守卫
    const canNavigate = await this.executeGuards(path, this.currentRoute)
    if (!canNavigate) {
      return
    }

    const previousRoute = this.currentRoute
    this.currentRoute = path

    // 触发路由变化事件
    const event: RouteChangeEvent = {
      type: 'route-change',
      from: previousRoute,
      to: path,
      state
    }

    this.emit('route-change', event)
  }

  /**
   * 执行导航守卫
   */
  private async executeGuards(to: string, from: string | null): Promise<boolean> {
    for (const guard of this.guards) {
      try {
        const result = await guard(to, from)
        if (result === false) {
          return false
        }
      } catch (error) {
        console.error('[RouterPlugin] Navigation guard error:', error)
        return false
      }
    }
    return true
  }

  /**
   * 注册路由API到核心
   */
  private registerRouterAPI(): void {
    if (!this.core) return

    // 注册全局路由API
    const routerAPI = {
      navigate: this.navigate.bind(this),
      replace: this.replace.bind(this),
      goBack: this.goBack.bind(this),
      goForward: this.goForward.bind(this),
      getCurrentRoute: this.getCurrentRoute.bind(this),
      addGuard: this.addGuard.bind(this),
      removeGuard: this.removeGuard.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_ROUTER__ = routerAPI
    }
  }
}

export default RouterPlugin