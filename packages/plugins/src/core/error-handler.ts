import { PluginBase } from '../base/plugin-base'
import type { MicroCore } from '@micro-core/core'
import type { ErrorHandlerConfig, ErrorInfo, ErrorRecoveryStrategy } from '../types/plugin'

/**
 * ErrorHandler插件 - 错误处理
 * 提供微前端应用的错误捕获、处理和恢复功能
 */
export class ErrorHandlerPlugin extends PluginBase {
  public readonly name = 'error-handler'
  public readonly version = '0.1.0'

  private core: MicroCore | null = null
  private config: ErrorHandlerConfig | null = null
  private errors: ErrorInfo[] = []
  private recoveryStrategies: Map<string, ErrorRecoveryStrategy> = new Map()
  private originalErrorHandler: ((event: ErrorEvent) => void) | null = null
  private originalUnhandledRejectionHandler: ((event: PromiseRejectionEvent) => void) | null = null

  /**
   * 安装插件
   */
  public install(core: MicroCore): void {
    this.core = core
    this.setupErrorHandling()
    this.setupDefaultRecoveryStrategies()
    this.registerErrorHandlerAPI()
  }

  /**
   * 卸载插件
   */
  public uninstall(): void {
    this.removeErrorHandling()
    this.errors = []
    this.recoveryStrategies.clear()
    this.core = null
    this.config = null
  }

  /**
   * 配置错误处理
   */
  public configure(config: ErrorHandlerConfig): void {
    this.config = config
  }

  /**
   * 手动报告错误
   */
  public reportError(error: Error, context?: any): void {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      message: error.message,
      stack: error.stack || '',
      timestamp: Date.now(),
      type: 'manual',
      context,
      recovered: false
    }

    this.handleError(errorInfo)
  }

  /**
   * 报告应用错误
   */
  public reportAppError(appName: string, error: Error, context?: any): void {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      message: error.message,
      stack: error.stack || '',
      timestamp: Date.now(),
      type: 'app',
      appName,
      context,
      recovered: false
    }

    this.handleError(errorInfo)
  }

  /**
   * 获取错误列表
   */
  public getErrors(): ErrorInfo[] {
    return [...this.errors]
  }

  /**
   * 获取应用错误
   */
  public getAppErrors(appName: string): ErrorInfo[] {
    return this.errors.filter(error => error.appName === appName)
  }

  /**
   * 清空错误列表
   */
  public clearErrors(): void {
    this.errors = []
  }

  /**
   * 注册错误恢复策略
   */
  public registerRecoveryStrategy(errorType: string, strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.set(errorType, strategy)
  }

  /**
   * 移除错误恢复策略
   */
  public removeRecoveryStrategy(errorType: string): void {
    this.recoveryStrategies.delete(errorType)
  }

  /**
   * 尝试恢复错误
   */
  public async tryRecover(errorId: string): Promise<boolean> {
    const error = this.errors.find(e => e.id === errorId)
    if (!error || error.recovered) {
      return false
    }

    const strategy = this.recoveryStrategies.get(error.type)
    if (!strategy) {
      return false
    }

    try {
      const recovered = await strategy(error)
      if (recovered) {
        error.recovered = true
        error.recoveredAt = Date.now()
        
        // 触发恢复事件
        this.emit('error-recovered', error)
        
        return true
      }
    } catch (recoveryError) {
      console.error('[ErrorHandlerPlugin] Recovery failed:', recoveryError)
    }

    return false
  }

  /**
   * 设置错误处理
   */
  private setupErrorHandling(): void {
    // 保存原始错误处理器
    this.originalErrorHandler = window.onerror
    this.originalUnhandledRejectionHandler = window.onunhandledrejection

    // 设置全局错误处理器
    window.onerror = (message, source, lineno, colno, error) => {
      const errorInfo: ErrorInfo = {
        id: this.generateErrorId(),
        message: typeof message === 'string' ? message : 'Unknown error',
        stack: error?.stack || '',
        timestamp: Date.now(),
        type: 'javascript',
        source,
        lineno,
        colno,
        recovered: false
      }

      this.handleError(errorInfo)

      // 调用原始处理器
      if (this.originalErrorHandler) {
        return this.originalErrorHandler(message, source, lineno, colno, error)
      }

      return false
    }

    // 设置Promise拒绝处理器
    window.onunhandledrejection = (event) => {
      const errorInfo: ErrorInfo = {
        id: this.generateErrorId(),
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack || '',
        timestamp: Date.now(),
        type: 'promise',
        context: { reason: event.reason },
        recovered: false
      }

      this.handleError(errorInfo)

      // 调用原始处理器
      if (this.originalUnhandledRejectionHandler) {
        return this.originalUnhandledRejectionHandler(event)
      }
    }

    // 监听应用错误事件
    if (this.core) {
      this.core.on('app-error', (error) => {
        this.reportAppError(error.appName, error.error, error.context)
      })
    }
  }

  /**
   * 移除错误处理
   */
  private removeErrorHandling(): void {
    // 恢复原始错误处理器
    window.onerror = this.originalErrorHandler
    window.onunhandledrejection = this.originalUnhandledRejectionHandler
    
    this.originalErrorHandler = null
    this.originalUnhandledRejectionHandler = null
  }

  /**
   * 处理错误
   */
  private async handleError(errorInfo: ErrorInfo): Promise<void> {
    // 添加到错误列表
    this.errors.push(errorInfo)

    // 限制错误数量
    if (this.errors.length > 1000) {
      this.errors = this.errors.slice(-500)
    }

    // 触发错误事件
    this.emit('error-captured', errorInfo)

    // 输出到控制台
    this.logError(errorInfo)

    // 尝试自动恢复
    if (this.config?.autoRecover !== false) {
      await this.tryRecover(errorInfo.id)
    }

    // 上报错误（如果配置了）
    if (this.config?.reportEndpoint) {
      this.reportToEndpoint(errorInfo)
    }
  }

  /**
   * 输出错误到控制台
   */
  private logError(errorInfo: ErrorInfo): void {
    const prefix = `[ErrorHandlerPlugin ${new Date(errorInfo.timestamp).toLocaleTimeString()}]`
    
    console.group(`${prefix} ${errorInfo.type.toUpperCase()} ERROR`)
    console.error('Message:', errorInfo.message)
    
    if (errorInfo.appName) {
      console.error('App:', errorInfo.appName)
    }
    
    if (errorInfo.source) {
      console.error('Source:', errorInfo.source)
      console.error('Line:', errorInfo.lineno, 'Column:', errorInfo.colno)
    }
    
    if (errorInfo.stack) {
      console.error('Stack:', errorInfo.stack)
    }
    
    if (errorInfo.context) {
      console.error('Context:', errorInfo.context)
    }
    
    console.groupEnd()
  }

  /**
   * 上报错误到端点
   */
  private async reportToEndpoint(errorInfo: ErrorInfo): Promise<void> {
    if (!this.config?.reportEndpoint) return

    try {
      await fetch(this.config.reportEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...errorInfo,
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date(errorInfo.timestamp).toISOString()
        })
      })
    } catch (error) {
      console.error('[ErrorHandlerPlugin] Failed to report error:', error)
    }
  }

  /**
   * 设置默认恢复策略
   */
  private setupDefaultRecoveryStrategies(): void {
    // JavaScript错误恢复策略
    this.registerRecoveryStrategy('javascript', async (errorInfo) => {
      // 对于JavaScript错误，通常无法自动恢复
      return false
    })

    // Promise错误恢复策略
    this.registerRecoveryStrategy('promise', async (errorInfo) => {
      // 对于Promise错误，记录日志但不阻止应用运行
      console.warn('[ErrorHandlerPlugin] Promise rejection handled:', errorInfo.message)
      return true
    })

    // 应用错误恢复策略
    this.registerRecoveryStrategy('app', async (errorInfo) => {
      if (!errorInfo.appName || !this.core) {
        return false
      }

      try {
        // 尝试重新挂载应用
        console.log(`[ErrorHandlerPlugin] 尝试重新挂载应用: ${errorInfo.appName}`)
        
        // 这里需要调用核心的应用重新挂载方法
        // await this.core.remountApp(errorInfo.appName)
        
        return true
      } catch (error) {
        console.error(`[ErrorHandlerPlugin] 应用恢复失败: ${errorInfo.appName}`, error)
        return false
      }
    })

    // 网络错误恢复策略
    this.registerRecoveryStrategy('network', async (errorInfo) => {
      // 对于网络错误，可以尝试重新请求
      console.log('[ErrorHandlerPlugin] 网络错误已记录，建议检查网络连接')
      return true
    })
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 注册错误处理API到核心
   */
  private registerErrorHandlerAPI(): void {
    if (!this.core) return

    // 注册全局错误处理API
    const errorHandlerAPI = {
      reportError: this.reportError.bind(this),
      reportAppError: this.reportAppError.bind(this),
      getErrors: this.getErrors.bind(this),
      getAppErrors: this.getAppErrors.bind(this),
      clearErrors: this.clearErrors.bind(this),
      registerRecoveryStrategy: this.registerRecoveryStrategy.bind(this),
      removeRecoveryStrategy: this.removeRecoveryStrategy.bind(this),
      tryRecover: this.tryRecover.bind(this)
    }

    // 将API注册到全局
    if (typeof window !== 'undefined') {
      ;(window as any).__MICRO_CORE_ERROR_HANDLER__ = errorHandlerAPI
    }
  }
}

export default ErrorHandlerPlugin
