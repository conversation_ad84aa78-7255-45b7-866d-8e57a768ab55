import { describe, it, expect, beforeEach } from 'vitest'
import { PluginRegistry } from '../../src/market/registry'
import { PluginBase } from '../../src/base/plugin-base'
import type { MicroCore } from '@micro-core/core'

// 测试插件类
class TestPlugin extends PluginBase {
  public name = 'test-plugin'
  public version = '1.0.0'

  public async install(core: MicroCore): Promise<void> {
    // 测试安装逻辑
  }

  public async uninstall(): Promise<void> {
    // 测试卸载逻辑
  }
}

class AnotherTestPlugin extends PluginBase {
  public name = 'another-plugin'
  public version = '2.0.0'

  public async install(core: MicroCore): Promise<void> {
    // 测试安装逻辑
  }

  public async uninstall(): Promise<void> {
    // 测试卸载逻辑
  }
}

describe('PluginRegistry', () => {
  let registry: PluginRegistry

  beforeEach(() => {
    registry = new PluginRegistry()
  })

  describe('register', () => {
    it('应该成功注册插件', () => {
      const result = registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      expect(result).toBe(true)
    })

    it('应该拒绝重复注册相同版本的插件', () => {
      const metadata = {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      }

      registry.register('test-plugin', TestPlugin, metadata)
      const result = registry.register('test-plugin', TestPlugin, metadata)

      expect(result).toBe(false)
    })

    it('应该允许注册相同插件的不同版本', () => {
      const metadata1 = {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      }

      const metadata2 = {
        name: 'test-plugin',
        version: '2.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      }

      const result1 = registry.register('test-plugin', TestPlugin, metadata1)
      const result2 = registry.register('test-plugin', TestPlugin, metadata2)

      expect(result1).toBe(true)
      expect(result2).toBe(true)
    })
  })

  describe('get', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })
    })

    it('应该能获取已注册的插件', () => {
      const plugin = registry.get('test-plugin', '1.0.0')
      
      expect(plugin).toBeDefined()
      expect(plugin?.metadata.name).toBe('test-plugin')
      expect(plugin?.metadata.version).toBe('1.0.0')
    })

    it('应该在未指定版本时返回最新版本', () => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '2.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      const plugin = registry.get('test-plugin')
      
      expect(plugin).toBeDefined()
      expect(plugin?.metadata.version).toBe('2.0.0')
    })

    it('应该在插件不存在时返回undefined', () => {
      const plugin = registry.get('non-existent-plugin')
      
      expect(plugin).toBeUndefined()
    })
  })

  describe('has', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })
    })

    it('应该正确检查插件是否存在', () => {
      expect(registry.has('test-plugin', '1.0.0')).toBe(true)
      expect(registry.has('test-plugin')).toBe(true)
      expect(registry.has('non-existent-plugin')).toBe(false)
    })
  })

  describe('unregister', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })
    })

    it('应该成功注销插件', () => {
      const result = registry.unregister('test-plugin', '1.0.0')
      
      expect(result).toBe(true)
      expect(registry.has('test-plugin', '1.0.0')).toBe(false)
    })

    it('应该在插件不存在时返回false', () => {
      const result = registry.unregister('non-existent-plugin')
      
      expect(result).toBe(false)
    })
  })

  describe('list', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      registry.register('another-plugin', AnotherTestPlugin, {
        name: 'another-plugin',
        version: '2.0.0',
        description: '另一个测试插件',
        author: 'Another Author',
        category: 'test'
      })
    })

    it('应该返回所有已注册的插件', () => {
      const plugins = registry.list()
      
      expect(plugins).toHaveLength(2)
      expect(plugins.some(p => p.name === 'test-plugin')).toBe(true)
      expect(plugins.some(p => p.name === 'another-plugin')).toBe(true)
    })
  })

  describe('search', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      registry.register('another-plugin', AnotherTestPlugin, {
        name: 'another-plugin',
        version: '2.0.0',
        description: '另一个测试插件',
        author: 'Another Author',
        category: 'utility'
      })
    })

    it('应该能按名称搜索插件', () => {
      const results = registry.search('test')
      
      expect(results).toHaveLength(2)
    })

    it('应该能按描述搜索插件', () => {
      const results = registry.search('另一个')
      
      expect(results).toHaveLength(1)
      expect(results[0]?.name).toBe('another-plugin')
    })

    it('应该能按分类搜索插件', () => {
      const results = registry.search('utility')
      
      expect(results).toHaveLength(1)
      expect(results[0]?.name).toBe('another-plugin')
    })

    it('应该在没有匹配结果时返回空数组', () => {
      const results = registry.search('non-existent')
      
      expect(results).toHaveLength(0)
    })
  })

  describe('getVersions', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '2.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })
    })

    it('应该返回插件的所有版本', () => {
      const versions = registry.getVersions('test-plugin')
      
      expect(versions).toHaveLength(2)
      expect(versions).toContain('1.0.0')
      expect(versions).toContain('2.0.0')
    })

    it('应该在插件不存在时返回空数组', () => {
      const versions = registry.getVersions('non-existent-plugin')
      
      expect(versions).toHaveLength(0)
    })
  })

  describe('clear', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })
    })

    it('应该清空所有已注册的插件', () => {
      registry.clear()
      
      expect(registry.list()).toHaveLength(0)
      expect(registry.has('test-plugin')).toBe(false)
    })
  })

  describe('getStatistics', () => {
    beforeEach(() => {
      registry.register('test-plugin', TestPlugin, {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        category: 'test'
      })

      registry.register('another-plugin', AnotherTestPlugin, {
        name: 'another-plugin',
        version: '2.0.0',
        description: '另一个测试插件',
        author: 'Another Author',
        category: 'utility'
      })
    })

    it('应该返回正确的统计信息', () => {
      const stats = registry.getStatistics()
      
      expect(stats.totalPlugins).toBe(2)
      expect(stats.totalVersions).toBe(2)
      expect(stats.categories.test).toBe(1)
      expect(stats.categories.utility).toBe(1)
      expect(stats.authors['Test Author']).toBe(1)
      expect(stats.authors['Another Author']).toBe(1)
    })
  })
})