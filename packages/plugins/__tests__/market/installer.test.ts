import { describe, it, expect, beforeEach, vi } from 'vitest'
import { PluginInstaller } from '../../src/market/installer'
import { PluginRegistry } from '../../src/market/registry'
import { PluginValidator } from '../../src/market/validator'
import { PluginBase } from '../../src/base/plugin-base'
import type { MicroCore } from '@micro-core/core'

// Mock 依赖
vi.mock('../../src/market/registry')
vi.mock('../../src/market/validator')

// 测试插件类
class TestPlugin extends PluginBase {
  public name = 'test-plugin'
  public version = '1.0.0'

  public async install(core: MicroCore): Promise<void> {
    // 测试安装逻辑
  }

  public async uninstall(): Promise<void> {
    // 测试卸载逻辑
  }
}

// Mock MicroCore
const mockCore = {
  use: vi.fn()
} as unknown as MicroCore

describe('PluginInstaller', () => {
  let installer: PluginInstaller
  let mockRegistry: any
  let mockValidator: any

  beforeEach(() => {
    installer = new PluginInstaller(mockCore)
    
    // 重置 mocks
    mockRegistry = {
      get: vi.fn(),
      has: vi.fn()
    }
    
    mockValidator = {
      validate: vi.fn()
    }

    // 设置默认的 mock 返回值
    mockRegistry.get.mockReturnValue({
      metadata: {
        name: 'test-plugin',
        version: '1.0.0',
        description: '测试插件',
        author: 'Test Author',
        dependencies: {}
      },
      plugin: TestPlugin
    })

    mockValidator.validate.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: [],
      details: []
    })

    // 替换实际的实例
    vi.mocked(PluginRegistry.prototype.get).mockImplementation(mockRegistry.get)
    vi.mocked(PluginValidator.prototype.validate).mockImplementation(mockValidator.validate)
  })

  describe('install', () => {
    it('应该成功安装插件', async () => {
      const result = await installer.install('test-plugin', '1.0.0')

      expect(result.success).toBe(true)
      expect(result.pluginName).toBe('test-plugin')
      expect(result.pluginVersion).toBe('1.0.0')
      expect(mockCore.use).toHaveBeenCalled()
    })

    it('应该在插件不存在时返回失败', async () => {
      mockRegistry.get.mockReturnValue(null)

      const result = await installer.install('non-existent-plugin')

      expect(result.success).toBe(false)
      expect(result.error).toBe('插件不存在')
    })

    it('应该在插件已安装且非强制模式时返回失败', async () => {
      // 先安装一次
      await installer.install('test-plugin', '1.0.0')

      // 再次安装
      const result = await installer.install('test-plugin', '1.0.0')

      expect(result.success).toBe(false)
      expect(result.error).toBe('插件已安装')
    })

    it('应该在强制模式下重新安装已安装的插件', async () => {
      // 先安装一次
      await installer.install('test-plugin', '1.0.0')

      // 强制重新安装
      const result = await installer.install('test-plugin', '1.0.0', { force: true })

      expect(result.success).toBe(true)
    })

    it('应该在验证失败时返回失败', async () => {
      mockValidator.validate.mockResolvedValue({
        isValid: false,
        errors: ['验证失败'],
        warnings: [],
        details: []
      })

      const result = await installer.install('test-plugin', '1.0.0')

      expect(result.success).toBe(false)
      expect(result.error).toContain('插件验证失败')
    })

    it('应该在跳过验证时不进行验证', async () => {
      const result = await installer.install('test-plugin', '1.0.0', { 
        skipValidation: true 
      })

      expect(result.success).toBe(true)
      expect(mockValidator.validate).not.toHaveBeenCalled()
    })
  })

  describe('uninstall', () => {
    beforeEach(async () => {
      // 先安装插件
      await installer.install('test-plugin', '1.0.0')
    })

    it('应该成功卸载插件', async () => {
      const result = await installer.uninstall('test-plugin', '1.0.0')

      expect(result.success).toBe(true)
      expect(result.pluginName).toBe('test-plugin')
      expect(result.pluginVersion).toBe('1.0.0')
    })

    it('应该在插件未安装时返回失败', async () => {
      const result = await installer.uninstall('non-existent-plugin')

      expect(result.success).toBe(false)
      expect(result.error).toBe('插件未安装')
    })
  })

  describe('installBatch', () => {
    it('应该批量安装多个插件', async () => {
      const plugins = [
        { name: 'test-plugin', version: '1.0.0' },
        { name: 'test-plugin', version: '2.0.0' }
      ]

      // Mock 不同版本的插件
      mockRegistry.get.mockImplementation((name: string, version: string) => ({
        metadata: {
          name,
          version,
          description: '测试插件',
          author: 'Test Author',
          dependencies: {}
        },
        plugin: TestPlugin
      }))

      const results = await installer.installBatch(plugins)

      expect(results).toHaveLength(2)
      expect(results[0]?.success).toBe(true)
      expect(results[1]?.success).toBe(true)
    })

    it('应该在某个插件安装失败时停止后续安装', async () => {
      const plugins = [
        { name: 'test-plugin', version: '1.0.0' },
        { name: 'non-existent-plugin', version: '1.0.0' }
      ]

      mockRegistry.get.mockImplementation((name: string) => {
        if (name === 'non-existent-plugin') {
          return null
        }
        return {
          metadata: {
            name,
            version: '1.0.0',
            description: '测试插件',
            author: 'Test Author',
            dependencies: {}
          },
          plugin: TestPlugin
        }
      })

      const results = await installer.installBatch(plugins)

      expect(results).toHaveLength(2)
      expect(results[0]?.success).toBe(true)
      expect(results[1]?.success).toBe(false)
    })
  })

  describe('upgrade', () => {
    it('应该成功升级插件', async () => {
      // 先安装旧版本
      await installer.install('test-plugin', '1.0.0')

      // Mock 新版本
      mockRegistry.get.mockReturnValue({
        metadata: {
          name: 'test-plugin',
          version: '2.0.0',
          description: '测试插件',
          author: 'Test Author',
          dependencies: {}
        },
        plugin: TestPlugin
      })

      mockRegistry.getVersions = vi.fn().mockReturnValue(['1.0.0'])

      const result = await installer.upgrade('test-plugin', '2.0.0')

      expect(result.success).toBe(true)
      expect(result.pluginVersion).toBe('2.0.0')
    })
  })

  describe('getInstalledPlugins', () => {
    it('应该返回已安装插件列表', async () => {
      await installer.install('test-plugin', '1.0.0')

      const installed = installer.getInstalledPlugins()

      expect(installed).toHaveLength(1)
      expect(installed[0]?.name).toBe('test-plugin')
      expect(installed[0]?.version).toBe('1.0.0')
    })
  })

  describe('isInstalled', () => {
    it('应该正确检查插件是否已安装', async () => {
      expect(installer.isInstalled('test-plugin', '1.0.0')).toBe(false)

      await installer.install('test-plugin', '1.0.0')

      expect(installer.isInstalled('test-plugin', '1.0.0')).toBe(true)
    })
  })

  describe('getPluginInstance', () => {
    it('应该返回已安装插件的实例', async () => {
      await installer.install('test-plugin', '1.0.0')

      const instance = installer.getPluginInstance('test-plugin', '1.0.0')

      expect(instance).toBeInstanceOf(TestPlugin)
    })

    it('应该在插件未安装时返回undefined', () => {
      const instance = installer.getPluginInstance('non-existent-plugin')

      expect(instance).toBeUndefined()
    })
  })

  describe('queueInstall', () => {
    it('应该将插件添加到安装队列', async () => {
      const promise = installer.queueInstall('test-plugin', '1.0.0')

      const status = installer.getQueueStatus()
      expect(status.length).toBeGreaterThan(0)

      const result = await promise
      expect(result.success).toBe(true)
    })
  })

  describe('clearQueue', () => {
    it('应该清空安装队列', () => {
      installer.queueInstall('test-plugin', '1.0.0')
      installer.clearQueue()

      const status = installer.getQueueStatus()
      expect(status.length).toBe(0)
      expect(status.isProcessing).toBe(false)
    })
  })
})