import { describe, it, expect, beforeEach } from 'vitest'
import { PluginValidator } from '../../src/market/validator'
import { PluginBase } from '../../src/base/plugin-base'
import type { MicroCore } from '@micro-core/core'

// 测试插件类
class ValidTestPlugin extends PluginBase {
  public name = 'valid-test-plugin'
  public version = '1.0.0'

  public async install(core: MicroCore): Promise<void> {
    // 测试安装逻辑
  }

  public async uninstall(): Promise<void> {
    // 测试卸载逻辑
  }
}

class InvalidTestPlugin {
  // 缺少必要的方法和属性
}

class DangerousPlugin extends PluginBase {
  public name = 'dangerous-plugin'
  public version = '1.0.0'

  public async install(core: MicroCore): Promise<void> {
    // 包含危险代码
    eval('console.log("dangerous")')
  }

  public async uninstall(): Promise<void> {
    // 测试卸载逻辑
  }
}

describe('PluginValidator', () => {
  let validator: PluginValidator

  beforeEach(() => {
    validator = new PluginValidator()
  })

  describe('validate', () => {
    it('应该验证有效的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'valid-test-plugin',
          version: '1.0.0',
          description: '有效的测试插件',
          author: 'Test Author',
          category: 'test'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('应该拒绝元数据不完整的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'incomplete-plugin',
          version: '1.0.0',
          // 缺少 description 和 author
        } as any,
        plugin: ValidTestPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(error => error.includes('元数据不完整'))).toBe(true)
    })

    it('应该拒绝版本格式不正确的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'invalid-version-plugin',
          version: 'invalid-version',
          description: '版本格式不正确的插件',
          author: 'Test Author'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.warnings.some(warning => warning.includes('版本号格式不正确'))).toBe(true)
    })

    it('应该拒绝插件类无效的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'invalid-class-plugin',
          version: '1.0.0',
          description: '插件类无效的插件',
          author: 'Test Author'
        },
        plugin: InvalidTestPlugin as any
      }

      const result = await validator.validate(pluginPackage)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(error => error.includes('缺少必要的方法'))).toBe(true)
    })

    it('应该检测名称不一致的插件', async () => {
      class InconsistentNamePlugin extends PluginBase {
        public name = 'different-name'
        public version = '1.0.0'

        public async install(core: MicroCore): Promise<void> {}
        public async uninstall(): Promise<void> {}
      }

      const pluginPackage = {
        metadata: {
          name: 'original-name',
          version: '1.0.0',
          description: '名称不一致的插件',
          author: 'Test Author'
        },
        plugin: InconsistentNamePlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.warnings.some(warning => warning.includes('名称不一致'))).toBe(true)
    })

    it('应该检测版本不一致的插件', async () => {
      class InconsistentVersionPlugin extends PluginBase {
        public name = 'test-plugin'
        public version = '2.0.0'

        public async install(core: MicroCore): Promise<void> {}
        public async uninstall(): Promise<void> {}
      }

      const pluginPackage = {
        metadata: {
          name: 'test-plugin',
          version: '1.0.0',
          description: '版本不一致的插件',
          author: 'Test Author'
        },
        plugin: InconsistentVersionPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.warnings.some(warning => warning.includes('版本不一致'))).toBe(true)
    })
  })

  describe('quickValidate', () => {
    it('应该快速验证有效的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'valid-test-plugin',
          version: '1.0.0',
          description: '有效的测试插件',
          author: 'Test Author'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.quickValidate(pluginPackage)

      expect(result).toBe(true)
    })

    it('应该快速拒绝无效的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'invalid-plugin',
          version: '1.0.0'
          // 缺少必要字段
        } as any,
        plugin: ValidTestPlugin
      }

      const result = await validator.quickValidate(pluginPackage)

      expect(result).toBe(false)
    })
  })

  describe('securityCheck', () => {
    it('应该通过安全的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'safe-plugin',
          version: '1.0.0',
          description: '安全的插件',
          author: 'Test Author'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.securityCheck(pluginPackage)

      expect(result.isSafe).toBe(true)
      expect(result.riskLevel).toBe('low')
    })

    it('应该检测危险的插件', async () => {
      const pluginPackage = {
        metadata: {
          name: 'dangerous-plugin',
          version: '1.0.0',
          description: '危险的插件',
          author: 'Test Author'
        },
        plugin: DangerousPlugin
      }

      const result = await validator.securityCheck(pluginPackage)

      expect(result.isSafe).toBe(false)
      expect(result.riskLevel).toBe('high')
      expect(result.issues.some(issue => issue.type === 'dynamic_code')).toBe(true)
    })

    it('应该检测网络请求', async () => {
      class NetworkPlugin extends PluginBase {
        public name = 'network-plugin'
        public version = '1.0.0'

        public async install(core: MicroCore): Promise<void> {
          fetch('/api/data')
        }

        public async uninstall(): Promise<void> {}
      }

      const pluginPackage = {
        metadata: {
          name: 'network-plugin',
          version: '1.0.0',
          description: '网络请求插件',
          author: 'Test Author'
        },
        plugin: NetworkPlugin
      }

      const result = await validator.securityCheck(pluginPackage)

      expect(result.issues.some(issue => issue.type === 'network_request')).toBe(true)
    })

    it('应该检测存储访问', async () => {
      class StoragePlugin extends PluginBase {
        public name = 'storage-plugin'
        public version = '1.0.0'

        public async install(core: MicroCore): Promise<void> {
          localStorage.setItem('key', 'value')
        }

        public async uninstall(): Promise<void> {}
      }

      const pluginPackage = {
        metadata: {
          name: 'storage-plugin',
          version: '1.0.0',
          description: '存储访问插件',
          author: 'Test Author'
        },
        plugin: StoragePlugin
      }

      const result = await validator.securityCheck(pluginPackage)

      expect(result.issues.some(issue => issue.type === 'storage_access')).toBe(true)
    })
  })

  describe('addRule', () => {
    it('应该能添加自定义验证规则', async () => {
      const customRule = {
        name: 'custom_rule',
        description: '自定义规则',
        validate: () => false,
        errorMessage: '自定义规则失败',
        critical: true
      }

      validator.addRule(customRule)

      const pluginPackage = {
        metadata: {
          name: 'test-plugin',
          version: '1.0.0',
          description: '测试插件',
          author: 'Test Author'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(error => error.includes('自定义规则失败'))).toBe(true)
    })
  })

  describe('addSecurityRule', () => {
    it('应该能添加自定义安全规则', async () => {
      const customSecurityRule = {
        name: 'custom_security_rule',
        description: '自定义安全规则',
        validate: () => false,
        errorMessage: '自定义安全规则失败',
        critical: true
      }

      validator.addSecurityRule(customSecurityRule)

      const pluginPackage = {
        metadata: {
          name: 'test-plugin',
          version: '1.0.0',
          description: '测试插件',
          author: 'Test Author'
        },
        plugin: ValidTestPlugin
      }

      const result = await validator.validate(pluginPackage)

      expect(result.isValid).toBe(false)
      expect(result.errors.some(error => error.includes('自定义安全规则失败'))).toBe(true)
    })
  })

  describe('removeRule', () => {
    it('应该能移除验证规则', async () => {
      const rules = validator.getRules()
      const initialCount = rules.length

      validator.removeRule('metadata_completeness')

      const newRules = validator.getRules()
      expect(newRules.length).toBe(initialCount - 1)
      expect(newRules.some(rule => rule.name === 'metadata_completeness')).toBe(false)
    })
  })

  describe('removeSecurityRule', () => {
    it('应该能移除安全规则', async () => {
      const rules = validator.getSecurityRules()
      const initialCount = rules.length

      validator.removeSecurityRule('code_injection')

      const newRules = validator.getSecurityRules()
      expect(newRules.length).toBe(initialCount - 1)
      expect(newRules.some(rule => rule.name === 'code_injection')).toBe(false)
    })
  })

  describe('getRules', () => {
    it('应该返回所有验证规则', () => {
      const rules = validator.getRules()

      expect(rules.length).toBeGreaterThan(0)
      expect(rules.some(rule => rule.name === 'metadata_completeness')).toBe(true)
      expect(rules.some(rule => rule.name === 'version_format')).toBe(true)
      expect(rules.some(rule => rule.name === 'plugin_class')).toBe(true)
    })
  })

  describe('getSecurityRules', () => {
    it('应该返回所有安全规则', () => {
      const rules = validator.getSecurityRules()

      expect(rules.length).toBeGreaterThan(0)
      expect(rules.some(rule => rule.name === 'code_injection')).toBe(true)
      expect(rules.some(rule => rule.name === 'prototype_pollution')).toBe(true)
    })
  })
})