// 导出所有类型定义
export * from './kernel'
export * from './plugin'
export * from './application'
export * from './resource'

// 重新导出常用类型
export type { MicroApp, AppConfig, AppStatus } from './application'
export type { Plugin, PluginConfig, PluginHook } from './plugin'
export type { ResourceLoadOptions, ResourceType } from './resource'
export type { MicroKernelOptions } from './kernel'

// 微前端核心配置选项
export interface MicroCoreOptions {
  /** 生命周期配置 */
  lifecycle?: any
  /** 插件配置 */
  plugins?: any
  /** 应用配置 */
  application?: any
  /** 资源配置 */
  resource?: any
}