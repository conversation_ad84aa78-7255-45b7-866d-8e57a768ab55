/**
 * 插件系统类型定义
 */

/**
 * 插件状态枚举
 */
export enum PluginStatus {
  /** 未安装 */
  NOT_INSTALLED = 'not_installed',
  /** 已安装 */
  INSTALLED = 'installed',
  /** 已激活 */
  ACTIVATED = 'activated',
  /** 已停用 */
  DEACTIVATED = 'deactivated',
  /** 错误状态 */
  ERROR = 'error'
}

/**
 * 插件类型枚举
 */
export enum PluginType {
  /** 核心插件 */
  CORE = 'core',
  /** 扩展插件 */
  EXTENSION = 'extension',
  /** 主题插件 */
  THEME = 'theme',
  /** 工具插件 */
  UTILITY = 'utility'
}

/**
 * 插件配置接口
 */
export interface PluginConfig {
  /** 插件名称 */
  name: string
  /** 插件版本 */
  version: string
  /** 插件描述 */
  description?: string
  /** 插件作者 */
  author?: string
  /** 插件类型 */
  type?: PluginType
  /** 插件依赖 */
  dependencies?: string[]
  /** 插件配置 */
  config?: Record<string, any>
  /** 是否启用 */
  enabled?: boolean
}

/**
 * 插件钩子类型
 */
export type PluginHookType = 
  | 'beforeAppLoad'
  | 'afterAppLoad'
  | 'beforeAppMount'
  | 'afterAppMount'
  | 'beforeAppUnmount'
  | 'afterAppUnmount'
  | 'onAppError'
  | 'onRouteChange'
  | 'onPluginInstall'
  | 'onPluginUninstall'

/**
 * 插件钩子函数
 */
export type PluginHook = (context: PluginContext, ...args: any[]) => Promise<void> | void

/**
 * 插件上下文
 */
export interface PluginContext {
  /** 应用管理器 */
  applicationManager?: any
  /** 资源管理器 */
  resourceManager?: any
  /** 生命周期调度器 */
  lifecycleScheduler?: any
  /** 插件配置 */
  config: PluginConfig
  /** 插件实例 */
  instance: Plugin
}

/**
 * 插件接口
 */
export interface Plugin {
  /** 插件配置 */
  readonly config: PluginConfig
  /** 插件状态 */
  status: PluginStatus
  /** 插件上下文 */
  context?: PluginContext
  
  /** 安装插件 */
  install(context: PluginContext): Promise<void> | void
  /** 卸载插件 */
  uninstall?(context: PluginContext): Promise<void> | void
  /** 激活插件 */
  activate?(context: PluginContext): Promise<void> | void
  /** 停用插件 */
  deactivate?(context: PluginContext): Promise<void> | void
  /** 更新插件 */
  update?(context: PluginContext): Promise<void> | void
  
  /** 注册钩子 */
  registerHooks?(): Record<PluginHookType, PluginHook>
}

/**
 * 插件管理器选项
 */
export interface PluginManagerOptions {
  /** 是否启用插件系统 */
  enabled?: boolean
  /** 插件目录 */
  pluginDir?: string
  /** 是否自动加载插件 */
  autoLoad?: boolean
  /** 插件白名单 */
  whitelist?: string[]
  /** 插件黑名单 */
  blacklist?: string[]
}

/**
 * 插件注册表接口
 */
export interface IPluginRegistry {
  /** 注册插件 */
  register(plugin: Plugin): void
  /** 注销插件 */
  unregister(name: string): void
  /** 获取插件 */
  getPlugin(name: string): Plugin | undefined
  /** 获取所有插件 */
  getAllPlugins(): Plugin[]
  /** 获取已激活插件 */
  getActivePlugins(): Plugin[]
  /** 检查插件是否存在 */
  hasPlugin(name: string): boolean
}

/**
 * 插件加载器接口
 */
export interface IPluginLoader {
  /** 加载插件 */
  loadPlugin(name: string): Promise<Plugin>
  /** 卸载插件 */
  unloadPlugin(name: string): Promise<void>
  /** 重新加载插件 */
  reloadPlugin(name: string): Promise<Plugin>
  /** 批量加载插件 */
  loadPlugins(names: string[]): Promise<Plugin[]>
}

/**
 * 插件钩子管理器接口
 */
export interface IPluginHooks {
  /** 注册钩子 */
  registerHook(type: PluginHookType, hook: PluginHook, plugin: Plugin): void
  /** 注销钩子 */
  unregisterHook(type: PluginHookType, plugin: Plugin): void
  /** 执行钩子 */
  executeHook(type: PluginHookType, context: PluginContext, ...args: any[]): Promise<void>
  /** 获取钩子列表 */
  getHooks(type: PluginHookType): PluginHook[]
}

/**
 * 插件事件类型
 */
export type PluginEventType = 
  | 'installed'
  | 'uninstalled'
  | 'activated'
  | 'deactivated'
  | 'updated'
  | 'error'

/**
 * 插件事件数据
 */
export interface PluginEventData {
  /** 插件名称 */
  pluginName: string
  /** 事件类型 */
  type: PluginEventType
  /** 事件时间戳 */
  timestamp: number
  /** 事件数据 */
  data?: any
  /** 错误信息 */
  error?: Error
}

/**
 * 插件依赖信息
 */
export interface PluginDependency {
  /** 依赖名称 */
  name: string
  /** 依赖版本 */
  version: string
  /** 是否可选 */
  optional?: boolean
  /** 依赖描述 */
  description?: string
}

/**
 * 插件元数据
 */
export interface PluginMetadata {
  /** 插件ID */
  id: string
  /** 插件配置 */
  config: PluginConfig
  /** 插件依赖 */
  dependencies: PluginDependency[]
  /** 安装时间 */
  installedAt: number
  /** 最后更新时间 */
  updatedAt: number
  /** 插件大小 */
  size?: number
  /** 插件路径 */
  path?: string
}
