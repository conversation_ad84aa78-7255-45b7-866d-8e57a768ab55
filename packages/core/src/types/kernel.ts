import type { AppConfig } from './application'

/**
 * 微内核配置选项
 */
export interface MicroKernelOptions {
  /** 是否启用沙箱 */
  sandbox?: boolean
  /** 沙箱策略 */
  sandboxStrategy?: 'proxy' | 'define-property' | 'iframe' | 'web-component' | 'namespace' | 'federation'
  /** 是否启用预加载 */
  prefetch?: boolean
  /** 预加载策略 */
  prefetchStrategy?: 'idle' | 'visible' | 'hover' | 'load'
  /** 是否启用缓存 */
  cache?: boolean
  /** 缓存策略 */
  cacheStrategy?: 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB'
  /** 是否启用性能监控 */
  performance?: boolean
  /** 是否启用错误监控 */
  errorMonitoring?: boolean
  /** 自定义配置 */
  [key: string]: any
}

/**
 * 生命周期调度器接口
 */
export interface ILifecycleScheduler {
  /** 引导应用 */
  bootstrap(app: AppConfig): Promise<void>
  /** 挂载应用 */
  mount(app: AppConfig): Promise<void>
  /** 卸载应用 */
  unmount(app: AppConfig): Promise<void>
  /** 销毁应用 */
  unload(app: AppConfig): Promise<void>
}

/**
 * 任务调度器接口
 */
export interface ITaskScheduler {
  /** 添加任务 */
  addTask(task: ScheduledTask): void
  /** 移除任务 */
  removeTask(taskId: string): void
  /** 执行任务 */
  executeTask(taskId: string): Promise<void>
  /** 清空任务队列 */
  clearTasks(): void
  /** 获取任务状态 */
  getTaskStatus(taskId: string): TaskStatus | undefined
}

/**
 * 调度任务
 */
export interface ScheduledTask {
  /** 任务ID */
  id: string
  /** 任务名称 */
  name: string
  /** 任务类型 */
  type: 'bootstrap' | 'mount' | 'unmount' | 'unload' | 'custom'
  /** 任务优先级 */
  priority: number
  /** 任务执行函数 */
  execute: () => Promise<void>
  /** 任务依赖 */
  dependencies?: string[]
  /** 任务超时时间 */
  timeout?: number
  /** 任务重试次数 */
  retries?: number
  /** 任务创建时间 */
  createdAt: number
}

/**
 * 任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'

/**
 * 应用注册表接口
 */
export interface IApplicationRegistry {
  /** 注册应用 */
  register(config: AppConfig): void
  /** 注销应用 */
  unregister(name: string): void
  /** 获取应用 */
  getApp(name: string): AppConfig | undefined
  /** 获取所有应用 */
  getAllApps(): AppConfig[]
  /** 获取活跃应用 */
  getActiveApps(): AppConfig[]
  /** 检查应用是否存在 */
  hasApp(name: string): boolean
}

/**
 * 资源加载器接口
 */
export interface IResourceLoader {
  /** 加载脚本 */
  loadScript(url: string, options?: LoadOptions): Promise<void>
  /** 加载样式 */
  loadStyle(url: string, options?: LoadOptions): Promise<void>
  /** 加载模块 */
  loadModule(url: string, options?: LoadOptions): Promise<any>
  /** 预加载资源 */
  prefetch(urls: string[]): Promise<void>
  /** 清理资源 */
  cleanup(urls?: string[]): void
}

/**
 * 资源加载选项
 */
export interface LoadOptions {
  /** 是否缓存 */
  cache?: boolean
  /** 超时时间 */
  timeout?: number
  /** 重试次数 */
  retries?: number
  /** 跨域设置 */
  crossOrigin?: 'anonymous' | 'use-credentials'
  /** 完整性校验 */
  integrity?: string
  /** 自定义属性 */
  attributes?: Record<string, string>
}

/**
 * 微内核实例接口
 */
export interface IMicroKernel {
  /** 内核选项 */
  readonly options: MicroKernelOptions
  /** 生命周期调度器 */
  readonly lifecycleScheduler: ILifecycleScheduler
  /** 任务调度器 */
  readonly taskScheduler: ITaskScheduler
  /** 应用注册表 */
  readonly applicationRegistry: IApplicationRegistry
  /** 资源加载器 */
  readonly resourceLoader: IResourceLoader
  
  /** 启动内核 */
  start(): Promise<void>
  /** 停止内核 */
  stop(): Promise<void>
  /** 重启内核 */
  restart(): Promise<void>
  /** 获取内核状态 */
  getStatus(): KernelStatus
}

/**
 * 内核状态
 */
export type KernelStatus = 'idle' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error'