import type { MicroApp } from '../types/application'
import { AppStatus } from '../types/application'
import { MicroCoreError } from '@micro-core/shared'

/**
 * 生命周期调度器
 * 负责管理微应用的生命周期状态转换
 */
export class LifecycleScheduler {
  private readonly hooks = new Map<string, Array<(app: MicroApp) => void | Promise<void>>>()

  /**
   * 注册生命周期钩子
   */
  registerHook(
    lifecycle: string,
    hook: (app: MicroApp) => void | Promise<void>
  ): void {
    if (!this.hooks.has(lifecycle)) {
      this.hooks.set(lifecycle, [])
    }
    this.hooks.get(lifecycle)!.push(hook)
  }
  
  /**
   * 执行生命周期钩子
   */
  private async executeHooks(lifecycle: string, app: MicroApp): Promise<void> {
    const hooks = this.hooks.get(lifecycle) || []
    
    for (const hook of hooks) {
      try {
        await hook(app)
      } catch (error) {
        throw new MicroCoreError(
          `Failed to execute ${lifecycle} hook for app ${app.name}`,
          'LIFECYCLE_HOOK_ERROR',
          { app: app.name, lifecycle, error }
        )
      }
    }
  }
  
  /**
   * 启动应用
   */
  async bootstrap(app: MicroApp): Promise<void> {
    if (app.status !== 'loaded') {
      throw new MicroCoreError(
        `Cannot bootstrap app ${app.name} in status ${app.status}`,
        'INVALID_LIFECYCLE_STATUS',
        { app: app.name, status: app.status, expected: 'loaded' }
      )
    }
    
    try {
      await this.executeHooks('beforeBootstrap', app)
      
      // 更新状态为启动中
      this.updateStatus(app, AppStatus.BOOTSTRAPPING)

      // 执行应用的启动逻辑
      if (app.bootstrap) {
        await app.bootstrap()
      }

      // 更新状态为已启动
      this.updateStatus(app, AppStatus.BOOTSTRAPPED)
      
      await this.executeHooks('afterBootstrap', app)
    } catch (error) {
      this.updateStatus(app, AppStatus.ERROR)
      throw new MicroCoreError(
        `Failed to bootstrap app ${app.name}`,
        'BOOTSTRAP_ERROR'
      )
    }
  }
  
  /**
   * 挂载应用
   */
  async mount(app: MicroApp): Promise<void> {
    if (app.status !== 'bootstrapped' && app.status !== 'unmounted') {
      throw new MicroCoreError(
        `Cannot mount app ${app.name} in status ${app.status}`,
        'INVALID_LIFECYCLE_STATUS',
        { app: app.name, status: app.status, expected: ['bootstrapped', 'unmounted'] }
      )
    }
    
    try {
      await this.executeHooks('beforeMount', app)
      
      // 更新状态为挂载中
      this.updateStatus(app, AppStatus.MOUNTING)

      // 执行应用的挂载逻辑
      if (app.mount) {
        await app.mount()
      }

      // 更新状态为已挂载
      this.updateStatus(app, AppStatus.MOUNTED)

      await this.executeHooks('afterMount', app)
    } catch (error) {
      this.updateStatus(app, AppStatus.ERROR)
      throw new MicroCoreError(
        `Failed to mount app ${app.name}`,
        'MOUNT_ERROR'
      )
    }
  }
  
  /**
   * 卸载应用
   */
  async unmount(app: MicroApp): Promise<void> {
    if (app.status !== 'mounted') {
      throw new MicroCoreError(
        `Cannot unmount app ${app.name} in status ${app.status}`,
        'INVALID_LIFECYCLE_STATUS',
        { app: app.name, status: app.status, expected: 'mounted' }
      )
    }
    
    try {
      await this.executeHooks('beforeUnmount', app)
      
      // 更新状态为卸载中
      this.updateStatus(app, 'unmounting')
      
      // 执行应用的卸载逻辑
      await app.unmount()
      
      // 更新状态为已卸载
      this.updateStatus(app, 'unmounted')
      
      await this.executeHooks('afterUnmount', app)
    } catch (error) {
      this.updateStatus(app, 'error')
      throw new MicroCoreError(
        `Failed to unmount app ${app.name}`,
        'UNMOUNT_ERROR',
        { app: app.name, error }
      )
    }
  }
  
  /**
   * 卸载应用
   */
  async unload(app: MicroApp): Promise<void> {
    if (app.status === 'mounted') {
      await this.unmount(app)
    }
    
    try {
      await this.executeHooks('beforeUnload', app)
      
      // 执行应用的卸载逻辑
      await app.unload()
      
      // 更新状态为未注册
      this.updateStatus(app, 'not_registered')
      
      await this.executeHooks('afterUnload', app)
    } catch (error) {
      this.updateStatus(app, 'error')
      throw new MicroCoreError(
        `Failed to unload app ${app.name}`,
        'UNLOAD_ERROR',
        { app: app.name, error }
      )
    }
  }
  
  /**
   * 更新应用状态
   */
  private updateStatus(app: MicroApp, status: AppStatus): void {
    // 通过类型断言更新只读属性
    ;(app as any).status = status
    ;(app as any).updatedAt = new Date()
  }
  
  /**
   * 检查状态转换是否有效
   */
  isValidTransition(from: ApplicationStatus, to: ApplicationStatus): boolean {
    const validTransitions: Record<ApplicationStatus, ApplicationStatus[]> = {
      [ApplicationStatus.NOT_REGISTERED]: [ApplicationStatus.REGISTERED],
      [ApplicationStatus.REGISTERED]: [ApplicationStatus.LOADING, ApplicationStatus.NOT_REGISTERED],
      [ApplicationStatus.LOADING]: [ApplicationStatus.LOADED, ApplicationStatus.ERROR],
      [ApplicationStatus.LOADED]: [ApplicationStatus.BOOTSTRAPPING, ApplicationStatus.ERROR],
      [ApplicationStatus.BOOTSTRAPPING]: [ApplicationStatus.BOOTSTRAPPED, ApplicationStatus.ERROR],
      [ApplicationStatus.BOOTSTRAPPED]: [ApplicationStatus.MOUNTING, ApplicationStatus.ERROR],
      [ApplicationStatus.MOUNTING]: [ApplicationStatus.MOUNTED, ApplicationStatus.ERROR],
      [ApplicationStatus.MOUNTED]: [ApplicationStatus.UNMOUNTING, ApplicationStatus.ERROR],
      [ApplicationStatus.UNMOUNTING]: [ApplicationStatus.UNMOUNTED, ApplicationStatus.ERROR],
      [ApplicationStatus.UNMOUNTED]: [ApplicationStatus.MOUNTING, ApplicationStatus.NOT_REGISTERED],
      [ApplicationStatus.ERROR]: [
        ApplicationStatus.LOADING,
        ApplicationStatus.BOOTSTRAPPING,
        ApplicationStatus.MOUNTING,
        ApplicationStatus.UNMOUNTING,
        ApplicationStatus.NOT_REGISTERED
      ]
    }
    
    return validTransitions[from]?.includes(to) ?? false
  }
  
  /**
   * 获取下一个有效状态
   */
  getNextValidStates(status: ApplicationStatus): ApplicationStatus[] {
    const validTransitions: Record<ApplicationStatus, ApplicationStatus[]> = {
      [ApplicationStatus.NOT_REGISTERED]: [ApplicationStatus.REGISTERED],
      [ApplicationStatus.REGISTERED]: [ApplicationStatus.LOADING],
      [ApplicationStatus.LOADING]: [ApplicationStatus.LOADED],
      [ApplicationStatus.LOADED]: [ApplicationStatus.BOOTSTRAPPING],
      [ApplicationStatus.BOOTSTRAPPING]: [ApplicationStatus.BOOTSTRAPPED],
      [ApplicationStatus.BOOTSTRAPPED]: [ApplicationStatus.MOUNTING],
      [ApplicationStatus.MOUNTING]: [ApplicationStatus.MOUNTED],
      [ApplicationStatus.MOUNTED]: [ApplicationStatus.UNMOUNTING],
      [ApplicationStatus.UNMOUNTING]: [ApplicationStatus.UNMOUNTED],
      [ApplicationStatus.UNMOUNTED]: [ApplicationStatus.MOUNTING, ApplicationStatus.NOT_REGISTERED],
      [ApplicationStatus.ERROR]: []
    }
    
    return validTransitions[status] || []
  }
}