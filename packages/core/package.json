{"name": "@micro-core/core", "version": "0.1.0", "description": "微前端框架核心运行时，体积小于15KB的微内核实现", "type": "module", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "sideEffects": false, "scripts": {"dev": "vite build --watch", "build": "vite build", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "keywords": ["微前端", "micro-frontend", "微内核", "沙箱", "插件系统"], "author": {"name": "Echo", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://micro-core.dev", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/echo008/micro-core/issues"}, "dependencies": {"@micro-core/shared": "workspace:*"}, "devDependencies": {"rimraf": "^5.0.5", "typescript": "^5.3.3", "vite": "^7.0.6", "vite-plugin-dts": "^4.5.4", "vitest": "^3.2.4"}, "publishConfig": {"access": "public"}}